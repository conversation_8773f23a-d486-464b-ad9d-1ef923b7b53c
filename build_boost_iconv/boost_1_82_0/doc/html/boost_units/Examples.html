<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Examples</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../boost_units.html" title="Chapter 42. Boost.Units 1.1.0">
<link rel="prev" href="Quantities.html" title="Quantities">
<link rel="next" href="Utilities.html" title="Utilities">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="Quantities.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_units.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="Utilities.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="boost_units.Examples"></a><a class="link" href="Examples.html" title="Examples">Examples</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="Examples.html#boost_units.Examples.DimensionExample">Dimension Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.UnitExample">Unit Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.QuantityExample">Quantity Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.KitchenSinkExample">Kitchen Sink
      Example using SI units</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.ConversionExample">Conversion Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.UDTExample">User Defined Types</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.ComplexExample">Complex Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.PerformanceExample">Performance
      Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.RadarBeamHeightExample">Radar Beam
      Height</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.HeterogeneousUnitExample">Heterogeneous
      Unit Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.AbsoluteRelativeTemperatureExample">Absolute
      and Relative Temperature Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.RuntimeConversionFactorExample">Runtime
      Conversion Factor Example</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.UnitsWithNonbaseDimensions">Units
      with Non-base Dimensions</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.OutputForCompositeUnits">Output
      for Composite Units</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.autoscale">Automatically Scaled
      Units</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.ConversionFactor">Conversion Factor</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.RuntimeUnits">Runtime Units</a></span></dt>
<dt><span class="section"><a href="Examples.html#boost_units.Examples.lambda">Interoperability with Boost.Lambda</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.DimensionExample"></a><a class="link" href="Examples.html#boost_units.Examples.DimensionExample" title="Dimension Example">Dimension Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/dimension.cpp" target="_top">dimension.cpp</a>)
      </p>
<p>
        By using MPL metafunctions and the template specializations for operations
        on composite dimensions (defined in <code class="computeroutput"><a class="link" href="Reference.html#header.boost.units.dimension_hpp" title="Header &lt;boost/units/dimension.hpp&gt;">boost/units/dimension.hpp</a></code>)
        it is possible to perform compile time arithmetic according to the dimensional
        analysis rules described <a class="link" href="Dimensional_Analysis.html" title="Dimensional Analysis">above</a>
        to produce new composite dimensions :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">times</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">mass_dimension</span><span class="special">&gt;::</span><span class="identifier">type</span>   <span class="identifier">LM_type</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">divides</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">time_dimension</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">L_T_type</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">static_root</span><span class="special">&lt;</span>
    <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">divides</span><span class="special">&lt;</span><span class="identifier">energy_dimension</span><span class="special">,</span><span class="identifier">mass_dimension</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">,</span>
    <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;</span>
<span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">V_type</span><span class="special">;</span>
</pre>
<p>
      </p>
<p>
        outputting (with symbol demangling, implemented in <a href="../../../boost/units/detail/utility.hpp" target="_top">utility.hpp</a>)
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">length_dimension</span>  <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">length_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span>
<span class="identifier">mass_dimension</span>    <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">mass_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span>
<span class="identifier">time_dimension</span>    <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">time_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span>
<span class="identifier">energy_dimension</span>  <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">length_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">2l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">mass_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">time_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;-</span><span class="number">2l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="identifier">LM_type</span>      <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">length_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">mass_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="identifier">L_T_type</span>     <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">length_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">time_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;-</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="identifier">V_type</span>       <span class="special">=</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">length_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">list</span><span class="special">&lt;</span><span class="identifier">dim</span><span class="special">&lt;</span><span class="identifier">time_base_dimension</span><span class="special">,</span> <span class="identifier">static_rational</span><span class="special">&lt;-</span><span class="number">1l</span><span class="special">,</span> <span class="number">1l</span><span class="special">&gt;</span> <span class="special">&gt;,</span> <span class="identifier">dimensionless_type</span><span class="special">&gt;</span> <span class="special">&gt;</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.UnitExample"></a><a class="link" href="Examples.html#boost_units.Examples.UnitExample" title="Unit Example">Unit Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/unit.cpp" target="_top">unit.cpp</a>)
      </p>
<p>
        This example demonstrates the use of the simple but functional unit system
        implemented in <a href="../../../libs/units/example/test_system.hpp" target="_top">test_system.hpp</a>
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">const</span> <span class="identifier">length</span>                    <span class="identifier">L</span><span class="special">;</span>
<span class="keyword">const</span> <span class="identifier">mass</span>                      <span class="identifier">M</span><span class="special">;</span>
<span class="comment">// needs to be namespace-qualified because of global time definition</span>
<span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">test</span><span class="special">::</span><span class="identifier">time</span>  <span class="identifier">T</span><span class="special">;</span>
<span class="keyword">const</span> <span class="identifier">energy</span>                    <span class="identifier">E</span><span class="special">;</span>
</pre>
<p>
      </p>
<p>
        We can perform various algebraic operations on these units, resulting in
        the following output:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">L</span>             <span class="special">=</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>           <span class="special">=</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>           <span class="special">=</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">/</span><span class="identifier">L</span>           <span class="special">=</span> <span class="identifier">dimensionless</span>
<span class="identifier">meter</span><span class="special">*</span><span class="identifier">meter</span>   <span class="special">=</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">M</span><span class="special">*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">T</span><span class="special">)*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">T</span><span class="special">)</span> <span class="special">=</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="identifier">M</span><span class="special">*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">T</span><span class="special">)^</span><span class="number">2</span>     <span class="special">=</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>           <span class="special">=</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">L</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>       <span class="special">=</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="number">2</span><span class="identifier">vM</span>           <span class="special">=</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="special">(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span><span class="identifier">vM</span>       <span class="special">=</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">2</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.QuantityExample"></a><a class="link" href="Examples.html#boost_units.Examples.QuantityExample" title="Quantity Example">Quantity Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/quantity.cpp" target="_top">quantity.cpp</a>)
      </p>
<p>
        This example demonstrates how to use quantities of our toy unit system :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">L</span> <span class="special">=</span> <span class="number">2.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">;</span>                     <span class="comment">// quantity of length</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">energy</span><span class="special">&gt;</span> <span class="identifier">E</span> <span class="special">=</span> <span class="identifier">kilograms</span><span class="special">*</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">);</span>    <span class="comment">// quantity of energy</span>
</pre>
<p>
      </p>
<p>
        giving us the basic quantity functionality :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">L</span>                                 <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="number">4</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="number">0</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="number">4</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">/</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="number">1</span> <span class="identifier">dimensionless</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">meter</span>                           <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">kilograms</span><span class="special">*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">)*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">)</span> <span class="special">=</span> <span class="number">4</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="identifier">kilograms</span><span class="special">*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">)^</span><span class="number">2</span>           <span class="special">=</span> <span class="number">4</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>                               <span class="special">=</span> <span class="number">8</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">L</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>                           <span class="special">=</span> <span class="number">2.82843</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="number">2</span><span class="identifier">vL</span>                               <span class="special">=</span> <span class="number">1.41421</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="special">(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span><span class="identifier">vL</span>                           <span class="special">=</span> <span class="number">1.5874</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">2</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
</pre>
<p>
      </p>
<p>
        As a further demonstration of the flexibility of the system, we replace the
        <code class="computeroutput"><span class="keyword">double</span></code> value type with a <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span></code> value type (ignoring the question of
        the meaningfulness of complex lengths and energies) :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">,</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">L</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">3.0</span><span class="special">,</span><span class="number">4.0</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">energy</span><span class="special">,</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">E</span><span class="special">(</span><span class="identifier">kilograms</span><span class="special">*</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">));</span>
</pre>
<p>
      </p>
<p>
        and find that the code functions exactly as expected with no additional work,
        delegating operations to <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span></code> and performing the appropriate dimensional
        analysis :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">L</span>                                 <span class="special">=</span> <span class="special">(</span><span class="number">3</span><span class="special">,</span><span class="number">4</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="special">(</span><span class="number">6</span><span class="special">,</span><span class="number">8</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="special">(</span><span class="number">0</span><span class="special">,</span><span class="number">0</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="special">(-</span><span class="number">7</span><span class="special">,</span><span class="number">24</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">/</span><span class="identifier">L</span>                               <span class="special">=</span> <span class="special">(</span><span class="number">1</span><span class="special">,</span><span class="number">0</span><span class="special">)</span> <span class="identifier">dimensionless</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">meter</span>                           <span class="special">=</span> <span class="special">(</span><span class="number">3</span><span class="special">,</span><span class="number">4</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">kilograms</span><span class="special">*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">)*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">)</span> <span class="special">=</span> <span class="special">(-</span><span class="number">7</span><span class="special">,</span><span class="number">24</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="identifier">kilograms</span><span class="special">*(</span><span class="identifier">L</span><span class="special">/</span><span class="identifier">seconds</span><span class="special">)^</span><span class="number">2</span>           <span class="special">=</span> <span class="special">(-</span><span class="number">7</span><span class="special">,</span><span class="number">24</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>                               <span class="special">=</span> <span class="special">(-</span><span class="number">117</span><span class="special">,</span><span class="number">44</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">L</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>                           <span class="special">=</span> <span class="special">(</span><span class="number">2</span><span class="special">,</span><span class="number">11</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="number">2</span><span class="identifier">vL</span>                               <span class="special">=</span> <span class="special">(</span><span class="number">2</span><span class="special">,</span><span class="number">1</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="special">(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span><span class="identifier">vL</span>                           <span class="special">=</span> <span class="special">(</span><span class="number">2.38285</span><span class="special">,</span><span class="number">1.69466</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">2</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.KitchenSinkExample"></a><a class="link" href="Examples.html#boost_units.Examples.KitchenSinkExample" title="Kitchen Sink Example using SI units">Kitchen Sink
      Example using SI units</a>
</h3></div></div></div>
<div class="toc"><dl class="toc"><dt><span class="section"><a href="Examples.html#boost_units.Examples.KitchenSinkExample.UDT_Quantities">User-defined
        value types</a></span></dt></dl></div>
<p>
        (<a href="../../../libs/units/example/kitchen_sink.cpp" target="_top">kitchen_sink.cpp</a>)
      </p>
<p>
        This example provides a fairly extensive set of tests covering most of the
        <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
        functionality. It uses the SI unit system defined in <code class="computeroutput"><a class="link" href="Reference.html#header.boost.units.systems.si_hpp" title="Header &lt;boost/units/systems/si.hpp&gt;">boost/units/systems/si.hpp</a></code>.
      </p>
<p>
        If we define a few units and associated quantities,
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// scalar</span>
<span class="keyword">const</span> <span class="keyword">double</span>    <span class="identifier">s1</span> <span class="special">=</span> <span class="number">2</span><span class="special">;</span>

<span class="keyword">const</span> <span class="keyword">long</span>                  <span class="identifier">x1</span> <span class="special">=</span> <span class="number">2</span><span class="special">;</span>
<span class="keyword">const</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="number">4</span><span class="special">,</span><span class="number">3</span><span class="special">&gt;</span>  <span class="identifier">x2</span><span class="special">;</span>

<span class="comment">/// define some units</span>
<span class="identifier">force</span>       <span class="identifier">u1</span> <span class="special">=</span> <span class="identifier">newton</span><span class="special">;</span>
<span class="identifier">energy</span>      <span class="identifier">u2</span> <span class="special">=</span> <span class="identifier">joule</span><span class="special">;</span>

<span class="comment">/// define some quantities</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">force</span><span class="special">&gt;</span>      <span class="identifier">q1</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">u1</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">energy</span><span class="special">&gt;</span>     <span class="identifier">q2</span><span class="special">(</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">u2</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        the various algebraic operations between scalars, units, and quantities give
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">S1</span> <span class="special">:</span>    <span class="number">2</span>
<span class="identifier">X1</span> <span class="special">:</span>    <span class="number">2</span>
<span class="identifier">X2</span> <span class="special">:</span>    <span class="special">(</span><span class="number">4</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
<span class="identifier">U1</span> <span class="special">:</span>    <span class="identifier">N</span>
<span class="identifier">U2</span> <span class="special">:</span>    <span class="identifier">J</span>
<span class="identifier">Q1</span> <span class="special">:</span>    <span class="number">1</span> <span class="identifier">N</span>
<span class="identifier">Q2</span> <span class="special">:</span>    <span class="number">2</span> <span class="identifier">J</span>
</pre>
<p>
      </p>
<p>
        Scalar/unit operations :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">U1</span><span class="special">*</span><span class="identifier">S1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">N</span>
<span class="identifier">S1</span><span class="special">*</span><span class="identifier">U1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">N</span>
<span class="identifier">U1</span><span class="special">/</span><span class="identifier">S1</span> <span class="special">:</span> <span class="number">0.5</span> <span class="identifier">N</span>
<span class="identifier">S1</span><span class="special">/</span><span class="identifier">U1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">m</span><span class="special">^-</span><span class="number">1</span> <span class="identifier">kg</span><span class="special">^-</span><span class="number">1</span> <span class="identifier">s</span><span class="special">^</span><span class="number">2</span>
</pre>
<p>
      </p>
<p>
        Unit/unit operations and integral/rational powers of units :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">U1</span><span class="special">+</span><span class="identifier">U1</span> <span class="special">:</span> <span class="identifier">N</span>
<span class="identifier">U1</span><span class="special">-</span><span class="identifier">U1</span> <span class="special">:</span> <span class="identifier">N</span>
<span class="identifier">U1</span><span class="special">*</span><span class="identifier">U1</span> <span class="special">:</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">U1</span><span class="special">/</span><span class="identifier">U1</span> <span class="special">:</span> <span class="identifier">dimensionless</span>
<span class="identifier">U1</span><span class="special">*</span><span class="identifier">U2</span> <span class="special">:</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">U1</span><span class="special">/</span><span class="identifier">U2</span> <span class="special">:</span> <span class="identifier">m</span><span class="special">^-</span><span class="number">1</span>
<span class="identifier">U1</span><span class="special">^</span><span class="identifier">X</span>  <span class="special">:</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">X1vU1</span> <span class="special">:</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">1</span>
<span class="identifier">U1</span><span class="special">^</span><span class="identifier">X2</span> <span class="special">:</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">4</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">4</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="identifier">s</span><span class="special">^(-</span><span class="number">8</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
<span class="identifier">X2vU1</span> <span class="special">:</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">4</span><span class="special">)</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">4</span><span class="special">)</span> <span class="identifier">s</span><span class="special">^(-</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
</pre>
<p>
      </p>
<p>
        Scalar/quantity operations :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">Q1</span><span class="special">*</span><span class="identifier">S1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">N</span>
<span class="identifier">S1</span><span class="special">*</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">N</span>
<span class="identifier">Q1</span><span class="special">/</span><span class="identifier">S1</span> <span class="special">:</span> <span class="number">0.5</span> <span class="identifier">N</span>
<span class="identifier">S1</span><span class="special">/</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">m</span><span class="special">^-</span><span class="number">1</span> <span class="identifier">kg</span><span class="special">^-</span><span class="number">1</span> <span class="identifier">s</span><span class="special">^</span><span class="number">2</span>
</pre>
<p>
      </p>
<p>
        Unit/quantity operations :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">U1</span><span class="special">*</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">Q1</span><span class="special">*</span><span class="identifier">U1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">U1</span><span class="special">/</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">dimensionless</span>
<span class="identifier">Q1</span><span class="special">/</span><span class="identifier">U1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">dimensionless</span>
</pre>
<p>
      </p>
<p>
        Quantity/quantity operations and integral/rational powers of quantities :
      </p>
<p>
</p>
<pre class="programlisting"><span class="special">+</span><span class="identifier">Q1</span>   <span class="special">:</span> <span class="number">1</span> <span class="identifier">N</span>
<span class="special">-</span><span class="identifier">Q1</span>   <span class="special">:</span> <span class="special">-</span><span class="number">1</span> <span class="identifier">N</span>
<span class="identifier">Q1</span><span class="special">+</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">N</span>
<span class="identifier">Q1</span><span class="special">-</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">0</span> <span class="identifier">N</span>
<span class="identifier">Q1</span><span class="special">*</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">Q1</span><span class="special">/</span><span class="identifier">Q1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">dimensionless</span>
<span class="identifier">Q1</span><span class="special">*</span><span class="identifier">Q2</span> <span class="special">:</span> <span class="number">2</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">Q1</span><span class="special">/</span><span class="identifier">Q2</span> <span class="special">:</span> <span class="number">0.5</span> <span class="identifier">m</span><span class="special">^-</span><span class="number">1</span>
<span class="identifier">Q1</span><span class="special">^</span><span class="identifier">X1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span><span class="special">^</span><span class="number">2</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">4</span>
<span class="identifier">X1vQ1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">1</span>
<span class="identifier">Q1</span><span class="special">^</span><span class="identifier">X2</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">4</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">4</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="identifier">s</span><span class="special">^(-</span><span class="number">8</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
<span class="identifier">X2vQ1</span> <span class="special">:</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">4</span><span class="special">)</span> <span class="identifier">kg</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">4</span><span class="special">)</span> <span class="identifier">s</span><span class="special">^(-</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
</pre>
<p>
      </p>
<p>
        Logical comparison operators are also defined between quantities :
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// check comparison tests</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span>    <span class="identifier">l1</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">meter</span><span class="special">),</span>
                    <span class="identifier">l2</span><span class="special">(</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        giving
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">l1</span> <span class="special">==</span> <span class="identifier">l2</span>    <span class="keyword">false</span>
<span class="identifier">l1</span> <span class="special">!=</span> <span class="identifier">l2</span>    <span class="keyword">true</span>
<span class="identifier">l1</span> <span class="special">&lt;=</span> <span class="identifier">l2</span>    <span class="keyword">true</span>
<span class="identifier">l1</span> <span class="special">&lt;</span> <span class="identifier">l2</span>     <span class="keyword">true</span>
<span class="identifier">l1</span> <span class="special">&gt;=</span> <span class="identifier">l2</span>    <span class="keyword">false</span>
<span class="identifier">l1</span> <span class="special">&gt;</span> <span class="identifier">l2</span>     <span class="keyword">false</span>
</pre>
<p>
      </p>
<p>
        Implicit conversion is allowed between dimensionless quantities and their
        corresponding value types :
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// check implicit unit conversion from dimensionless to value_type  </span>
<span class="keyword">const</span> <span class="keyword">double</span>    <span class="identifier">dimless</span> <span class="special">=</span> <span class="special">(</span><span class="identifier">q1</span><span class="special">/</span><span class="identifier">q1</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        A generic function for computing mechanical work can be defined that takes
        force and distance arguments in an arbitrary unit system and returns energy
        in the same system:
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// the physical definition of work - computed for an arbitrary unit system </span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">System</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">energy_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">Y</span><span class="special">&gt;</span>
<span class="identifier">work</span><span class="special">(</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">force_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="identifier">F</span><span class="special">,</span>
     <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="identifier">dx</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">F</span><span class="special">*</span><span class="identifier">dx</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// test calcuation of work</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">force</span><span class="special">&gt;</span>       <span class="identifier">F</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">newton</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span>      <span class="identifier">dx</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">meter</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">energy</span><span class="special">&gt;</span>      <span class="identifier">E</span><span class="special">(</span><span class="identifier">work</span><span class="special">(</span><span class="identifier">F</span><span class="special">,</span><span class="identifier">dx</span><span class="special">));</span>
</pre>
<p>
      </p>
<p>
        which functions as expected for SI quantities :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">F</span>  <span class="special">=</span> <span class="number">1</span> <span class="identifier">N</span>
<span class="identifier">dx</span> <span class="special">=</span> <span class="number">1</span> <span class="identifier">m</span>
<span class="identifier">E</span>  <span class="special">=</span> <span class="number">1</span> <span class="identifier">J</span>
</pre>
<p>
      </p>
<p>
        The ideal gas law can also be implemented in SI units :
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// the ideal gas law in si units</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">amount</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span>
<span class="identifier">idealGasLaw</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">pressure</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">P</span><span class="special">,</span>
            <span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">volume</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">V</span><span class="special">,</span>
            <span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">T</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">si</span><span class="special">;</span>

    <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">constants</span><span class="special">::</span><span class="identifier">codata</span><span class="special">;</span>
    <span class="keyword">return</span> <span class="special">(</span><span class="identifier">P</span><span class="special">*</span><span class="identifier">V</span><span class="special">/(</span><span class="identifier">R</span><span class="special">*</span><span class="identifier">T</span><span class="special">));</span>
<span class="special">}</span>
</pre>
<p>
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// test ideal gas law</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">temperature</span><span class="special">&gt;</span>   <span class="identifier">T</span> <span class="special">=</span> <span class="special">(</span><span class="number">273.</span><span class="special">+</span><span class="number">37.</span><span class="special">)*</span><span class="identifier">kelvin</span><span class="special">;</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">pressure</span><span class="special">&gt;</span>      <span class="identifier">P</span> <span class="special">=</span> <span class="number">1.01325e5</span><span class="special">*</span><span class="identifier">pascals</span><span class="special">;</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span>        <span class="identifier">r</span> <span class="special">=</span> <span class="number">0.5e-6</span><span class="special">*</span><span class="identifier">meters</span><span class="special">;</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">volume</span><span class="special">&gt;</span>        <span class="identifier">V</span> <span class="special">=</span> <span class="special">(</span><span class="number">4.0</span><span class="special">/</span><span class="number">3.0</span><span class="special">)*</span><span class="number">3.141592</span><span class="special">*</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">3</span><span class="special">&gt;(</span><span class="identifier">r</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">amount</span><span class="special">&gt;</span>        <span class="identifier">n</span><span class="special">(</span><span class="identifier">idealGasLaw</span><span class="special">(</span><span class="identifier">P</span><span class="special">,</span><span class="identifier">V</span><span class="special">,</span><span class="identifier">T</span><span class="special">));</span>
</pre>
<p>
      </p>
<p>
        with the resulting output :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">r</span> <span class="special">=</span> <span class="number">5e-07</span> <span class="identifier">m</span>
<span class="identifier">P</span> <span class="special">=</span> <span class="number">101325</span> <span class="identifier">Pa</span>
<span class="identifier">V</span> <span class="special">=</span> <span class="number">5.23599e-19</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">T</span> <span class="special">=</span> <span class="number">310</span> <span class="identifier">K</span>
<span class="identifier">n</span> <span class="special">=</span> <span class="number">2.05835e-17</span> <span class="identifier">mol</span>
<span class="identifier">R</span> <span class="special">=</span> <span class="number">8.314472</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span> <span class="identifier">K</span><span class="special">^-</span><span class="number">1</span> <span class="identifier">mol</span><span class="special">^-</span><span class="number">1</span> <span class="special">(</span><span class="identifier">rel</span><span class="special">.</span> <span class="identifier">unc</span><span class="special">.</span> <span class="special">=</span> <span class="number">1.8e-06</span><span class="special">)</span>
</pre>
<p>
      </p>
<p>
        Trigonometric and inverse trigonometric functions can be implemented for
        any unit system that provides an angular base dimension. For radians, these
        functions are found in <code class="computeroutput"><a class="link" href="Reference.html#header.boost.units.cmath_hpp" title="Header &lt;boost/units/cmath.hpp&gt;">boost/units/cmath.hpp</a></code>
        These behave as one expects, with trigonometric functions taking an angular
        quantity and returning a dimensionless quantity, while the inverse trigonometric
        functions take a dimensionless quantity and return an angular quantity :
      </p>
<p>
        Defining a few angular quantities,
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// test trig stuff</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">plane_angle</span><span class="special">&gt;</span>           <span class="identifier">theta</span> <span class="special">=</span> <span class="number">0.375</span><span class="special">*</span><span class="identifier">radians</span><span class="special">;</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">dimensionless</span><span class="special">&gt;</span>         <span class="identifier">sin_theta</span> <span class="special">=</span> <span class="identifier">sin</span><span class="special">(</span><span class="identifier">theta</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">plane_angle</span><span class="special">&gt;</span>           <span class="identifier">thetap</span> <span class="special">=</span> <span class="identifier">asin</span><span class="special">(</span><span class="identifier">sin_theta</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        yields
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">theta</span>            <span class="special">=</span> <span class="number">0.375</span> <span class="identifier">rd</span>
<span class="identifier">sin</span><span class="special">(</span><span class="identifier">theta</span><span class="special">)</span>       <span class="special">=</span> <span class="number">0.366273</span> <span class="identifier">dimensionless</span>
<span class="identifier">asin</span><span class="special">(</span><span class="identifier">sin</span><span class="special">(</span><span class="identifier">theta</span><span class="special">))</span> <span class="special">=</span> <span class="number">0.375</span> <span class="identifier">rd</span>
</pre>
<p>
      </p>
<p>
        Dealing with complex quantities is trivial. Here is the calculation of complex
        impedance :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">electric_potential</span><span class="special">,</span><span class="identifier">complex_type</span><span class="special">&gt;</span> <span class="identifier">v</span> <span class="special">=</span> <span class="identifier">complex_type</span><span class="special">(</span><span class="number">12.5</span><span class="special">,</span><span class="number">0.0</span><span class="special">)*</span><span class="identifier">volts</span><span class="special">;</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">current</span><span class="special">,</span><span class="identifier">complex_type</span><span class="special">&gt;</span>            <span class="identifier">i</span> <span class="special">=</span> <span class="identifier">complex_type</span><span class="special">(</span><span class="number">3.0</span><span class="special">,</span><span class="number">4.0</span><span class="special">)*</span><span class="identifier">amperes</span><span class="special">;</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">resistance</span><span class="special">,</span><span class="identifier">complex_type</span><span class="special">&gt;</span>         <span class="identifier">z</span> <span class="special">=</span> <span class="identifier">complex_type</span><span class="special">(</span><span class="number">1.5</span><span class="special">,-</span><span class="number">2.0</span><span class="special">)*</span><span class="identifier">ohms</span><span class="special">;</span>
</pre>
<p>
      </p>
<p>
        giving
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">V</span>   <span class="special">=</span> <span class="special">(</span><span class="number">12.5</span><span class="special">,</span><span class="number">0</span><span class="special">)</span> <span class="identifier">V</span>
<span class="identifier">I</span>   <span class="special">=</span> <span class="special">(</span><span class="number">3</span><span class="special">,</span><span class="number">4</span><span class="special">)</span> <span class="identifier">A</span>
<span class="identifier">Z</span>   <span class="special">=</span> <span class="special">(</span><span class="number">1.5</span><span class="special">,-</span><span class="number">2</span><span class="special">)</span> <span class="identifier">Ohm</span>
<span class="identifier">I</span><span class="special">*</span><span class="identifier">Z</span> <span class="special">=</span> <span class="special">(</span><span class="number">12.5</span><span class="special">,</span><span class="number">0</span><span class="special">)</span> <span class="identifier">V</span>
</pre>
<p>
      </p>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="boost_units.Examples.KitchenSinkExample.UDT_Quantities"></a><a class="link" href="Examples.html#boost_units.Examples.KitchenSinkExample.UDT_Quantities" title="User-defined value types">User-defined
        value types</a>
</h4></div></div></div>
<p>
          User-defined value types that support the appropriate arithmetic operations
          are automatically supported as quantity value types. The operators that
          are supported by default for quantity value types are unary plus, unary
          minus, addition, subtraction, multiplication, division, equal-to, not-equal-to,
          less-than, less-or-equal-to, greater-than, and greater-or-equal-to. Support
          for rational powers and roots can be added by overloading the <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/power_typeof_helper.html" title="Struct template power_typeof_helper">power_typeof_helper</a></code></span>
          and <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/root_typeof_helper.html" title="Struct template root_typeof_helper">root_typeof_helper</a></code></span>
          classes. Here we implement a user-defined <code class="computeroutput"><span class="identifier">measurement</span></code>
          class that models a numerical measurement with an associated measurement
          error and the appropriate algebra and demonstrates its use as a quantity
          value type; the full code is found in <a href="../../../libs/units/example/measurement.hpp" target="_top">measurement.hpp</a>.
        </p>
<p>
          Then, defining some <code class="computeroutput"><span class="identifier">measurement</span></code>
          <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
          variables
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">,</span><span class="identifier">measurement</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="special">&gt;</span>
    <span class="identifier">u</span><span class="special">(</span><span class="identifier">measurement</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">1.0</span><span class="special">,</span><span class="number">0.0</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">),</span>
    <span class="identifier">w</span><span class="special">(</span><span class="identifier">measurement</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">4.52</span><span class="special">,</span><span class="number">0.02</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">),</span>
    <span class="identifier">x</span><span class="special">(</span><span class="identifier">measurement</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">2.0</span><span class="special">,</span><span class="number">0.2</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">),</span>
    <span class="identifier">y</span><span class="special">(</span><span class="identifier">measurement</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">3.0</span><span class="special">,</span><span class="number">0.6</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">);</span>
</pre>
<p>
        </p>
<p>
          gives
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">x</span><span class="special">+</span><span class="identifier">y</span><span class="special">-</span><span class="identifier">w</span>         <span class="special">=</span> <span class="number">0.48</span><span class="special">(+/-</span><span class="number">0.632772</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">w</span><span class="special">*</span><span class="identifier">x</span>           <span class="special">=</span> <span class="number">9.04</span><span class="special">(+/-</span><span class="number">0.904885</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">x</span><span class="special">/</span><span class="identifier">y</span>           <span class="special">=</span> <span class="number">0.666667</span><span class="special">(+/-</span><span class="number">0.149071</span><span class="special">)</span> <span class="identifier">dimensionless</span>
</pre>
<p>
        </p>
<p>
          If we implement the overloaded helper classes for rational powers and roots
          then we can also compute rational powers of measurement quantities :
        </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">w</span><span class="special">*</span><span class="identifier">y</span><span class="special">^</span><span class="number">2</span><span class="special">/(</span><span class="identifier">u</span><span class="special">*</span><span class="identifier">x</span><span class="special">)^</span><span class="number">2</span> <span class="special">=</span> <span class="number">10.17</span><span class="special">(+/-</span><span class="number">3.52328</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^-</span><span class="number">1</span>
<span class="identifier">w</span><span class="special">/(</span><span class="identifier">u</span><span class="special">*</span><span class="identifier">x</span><span class="special">)^(</span><span class="number">1</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="special">=</span> <span class="number">3.19612</span><span class="special">(+/-</span><span class="number">0.160431</span><span class="special">)</span> <span class="identifier">dimensionless</span>
</pre>
<p>
        </p>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.ConversionExample"></a><a class="link" href="Examples.html#boost_units.Examples.ConversionExample" title="Conversion Example">Conversion Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/conversion.cpp" target="_top">conversion.cpp</a>)
      </p>
<p>
        This example demonstrates the various allowed conversions between SI and
        CGS units. Defining some quantities
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>     <span class="identifier">L1</span> <span class="special">=</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="keyword">int</span><span class="special">(</span><span class="number">2.5</span><span class="special">)*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meters</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">L2</span><span class="special">(</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">,</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">2.5</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meters</span><span class="special">));</span>
</pre>
<p>
      </p>
<p>
        illustrates implicit conversion of quantities of different value types where
        implicit conversion of the value types themselves is allowed. N.B. The conversion
        from double to int is treated as an explicit conversion because there is
        no way to emulate the exact behavior of the built-in conversion. Explicit
        constructors allow conversions for two cases:
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            explicit casting of a <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
            to a different <code class="computeroutput"><span class="identifier">value_type</span></code>
            :
          </li></ul></div>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="identifier">L3</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">,</span><span class="keyword">int</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">L1</span><span class="special">);</span>
</pre>
<p>
      </p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem">
            and explicit casting of a <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
            to a different unit :
          </li></ul></div>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>    <span class="identifier">L4</span> <span class="special">=</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">L1</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        giving the following output :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">L1</span> <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span>
<span class="identifier">L2</span> <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span>
<span class="identifier">L3</span> <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span>
<span class="identifier">L4</span> <span class="special">=</span> <span class="number">200</span> <span class="identifier">cm</span>
<span class="identifier">L5</span> <span class="special">=</span> <span class="number">5</span> <span class="identifier">m</span>
<span class="identifier">L6</span> <span class="special">=</span> <span class="number">4</span> <span class="identifier">m</span>
<span class="identifier">L7</span> <span class="special">=</span> <span class="number">200</span> <span class="identifier">cm</span>
</pre>
<p>
      </p>
<p>
        A few more explicit unit system conversions :
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">volume</span><span class="special">&gt;</span>    <span class="identifier">vs</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">3</span><span class="special">&gt;(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">));</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">volume</span><span class="special">&gt;</span>   <span class="identifier">vc</span><span class="special">(</span><span class="identifier">vs</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">volume</span><span class="special">&gt;</span>    <span class="identifier">vs2</span><span class="special">(</span><span class="identifier">vc</span><span class="special">);</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">energy</span><span class="special">&gt;</span>    <span class="identifier">es</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">joule</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">energy</span><span class="special">&gt;</span>   <span class="identifier">ec</span><span class="special">(</span><span class="identifier">es</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">energy</span><span class="special">&gt;</span>    <span class="identifier">es2</span><span class="special">(</span><span class="identifier">ec</span><span class="special">);</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">velocity</span><span class="special">&gt;</span>  <span class="identifier">v1</span> <span class="special">=</span> <span class="number">2.0</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meters</span><span class="special">/</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">,</span>
                        <span class="identifier">v2</span><span class="special">(</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">centimeters</span><span class="special">/</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">second</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        which produces the following output:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">volume</span> <span class="special">(</span><span class="identifier">m</span><span class="special">^</span><span class="number">3</span><span class="special">)</span>  <span class="special">=</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">volume</span> <span class="special">(</span><span class="identifier">cm</span><span class="special">^</span><span class="number">3</span><span class="special">)</span> <span class="special">=</span> <span class="number">1e+06</span> <span class="identifier">cm</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">volume</span> <span class="special">(</span><span class="identifier">m</span><span class="special">^</span><span class="number">3</span><span class="special">)</span>  <span class="special">=</span> <span class="number">1</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>

<span class="identifier">energy</span> <span class="special">(</span><span class="identifier">joules</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span> <span class="identifier">J</span>
<span class="identifier">energy</span> <span class="special">(</span><span class="identifier">ergs</span><span class="special">)</span>   <span class="special">=</span> <span class="number">1e+07</span> <span class="identifier">erg</span>
<span class="identifier">energy</span> <span class="special">(</span><span class="identifier">joules</span><span class="special">)</span> <span class="special">=</span> <span class="number">1</span> <span class="identifier">J</span>

<span class="identifier">velocity</span> <span class="special">(</span><span class="number">2</span> <span class="identifier">m</span><span class="special">/</span><span class="identifier">s</span><span class="special">)</span>  <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">1</span>
<span class="identifier">velocity</span> <span class="special">(</span><span class="number">2</span> <span class="identifier">cm</span><span class="special">/</span><span class="identifier">s</span><span class="special">)</span> <span class="special">=</span> <span class="number">0.02</span> <span class="identifier">m</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">1</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.UDTExample"></a><a class="link" href="Examples.html#boost_units.Examples.UDTExample" title="User Defined Types">User Defined Types</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/quaternion.cpp" target="_top">quaternion.cpp</a>)
      </p>
<p>
        This example demonstrates the use of <code class="computeroutput"><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span></code>
        as a value type for <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
        and the converse. For the first case, we first define specializations of
        <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/power_typeof_helper.html" title="Struct template power_typeof_helper">power_typeof_helper</a></code></span>
        and <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/root_typeof_helper.html" title="Struct template root_typeof_helper">root_typeof_helper</a></code></span>
        for powers and roots, respectively:
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// specialize power typeof helper</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="comment">// boost::math::quaternion only supports integer powers</span>
    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">(</span><span class="identifier">D</span><span class="special">==</span><span class="number">1</span><span class="special">);</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span>
        <span class="keyword">typename</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>
    <span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">return</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">pow</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">N</span><span class="special">));</span>
    <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// specialize root typeof helper</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="comment">// boost::math::quaternion only supports integer powers</span>
    <span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">(</span><span class="identifier">N</span><span class="special">==</span><span class="number">1</span><span class="special">);</span>

    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span>
        <span class="keyword">typename</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>
    <span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">return</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">pow</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">int</span><span class="special">&gt;(</span><span class="identifier">D</span><span class="special">));</span>
    <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
      </p>
<p>
        We can now declare a <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
        of a <code class="computeroutput"><span class="identifier">quaternion</span></code> :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">,</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="special">&gt;</span>     <span class="identifier">length_dimension</span><span class="special">;</span>

<span class="identifier">length_dimension</span>    <span class="identifier">L</span><span class="special">(</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">4.0</span><span class="special">,</span><span class="number">3.0</span><span class="special">,</span><span class="number">2.0</span><span class="special">,</span><span class="number">1.0</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        so that all operations that are defined in the <code class="computeroutput"><span class="identifier">quaternion</span></code>
        class behave correctly. If rational powers were defined for this class, it
        would be possible to compute rational powers and roots with no additional
        changes.
      </p>
<p>
</p>
<pre class="programlisting"><span class="special">+</span><span class="identifier">L</span>      <span class="special">=</span> <span class="special">(</span><span class="number">4</span><span class="special">,</span><span class="number">3</span><span class="special">,</span><span class="number">2</span><span class="special">,</span><span class="number">1</span><span class="special">)</span> <span class="identifier">m</span>
<span class="special">-</span><span class="identifier">L</span>      <span class="special">=</span> <span class="special">(-</span><span class="number">4</span><span class="special">,-</span><span class="number">3</span><span class="special">,-</span><span class="number">2</span><span class="special">,-</span><span class="number">1</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>     <span class="special">=</span> <span class="special">(</span><span class="number">8</span><span class="special">,</span><span class="number">6</span><span class="special">,</span><span class="number">4</span><span class="special">,</span><span class="number">2</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>     <span class="special">=</span> <span class="special">(</span><span class="number">0</span><span class="special">,</span><span class="number">0</span><span class="special">,</span><span class="number">0</span><span class="special">,</span><span class="number">0</span><span class="special">)</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">L</span>     <span class="special">=</span> <span class="special">(</span><span class="number">2</span><span class="special">,</span><span class="number">24</span><span class="special">,</span><span class="number">16</span><span class="special">,</span><span class="number">8</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">/</span><span class="identifier">L</span>     <span class="special">=</span> <span class="special">(</span><span class="number">1</span><span class="special">,</span><span class="number">0</span><span class="special">,</span><span class="number">0</span><span class="special">,</span><span class="number">0</span><span class="special">)</span> <span class="identifier">dimensionless</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>     <span class="special">=</span> <span class="special">(-</span><span class="number">104</span><span class="special">,</span><span class="number">102</span><span class="special">,</span><span class="number">68</span><span class="special">,</span><span class="number">34</span><span class="special">)</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
</pre>
<p>
      </p>
<p>
        Now, if for some reason we preferred the <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span> to be the value
        type of the <code class="computeroutput"><span class="identifier">quaternion</span></code> class
        we would have :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">&gt;</span>     <span class="identifier">length_dimension</span><span class="special">;</span>

<span class="identifier">length_dimension</span>    <span class="identifier">L</span><span class="special">(</span><span class="number">4.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">,</span><span class="number">3.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">,</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">,</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        Here, the unary plus and minus and addition and subtraction operators function
        correctly. Unfortunately, the multiplication and division operations fail
        because <code class="computeroutput"><span class="identifier">quaternion</span></code> implements
        them in terms of the <code class="computeroutput"><span class="special">*=</span></code> and
        <code class="computeroutput"><span class="special">/=</span></code> operators, respectively,
        which are incapable of representing the heterogeneous unit algebra needed
        for quantities (an identical problem occurs with <code class="computeroutput"><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span></code>,
        for the same reason). In order to compute rational powers and roots, we need
        to specialize <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/power_typeof_helper.html" title="Struct template power_typeof_helper">power_typeof_helper</a></code></span>
        and <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/root_typeof_helper.html" title="Struct template root_typeof_helper">root_typeof_helper</a></code></span>
        as follows:
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// specialize power typeof helper for quaternion&lt;quantity&lt;Unit,Y&gt; &gt;</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Unit</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;,</span>
    <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span>
        <span class="identifier">Y</span><span class="special">,</span>
        <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span>
    <span class="special">&gt;::</span><span class="identifier">type</span>     <span class="identifier">value_type</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span>
        <span class="identifier">Unit</span><span class="special">,</span>
        <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span>
    <span class="special">&gt;::</span><span class="identifier">type</span>  <span class="identifier">unit_type</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit_type</span><span class="special">,</span><span class="identifier">value_type</span><span class="special">&gt;</span>         <span class="identifier">quantity_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity_type</span><span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">value_type</span><span class="special">&gt;</span>   <span class="identifier">tmp</span> <span class="special">=</span>
            <span class="identifier">pow</span><span class="special">&lt;</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;(</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_1</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_2</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_3</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_4</span><span class="special">().</span><span class="identifier">value</span><span class="special">()));</span>

        <span class="keyword">return</span> <span class="identifier">type</span><span class="special">(</span><span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_1</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_2</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_3</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_4</span><span class="special">()));</span>
    <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">/// specialize root typeof helper for quaternion&lt;quantity&lt;Unit,Y&gt; &gt;</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Unit</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;,</span>
    <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span>
        <span class="identifier">Y</span><span class="special">,</span>
        <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span>
    <span class="special">&gt;::</span><span class="identifier">type</span>      <span class="identifier">value_type</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span>
        <span class="identifier">Unit</span><span class="special">,</span>
        <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span>
    <span class="special">&gt;::</span><span class="identifier">type</span>   <span class="identifier">unit_type</span><span class="special">;</span>

    <span class="keyword">typedef</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit_type</span><span class="special">,</span><span class="identifier">value_type</span><span class="special">&gt;</span>         <span class="identifier">quantity_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity_type</span><span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">const</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">value_type</span><span class="special">&gt;</span>   <span class="identifier">tmp</span> <span class="special">=</span>
            <span class="identifier">root</span><span class="special">&lt;</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">math</span><span class="special">::</span><span class="identifier">quaternion</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;(</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_1</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_2</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_3</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                <span class="identifier">x</span><span class="special">.</span><span class="identifier">R_component_4</span><span class="special">().</span><span class="identifier">value</span><span class="special">()));</span>

        <span class="keyword">return</span> <span class="identifier">type</span><span class="special">(</span><span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_1</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_2</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_3</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">R_component_4</span><span class="special">()));</span>
    <span class="special">}</span>
<span class="special">};</span>
</pre>
<p>
      </p>
<p>
        giving:
      </p>
<p>
</p>
<pre class="programlisting"><span class="special">+</span><span class="identifier">L</span>      <span class="special">=</span> <span class="special">(</span><span class="number">4</span> <span class="identifier">m</span><span class="special">,</span><span class="number">3</span> <span class="identifier">m</span><span class="special">,</span><span class="number">2</span> <span class="identifier">m</span><span class="special">,</span><span class="number">1</span> <span class="identifier">m</span><span class="special">)</span>
<span class="special">-</span><span class="identifier">L</span>      <span class="special">=</span> <span class="special">(-</span><span class="number">4</span> <span class="identifier">m</span><span class="special">,-</span><span class="number">3</span> <span class="identifier">m</span><span class="special">,-</span><span class="number">2</span> <span class="identifier">m</span><span class="special">,-</span><span class="number">1</span> <span class="identifier">m</span><span class="special">)</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>     <span class="special">=</span> <span class="special">(</span><span class="number">8</span> <span class="identifier">m</span><span class="special">,</span><span class="number">6</span> <span class="identifier">m</span><span class="special">,</span><span class="number">4</span> <span class="identifier">m</span><span class="special">,</span><span class="number">2</span> <span class="identifier">m</span><span class="special">)</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>     <span class="special">=</span> <span class="special">(</span><span class="number">0</span> <span class="identifier">m</span><span class="special">,</span><span class="number">0</span> <span class="identifier">m</span><span class="special">,</span><span class="number">0</span> <span class="identifier">m</span><span class="special">,</span><span class="number">0</span> <span class="identifier">m</span><span class="special">)</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>     <span class="special">=</span> <span class="special">(-</span><span class="number">104</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span><span class="special">,</span><span class="number">102</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span><span class="special">,</span><span class="number">68</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span><span class="special">,</span><span class="number">34</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span><span class="special">)</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.ComplexExample"></a><a class="link" href="Examples.html#boost_units.Examples.ComplexExample" title="Complex Example">Complex Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/complex.cpp" target="_top">complex.cpp</a>)
      </p>
<p>
        This example demonstrates how to implement a replacement <code class="computeroutput"><span class="identifier">complex</span></code>
        class that functions correctly both as a quantity value type and as a quantity
        container class, including heterogeneous multiplication and division operations
        and rational powers and roots. Naturally, heterogeneous operations are only
        supported on compilers that implement <code class="computeroutput"><span class="identifier">typeof</span></code>.
        The primary differences are that binary operations are not implemented using
        the <code class="computeroutput"><span class="identifier">op</span><span class="special">=</span></code>
        operators and use the utility classes <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/add_typeof_helper.html" title="Struct template add_typeof_helper">add_typeof_helper</a></code></span>,
        <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/subtract_typeof_helper.html" title="Struct template subtract_typeof_helper">subtract_typeof_helper</a></code></span>,
        <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/multiply_typeof_helper.html" title="Struct template multiply_typeof_helper">multiply_typeof_helper</a></code></span>,
        and <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/divide_typeof_helper.html" title="Struct template divide_typeof_helper">divide_typeof_helper</a></code></span>.
        In addition, <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/power_typeof_helper.html" title="Struct template power_typeof_helper">power_typeof_helper</a></code></span>
        and <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/root_typeof_helper.html" title="Struct template root_typeof_helper">root_typeof_helper</a></code></span>
        are defined for both cases :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">units</span> <span class="special">{</span>

<span class="comment">/// replacement complex class </span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">class</span> <span class="identifier">complex</span>
<span class="special">{</span>
    <span class="keyword">public</span><span class="special">:</span>
        <span class="keyword">typedef</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">T</span><span class="special">&gt;</span>  <span class="identifier">this_type</span><span class="special">;</span>

        <span class="keyword">constexpr</span> <span class="identifier">complex</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">r</span> <span class="special">=</span> <span class="number">0</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">i</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="special">:</span> <span class="identifier">r_</span><span class="special">(</span><span class="identifier">r</span><span class="special">),</span><span class="identifier">i_</span><span class="special">(</span><span class="identifier">i</span><span class="special">)</span> <span class="special">{</span> <span class="special">}</span>
        <span class="keyword">constexpr</span> <span class="identifier">complex</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="identifier">source</span><span class="special">)</span> <span class="special">:</span> <span class="identifier">r_</span><span class="special">(</span><span class="identifier">source</span><span class="special">.</span><span class="identifier">r_</span><span class="special">),</span><span class="identifier">i_</span><span class="special">(</span><span class="identifier">source</span><span class="special">.</span><span class="identifier">i_</span><span class="special">)</span> <span class="special">{</span> <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">=(</span><span class="keyword">const</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="identifier">source</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="keyword">if</span> <span class="special">(</span><span class="keyword">this</span> <span class="special">==</span> <span class="special">&amp;</span><span class="identifier">source</span><span class="special">)</span> <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>

            <span class="identifier">r_</span> <span class="special">=</span> <span class="identifier">source</span><span class="special">.</span><span class="identifier">r_</span><span class="special">;</span>
            <span class="identifier">i_</span> <span class="special">=</span> <span class="identifier">source</span><span class="special">.</span><span class="identifier">i_</span><span class="special">;</span>

            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">real</span><span class="special">()</span>             <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">r_</span><span class="special">;</span> <span class="special">}</span>
        <span class="keyword">constexpr</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">imag</span><span class="special">()</span>             <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">i_</span><span class="special">;</span> <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">real</span><span class="special">()</span> <span class="keyword">const</span>       <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">r_</span><span class="special">;</span> <span class="special">}</span>
        <span class="keyword">constexpr</span> <span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">imag</span><span class="special">()</span> <span class="keyword">const</span>       <span class="special">{</span> <span class="keyword">return</span> <span class="identifier">i_</span><span class="special">;</span> <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">+=(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">r_</span> <span class="special">+=</span> <span class="identifier">val</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">-=(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">r_</span> <span class="special">-=</span> <span class="identifier">val</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">*=(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">r_</span> <span class="special">*=</span> <span class="identifier">val</span><span class="special">;</span>
            <span class="identifier">i_</span> <span class="special">*=</span> <span class="identifier">val</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">/=(</span><span class="keyword">const</span> <span class="identifier">T</span><span class="special">&amp;</span> <span class="identifier">val</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">r_</span> <span class="special">/=</span> <span class="identifier">val</span><span class="special">;</span>
            <span class="identifier">i_</span> <span class="special">/=</span> <span class="identifier">val</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">+=(</span><span class="keyword">const</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="identifier">source</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">r_</span> <span class="special">+=</span> <span class="identifier">source</span><span class="special">.</span><span class="identifier">r_</span><span class="special">;</span>
            <span class="identifier">i_</span> <span class="special">+=</span> <span class="identifier">source</span><span class="special">.</span><span class="identifier">i_</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">-=(</span><span class="keyword">const</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="identifier">source</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">r_</span> <span class="special">-=</span> <span class="identifier">source</span><span class="special">.</span><span class="identifier">r_</span><span class="special">;</span>
            <span class="identifier">i_</span> <span class="special">-=</span> <span class="identifier">source</span><span class="special">.</span><span class="identifier">i_</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">*=(</span><span class="keyword">const</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="identifier">source</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="special">*</span><span class="keyword">this</span> <span class="special">=</span> <span class="special">*</span><span class="keyword">this</span> <span class="special">*</span> <span class="identifier">source</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

        <span class="keyword">constexpr</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">/=(</span><span class="keyword">const</span> <span class="identifier">this_type</span><span class="special">&amp;</span> <span class="identifier">source</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="special">*</span><span class="keyword">this</span> <span class="special">=</span> <span class="special">*</span><span class="keyword">this</span> <span class="special">/</span> <span class="identifier">source</span><span class="special">;</span>
            <span class="keyword">return</span> <span class="special">*</span><span class="keyword">this</span><span class="special">;</span>
        <span class="special">}</span>

    <span class="keyword">private</span><span class="special">:</span>
        <span class="identifier">T</span>   <span class="identifier">r_</span><span class="special">,</span><span class="identifier">i_</span><span class="special">;</span>
<span class="special">};</span>

<span class="special">}</span>

<span class="special">}</span>

<span class="preprocessor">#if</span> <span class="identifier">BOOST_UNITS_HAS_BOOST_TYPEOF</span>

<span class="preprocessor">#include</span> <span class="identifier">BOOST_TYPEOF_INCREMENT_REGISTRATION_GROUP</span><span class="special">()</span>

<span class="identifier">BOOST_TYPEOF_REGISTER_TEMPLATE</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">complex</span><span class="special">,</span> <span class="number">1</span><span class="special">)</span>

<span class="preprocessor">#endif</span>

<span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>

<span class="keyword">namespace</span> <span class="identifier">units</span> <span class="special">{</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">X</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">unary_plus_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="keyword">operator</span><span class="special">+(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">unary_plus_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;::</span><span class="identifier">type</span>  <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">&gt;(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">(),</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">());</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">X</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">unary_minus_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">unary_minus_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">&gt;(-</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">(),-</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">());</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">X</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">add_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="keyword">operator</span><span class="special">+(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">add_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">&gt;(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">()+</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">(),</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()+</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">());</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">X</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">subtract_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="keyword">operator</span><span class="special">-(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">subtract_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">&gt;(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">()-</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">(),</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()-</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">());</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">X</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">multiply_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="keyword">operator</span><span class="special">*(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">multiply_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">&gt;(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()</span> <span class="special">-</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">(),</span>
                         <span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()</span> <span class="special">+</span> <span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">());</span>

<span class="comment">//  fully correct implementation has more complex return type</span>
<span class="comment">//</span>
<span class="comment">//    typedef typename boost::units::multiply_typeof_helper&lt;X,Y&gt;::type xy_type;</span>
<span class="comment">//    </span>
<span class="comment">//    typedef typename boost::units::add_typeof_helper&lt;</span>
<span class="comment">//      xy_type,xy_type&gt;::type         xy_plus_xy_type;</span>
<span class="comment">//    typedef typename</span>
<span class="comment">//        boost::units::subtract_typeof_helper&lt;xy_type,xy_type&gt;::type</span>
<span class="comment">//        xy_minus_xy_type;</span>
<span class="comment">//    </span>
<span class="comment">//    BOOST_STATIC_ASSERT((boost::is_same&lt;xy_plus_xy_type,</span>
<span class="comment">//                                       xy_minus_xy_type&gt;::value == true));</span>
<span class="comment">//    </span>
<span class="comment">//    return complex&lt;xy_plus_xy_type&gt;(x.real()*y.real()-x.imag()*y.imag(),</span>
<span class="comment">//                                    x.real()*y.imag()+x.imag()*y.real());</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">X</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">divide_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span><span class="special">&gt;</span>
<span class="keyword">operator</span><span class="special">/(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">y</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// naive implementation of complex division</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">divide_typeof_helper</span><span class="special">&lt;</span><span class="identifier">X</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">type</span><span class="special">&gt;((</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()+</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">())/</span>
                            <span class="special">(</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()+</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()),</span>
                         <span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()-</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">())/</span>
                            <span class="special">(</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">real</span><span class="special">()+</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()*</span><span class="identifier">y</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()));</span>

<span class="comment">//  fully correct implementation has more complex return type</span>
<span class="comment">//</span>
<span class="comment">//  typedef typename boost::units::multiply_typeof_helper&lt;X,Y&gt;::type xy_type;</span>
<span class="comment">//  typedef typename boost::units::multiply_typeof_helper&lt;Y,Y&gt;::type yy_type;</span>
<span class="comment">//</span>
<span class="comment">//  typedef typename boost::units::add_typeof_helper&lt;xy_type, xy_type&gt;::type</span>
<span class="comment">//      xy_plus_xy_type;</span>
<span class="comment">//  typedef typename boost::units::subtract_typeof_helper&lt;</span>
<span class="comment">//      xy_type,xy_type&gt;::type xy_minus_xy_type;</span>
<span class="comment">//</span>
<span class="comment">//  typedef typename boost::units::divide_typeof_helper&lt;</span>
<span class="comment">//      xy_plus_xy_type,yy_type&gt;::type      xy_plus_xy_over_yy_type;</span>
<span class="comment">//  typedef typename boost::units::divide_typeof_helper&lt;</span>
<span class="comment">//      xy_minus_xy_type,yy_type&gt;::type     xy_minus_xy_over_yy_type;</span>
<span class="comment">//</span>
<span class="comment">//  BOOST_STATIC_ASSERT((boost::is_same&lt;xy_plus_xy_over_yy_type,</span>
<span class="comment">//                                  xy_minus_xy_over_yy_type&gt;::value == true));</span>
<span class="comment">//</span>
<span class="comment">//  return complex&lt;xy_plus_xy_over_yy_type&gt;(</span>
<span class="comment">//      (x.real()*y.real()+x.imag()*y.imag())/</span>
<span class="comment">//          (y.real()*y.real()+y.imag()*y.imag()),</span>
<span class="comment">//      (x.imag()*y.real()-x.real()*y.imag())/</span>
<span class="comment">//          (y.real()*y.real()+y.imag()*y.imag()));</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;</span>
<span class="identifier">pow</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">Y</span><span class="special">&amp;</span> <span class="identifier">y</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="identifier">tmp</span><span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">(),</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">());</span>

    <span class="identifier">tmp</span> <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pow</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">,</span><span class="identifier">y</span><span class="special">);</span>

    <span class="keyword">return</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">real</span><span class="special">(),</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">imag</span><span class="special">());</span>
<span class="special">}</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">&gt;</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span><span class="special">&amp;</span> <span class="keyword">operator</span><span class="special">&lt;&lt;(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">ostream</span><span class="special">&amp;</span> <span class="identifier">os</span><span class="special">,</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">val</span><span class="special">)</span>
<span class="special">{</span>
    <span class="identifier">os</span> <span class="special">&lt;&lt;</span> <span class="identifier">val</span><span class="special">.</span><span class="identifier">real</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" + "</span> <span class="special">&lt;&lt;</span> <span class="identifier">val</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()</span> <span class="special">&lt;&lt;</span> <span class="string">" i"</span><span class="special">;</span>

    <span class="keyword">return</span> <span class="identifier">os</span><span class="special">;</span>
<span class="special">}</span>

<span class="comment">/// specialize power typeof helper for complex&lt;Y&gt;</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">complex</span><span class="special">&lt;</span>
        <span class="keyword">typename</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>
    <span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">const</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span>  <span class="identifier">rat</span><span class="special">;</span>

        <span class="keyword">const</span> <span class="identifier">Y</span>    <span class="identifier">m</span> <span class="special">=</span> <span class="identifier">Y</span><span class="special">(</span><span class="identifier">rat</span><span class="special">.</span><span class="identifier">numerator</span><span class="special">())/</span><span class="identifier">Y</span><span class="special">(</span><span class="identifier">rat</span><span class="special">.</span><span class="identifier">denominator</span><span class="special">());</span>

        <span class="keyword">return</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">pow</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="identifier">m</span><span class="special">);</span>
    <span class="special">}</span>
<span class="special">};</span>

<span class="comment">/// specialize root typeof helper for complex&lt;Y&gt;</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="identifier">complex</span><span class="special">&lt;</span>
        <span class="keyword">typename</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>
    <span class="special">&gt;</span> <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">const</span> <span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span>  <span class="identifier">rat</span><span class="special">;</span>

        <span class="keyword">const</span> <span class="identifier">Y</span>    <span class="identifier">m</span> <span class="special">=</span> <span class="identifier">Y</span><span class="special">(</span><span class="identifier">rat</span><span class="special">.</span><span class="identifier">denominator</span><span class="special">())/</span><span class="identifier">Y</span><span class="special">(</span><span class="identifier">rat</span><span class="special">.</span><span class="identifier">numerator</span><span class="special">());</span>

        <span class="keyword">return</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">pow</span><span class="special">(</span><span class="identifier">x</span><span class="special">,</span><span class="identifier">m</span><span class="special">);</span>
    <span class="special">}</span>
<span class="special">};</span>

<span class="comment">/// specialize power typeof helper for complex&lt;quantity&lt;Unit,Y&gt; &gt;</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Unit</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
        <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>       <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
        <span class="identifier">power_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">unit_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit_type</span><span class="special">,</span><span class="identifier">value_type</span><span class="special">&gt;</span>                      <span class="identifier">quantity_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity_type</span><span class="special">&gt;</span>                              <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">value_type</span><span class="special">&gt;</span>   <span class="identifier">tmp</span> <span class="special">=</span>
            <span class="identifier">pow</span><span class="special">&lt;</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                                                  <span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">().</span><span class="identifier">value</span><span class="special">()));</span>

        <span class="keyword">return</span> <span class="identifier">type</span><span class="special">(</span><span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">real</span><span class="special">()),</span>
                    <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()));</span>
    <span class="special">}</span>
<span class="special">};</span>

<span class="comment">/// specialize root typeof helper for complex&lt;quantity&lt;Unit,Y&gt; &gt;</span>
<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">Y</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">Unit</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">,</span><span class="keyword">long</span> <span class="identifier">D</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
        <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>       <span class="identifier">value_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="keyword">typename</span>
        <span class="identifier">root_typeof_helper</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;::</span><span class="identifier">type</span>    <span class="identifier">unit_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit_type</span><span class="special">,</span><span class="identifier">value_type</span><span class="special">&gt;</span>                      <span class="identifier">quantity_type</span><span class="special">;</span>
    <span class="keyword">typedef</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity_type</span><span class="special">&gt;</span>                              <span class="identifier">type</span><span class="special">;</span>

    <span class="keyword">static</span> <span class="identifier">type</span> <span class="identifier">value</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">Unit</span><span class="special">,</span><span class="identifier">Y</span><span class="special">&gt;</span> <span class="special">&gt;&amp;</span> <span class="identifier">x</span><span class="special">)</span>
    <span class="special">{</span>
        <span class="keyword">const</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">value_type</span><span class="special">&gt;</span>   <span class="identifier">tmp</span> <span class="special">=</span>
            <span class="identifier">root</span><span class="special">&lt;</span><span class="identifier">static_rational</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">,</span><span class="identifier">D</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">Y</span><span class="special">&gt;(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">real</span><span class="special">().</span><span class="identifier">value</span><span class="special">(),</span>
                                                   <span class="identifier">x</span><span class="special">.</span><span class="identifier">imag</span><span class="special">().</span><span class="identifier">value</span><span class="special">()));</span>

        <span class="keyword">return</span> <span class="identifier">type</span><span class="special">(</span><span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">real</span><span class="special">()),</span>
                   <span class="identifier">quantity_type</span><span class="special">::</span><span class="identifier">from_value</span><span class="special">(</span><span class="identifier">tmp</span><span class="special">.</span><span class="identifier">imag</span><span class="special">()));</span>
    <span class="special">}</span>
<span class="special">};</span>

<span class="special">}</span> <span class="comment">// namespace units</span>

<span class="special">}</span> <span class="comment">// namespace boost</span>
</pre>
<p>
      </p>
<p>
        With this replacement <code class="computeroutput"><span class="identifier">complex</span></code>
        class, we can declare a complex variable :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">,</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;</span> <span class="special">&gt;</span>     <span class="identifier">length_dimension</span><span class="special">;</span>

<span class="keyword">const</span> <span class="identifier">length_dimension</span>    <span class="identifier">L</span><span class="special">(</span><span class="identifier">complex</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="number">2.0</span><span class="special">,</span><span class="number">1.0</span><span class="special">)*</span><span class="identifier">meters</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        to get the correct behavior for all cases supported by <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span> with a <code class="computeroutput"><span class="identifier">complex</span></code> value type :
      </p>
<p>
</p>
<pre class="programlisting"><span class="special">+</span><span class="identifier">L</span>      <span class="special">=</span> <span class="number">2</span> <span class="special">+</span> <span class="number">1</span> <span class="identifier">i</span> <span class="identifier">m</span>
<span class="special">-</span><span class="identifier">L</span>      <span class="special">=</span> <span class="special">-</span><span class="number">2</span> <span class="special">+</span> <span class="special">-</span><span class="number">1</span> <span class="identifier">i</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">4</span> <span class="special">+</span> <span class="number">2</span> <span class="identifier">i</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">0</span> <span class="special">+</span> <span class="number">0</span> <span class="identifier">i</span> <span class="identifier">m</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">3</span> <span class="special">+</span> <span class="number">4</span> <span class="identifier">i</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
<span class="identifier">L</span><span class="special">/</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">1</span> <span class="special">+</span> <span class="number">0</span> <span class="identifier">i</span> <span class="identifier">dimensionless</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>     <span class="special">=</span> <span class="number">2</span> <span class="special">+</span> <span class="number">11</span> <span class="identifier">i</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span>
<span class="identifier">L</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="special">=</span> <span class="number">2.56713</span> <span class="special">+</span> <span class="number">2.14247</span> <span class="identifier">i</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span>
<span class="number">3</span><span class="identifier">vL</span>     <span class="special">=</span> <span class="number">1.29207</span> <span class="special">+</span> <span class="number">0.201294</span> <span class="identifier">i</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
<span class="special">(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span><span class="identifier">vL</span> <span class="special">=</span> <span class="number">1.62894</span> <span class="special">+</span> <span class="number">0.520175</span> <span class="identifier">i</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">2</span><span class="special">/</span><span class="number">3</span><span class="special">)</span>
</pre>
<p>
      </p>
<p>
        and, similarly, <code class="computeroutput"><span class="identifier">complex</span></code> with
        a <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
        value type
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">complex</span><span class="special">&lt;</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">&gt;</span>     <span class="identifier">length_dimension</span><span class="special">;</span>

<span class="keyword">const</span> <span class="identifier">length_dimension</span>    <span class="identifier">L</span><span class="special">(</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">,</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">meters</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        gives
      </p>
<p>
</p>
<pre class="programlisting"><span class="special">+</span><span class="identifier">L</span>      <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span> <span class="special">+</span> <span class="number">1</span> <span class="identifier">m</span> <span class="identifier">i</span>
<span class="special">-</span><span class="identifier">L</span>      <span class="special">=</span> <span class="special">-</span><span class="number">2</span> <span class="identifier">m</span> <span class="special">+</span> <span class="special">-</span><span class="number">1</span> <span class="identifier">m</span> <span class="identifier">i</span>
<span class="identifier">L</span><span class="special">+</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">4</span> <span class="identifier">m</span> <span class="special">+</span> <span class="number">2</span> <span class="identifier">m</span> <span class="identifier">i</span>
<span class="identifier">L</span><span class="special">-</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">0</span> <span class="identifier">m</span> <span class="special">+</span> <span class="number">0</span> <span class="identifier">m</span> <span class="identifier">i</span>
<span class="identifier">L</span><span class="special">*</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">3</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="special">+</span> <span class="number">4</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span> <span class="identifier">i</span>
<span class="identifier">L</span><span class="special">/</span><span class="identifier">L</span>     <span class="special">=</span> <span class="number">1</span> <span class="identifier">dimensionless</span> <span class="special">+</span> <span class="number">0</span> <span class="identifier">dimensionless</span> <span class="identifier">i</span>
<span class="identifier">L</span><span class="special">^</span><span class="number">3</span>     <span class="special">=</span> <span class="number">2</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span> <span class="special">+</span> <span class="number">11</span> <span class="identifier">m</span><span class="special">^</span><span class="number">3</span> <span class="identifier">i</span>
<span class="identifier">L</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="special">=</span> <span class="number">2.56713</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="special">+</span> <span class="number">2.14247</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span> <span class="identifier">i</span>
<span class="number">3</span><span class="identifier">vL</span>     <span class="special">=</span> <span class="number">1.29207</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="number">0.201294</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">1</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="identifier">i</span>
<span class="special">(</span><span class="number">3</span><span class="special">/</span><span class="number">2</span><span class="special">)</span><span class="identifier">vL</span> <span class="special">=</span> <span class="number">1.62894</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">2</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="special">+</span> <span class="number">0.520175</span> <span class="identifier">m</span><span class="special">^(</span><span class="number">2</span><span class="special">/</span><span class="number">3</span><span class="special">)</span> <span class="identifier">i</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.PerformanceExample"></a><a class="link" href="Examples.html#boost_units.Examples.PerformanceExample" title="Performance Example">Performance
      Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/performance.cpp" target="_top">performance.cpp</a>)
      </p>
<p>
        This example provides an ad hoc performance test to verify that zero runtime
        overhead is incurred when using <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/quantity.html" title="Class template quantity">quantity</a></code></span>
        in place of <code class="computeroutput"><span class="keyword">double</span></code>. Note that
        performance optimization and testing is not trivial, so some care must be
        taken in profiling. It is also critical to have a compiler capable of optimizing
        the many template instantiations and inline calls effectively to achieve
        maximal performance. Zero overhead for this test has been verified using
        gcc 4.0.1, and icc 9.0, 10.0, and 10.1 on Mac OS 10.4 and 10.5, and using
        msvc 8.0 on Windows XP.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.RadarBeamHeightExample"></a><a class="link" href="Examples.html#boost_units.Examples.RadarBeamHeightExample" title="Radar Beam Height">Radar Beam
      Height</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/radar_beam_height.cpp" target="_top">radar_beam_height.cpp</a>)
      </p>
<p>
        This example demonstrates the implementation of two non-SI units of length,
        the nautical mile :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">nautical</span> <span class="special">{</span>

<span class="keyword">struct</span> <span class="identifier">length_base_unit</span> <span class="special">:</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">base_unit</span><span class="special">&lt;</span><span class="identifier">length_base_unit</span><span class="special">,</span> <span class="identifier">length_dimension</span><span class="special">,</span> <span class="number">1</span><span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">static</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">name</span><span class="special">()</span>       <span class="special">{</span> <span class="keyword">return</span> <span class="string">"nautical mile"</span><span class="special">;</span> <span class="special">}</span>
    <span class="keyword">static</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">symbol</span><span class="special">()</span>     <span class="special">{</span> <span class="keyword">return</span> <span class="string">"nmi"</span><span class="special">;</span> <span class="special">}</span>
<span class="special">};</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">make_system</span><span class="special">&lt;</span><span class="identifier">length_base_unit</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">system</span><span class="special">;</span>

<span class="comment">/// unit typedefs</span>
<span class="keyword">typedef</span> <span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">system</span><span class="special">&gt;</span>    <span class="identifier">length</span><span class="special">;</span>

<span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">length</span> <span class="identifier">mile</span><span class="special">,</span><span class="identifier">miles</span><span class="special">;</span>

<span class="special">}</span> <span class="comment">// namespace nautical</span>

<span class="comment">// helper for conversions between nautical length and si length</span>
<span class="identifier">BOOST_UNITS_DEFINE_CONVERSION_FACTOR</span><span class="special">(</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length_base_unit</span><span class="special">,</span>
                                     <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter_base_unit</span><span class="special">,</span>
                                     <span class="keyword">double</span><span class="special">,</span> <span class="number">1.852e3</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        and the imperial foot :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="identifier">imperial</span> <span class="special">{</span>

<span class="keyword">struct</span> <span class="identifier">length_base_unit</span> <span class="special">:</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">base_unit</span><span class="special">&lt;</span><span class="identifier">length_base_unit</span><span class="special">,</span> <span class="identifier">length_dimension</span><span class="special">,</span> <span class="number">2</span><span class="special">&gt;</span>
<span class="special">{</span>
    <span class="keyword">static</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">name</span><span class="special">()</span>       <span class="special">{</span> <span class="keyword">return</span> <span class="string">"foot"</span><span class="special">;</span> <span class="special">}</span>
    <span class="keyword">static</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">symbol</span><span class="special">()</span>     <span class="special">{</span> <span class="keyword">return</span> <span class="string">"ft"</span><span class="special">;</span> <span class="special">}</span>
<span class="special">};</span>

<span class="keyword">typedef</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">make_system</span><span class="special">&lt;</span><span class="identifier">length_base_unit</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">system</span><span class="special">;</span>

<span class="comment">/// unit typedefs</span>
<span class="keyword">typedef</span> <span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">system</span><span class="special">&gt;</span>    <span class="identifier">length</span><span class="special">;</span>

<span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">length</span> <span class="identifier">foot</span><span class="special">,</span><span class="identifier">feet</span><span class="special">;</span>

<span class="special">}</span> <span class="comment">// imperial</span>

<span class="comment">// helper for conversions between imperial length and si length</span>
<span class="identifier">BOOST_UNITS_DEFINE_CONVERSION_FACTOR</span><span class="special">(</span><span class="identifier">imperial</span><span class="special">::</span><span class="identifier">length_base_unit</span><span class="special">,</span>
                                     <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter_base_unit</span><span class="special">,</span>
                                     <span class="keyword">double</span><span class="special">,</span> <span class="number">1.0</span><span class="special">/</span><span class="number">3.28083989501312</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        These units include conversions between themselves and the meter. Three functions
        for computing radar beam height from radar range and the local earth radius
        are defined. The first takes arguments in one system and returns a value
        in the same system :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">System</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">T</span><span class="special">&gt;</span>
<span class="identifier">radar_beam_height</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">T</span><span class="special">&gt;&amp;</span> <span class="identifier">radar_range</span><span class="special">,</span>
                  <span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">T</span><span class="special">&gt;&amp;</span> <span class="identifier">earth_radius</span><span class="special">,</span>
                  <span class="identifier">T</span> <span class="identifier">k</span> <span class="special">=</span> <span class="number">4.0</span><span class="special">/</span><span class="number">3.0</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System</span><span class="special">&gt;,</span><span class="identifier">T</span><span class="special">&gt;</span>
        <span class="special">(</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">radar_range</span><span class="special">)/(</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">k</span><span class="special">*</span><span class="identifier">earth_radius</span><span class="special">));</span>
<span class="special">}</span>
</pre>
<p>
      </p>
<p>
        The second is similar, but is templated on return type, so that the arguments
        are converted to the return unit system internally :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span> <span class="identifier">return_type</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">System1</span><span class="special">,</span><span class="keyword">class</span> <span class="identifier">System2</span><span class="special">,</span><span class="keyword">typename</span> <span class="identifier">T</span><span class="special">&gt;</span>
<span class="keyword">constexpr</span>
<span class="identifier">return_type</span>
<span class="identifier">radar_beam_height</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System1</span><span class="special">&gt;,</span><span class="identifier">T</span><span class="special">&gt;&amp;</span> <span class="identifier">radar_range</span><span class="special">,</span>
                  <span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">length_dimension</span><span class="special">,</span><span class="identifier">System2</span><span class="special">&gt;,</span><span class="identifier">T</span><span class="special">&gt;&amp;</span> <span class="identifier">earth_radius</span><span class="special">,</span>
                  <span class="identifier">T</span> <span class="identifier">k</span> <span class="special">=</span> <span class="number">4.0</span><span class="special">/</span><span class="number">3.0</span><span class="special">)</span>
<span class="special">{</span>
    <span class="comment">// need to decide which system to use for calculation</span>
    <span class="keyword">return</span> <span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">return_type</span><span class="special">&gt;(</span><span class="identifier">radar_range</span><span class="special">))</span>
            <span class="special">/</span> <span class="special">(</span><span class="number">2.0</span><span class="special">*</span><span class="identifier">k</span><span class="special">*</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">return_type</span><span class="special">&gt;(</span><span class="identifier">earth_radius</span><span class="special">));</span>
<span class="special">}</span>
</pre>
<p>
      </p>
<p>
        Finally, the third function is an empirical approximation that is only valid
        for radar ranges specified in nautical miles, returning beam height in feet.
        This function uses the heterogeneous unit of nautical miles per square root
        of feet to ensure dimensional correctness :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">constexpr</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">imperial</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>
<span class="identifier">radar_beam_height</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;&amp;</span> <span class="identifier">range</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">imperial</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>
        <span class="special">(</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">range</span><span class="special">/(</span><span class="number">1.23</span><span class="special">*</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">miles</span><span class="special">/</span><span class="identifier">root</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">imperial</span><span class="special">::</span><span class="identifier">feet</span><span class="special">))));</span>
<span class="special">}</span>
</pre>
<p>
      </p>
<p>
        With these, we can compute radar beam height in various unit systems :
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">radar_range</span><span class="special">(</span><span class="number">300.0</span><span class="special">*</span><span class="identifier">miles</span><span class="special">);</span>
<span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>       <span class="identifier">earth_radius</span><span class="special">(</span><span class="number">6371.0087714</span><span class="special">*</span><span class="identifier">kilo</span><span class="special">*</span><span class="identifier">meters</span><span class="special">);</span>

<span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>       <span class="identifier">beam_height_1</span><span class="special">(</span><span class="identifier">radar_beam_height</span><span class="special">(</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;(</span><span class="identifier">radar_range</span><span class="special">),</span><span class="identifier">earth_radius</span><span class="special">));</span>
<span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">beam_height_2</span><span class="special">(</span><span class="identifier">radar_beam_height</span><span class="special">(</span><span class="identifier">radar_range</span><span class="special">,</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;(</span><span class="identifier">earth_radius</span><span class="special">)));</span>
<span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>       <span class="identifier">beam_height_3</span><span class="special">(</span><span class="identifier">radar_beam_height</span><span class="special">&lt;</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">radar_range</span><span class="special">,</span><span class="identifier">earth_radius</span><span class="special">));</span>
<span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">beam_height_4</span><span class="special">(</span><span class="identifier">radar_beam_height</span><span class="special">&lt;</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">nautical</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">&gt;(</span><span class="identifier">radar_range</span><span class="special">,</span><span class="identifier">earth_radius</span><span class="special">));</span>
</pre>
<p>
      </p>
<p>
        giving
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">radar</span> <span class="identifier">range</span>        <span class="special">:</span> <span class="number">300</span> <span class="identifier">nmi</span>
<span class="identifier">earth</span> <span class="identifier">radius</span>       <span class="special">:</span> <span class="number">6.37101e+06</span> <span class="identifier">m</span>
<span class="identifier">beam</span> <span class="identifier">height</span> <span class="number">1</span>      <span class="special">:</span> <span class="number">18169.7</span> <span class="identifier">m</span>
<span class="identifier">beam</span> <span class="identifier">height</span> <span class="number">2</span>      <span class="special">:</span> <span class="number">9.81085</span> <span class="identifier">nmi</span>
<span class="identifier">beam</span> <span class="identifier">height</span> <span class="number">3</span>      <span class="special">:</span> <span class="number">18169.7</span> <span class="identifier">m</span>
<span class="identifier">beam</span> <span class="identifier">height</span> <span class="number">4</span>      <span class="special">:</span> <span class="number">9.81085</span> <span class="identifier">nmi</span>
<span class="identifier">beam</span> <span class="identifier">height</span> <span class="identifier">approx</span> <span class="special">:</span> <span class="number">59488.4</span> <span class="identifier">ft</span>
<span class="identifier">beam</span> <span class="identifier">height</span> <span class="identifier">approx</span> <span class="special">:</span> <span class="number">18132.1</span> <span class="identifier">m</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.HeterogeneousUnitExample"></a><a class="link" href="Examples.html#boost_units.Examples.HeterogeneousUnitExample" title="Heterogeneous Unit Example">Heterogeneous
      Unit Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/heterogeneous_unit.cpp" target="_top">heterogeneous_unit.cpp</a>)
      </p>
<p>
        Mixed units and mixed unit conversions.
      </p>
<p>
        This code:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span>        <span class="identifier">L</span><span class="special">(</span><span class="number">1.5</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">mass</span><span class="special">&gt;</span>         <span class="identifier">M</span><span class="special">(</span><span class="number">1.0</span><span class="special">*</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">gram</span><span class="special">);</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">L</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">M</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">L</span><span class="special">*</span><span class="identifier">M</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">L</span><span class="special">/</span><span class="identifier">M</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="number">1.0</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">kilogram</span><span class="special">/</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="number">1.0</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">kilogram</span><span class="special">/</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)/</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="number">1.0</span><span class="special">*</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">centimeter</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">kilogram</span><span class="special">/</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="number">1.0</span><span class="special">*</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">centimeter</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">kilogram</span><span class="special">/</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)/</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
</pre>
<p>
      </p>
<p>
        gives
      </p>
<p>
</p>
<pre class="programlisting"><span class="number">1.5</span> <span class="identifier">m</span>
<span class="number">1</span> <span class="identifier">g</span>
<span class="number">1.5</span> <span class="identifier">m</span> <span class="identifier">g</span>
<span class="number">1.5</span> <span class="identifier">m</span> <span class="identifier">g</span><span class="special">^-</span><span class="number">1</span>

<span class="number">1</span> <span class="identifier">N</span>
<span class="number">1</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>

<span class="number">1</span> <span class="identifier">cm</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
<span class="number">1</span> <span class="identifier">cm</span> <span class="identifier">m</span><span class="special">^-</span><span class="number">1</span> <span class="identifier">kg</span> <span class="identifier">s</span><span class="special">^-</span><span class="number">2</span>
</pre>
<p>
      </p>
<p>
        Arbitrary conversions also work:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">area</span><span class="special">&gt;</span>      <span class="identifier">A</span><span class="special">(</span><span class="number">1.5</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">*</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">centimeter</span><span class="special">);</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="number">1.5</span><span class="special">*</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">*</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">centimeter</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">A</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span>
          <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
</pre>
<p>
      </p>
<p>
        yielding
      </p>
<p>
</p>
<pre class="programlisting"><span class="number">1.5</span> <span class="identifier">cm</span> <span class="identifier">m</span>
<span class="number">0.015</span> <span class="identifier">m</span><span class="special">^</span><span class="number">2</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.AbsoluteRelativeTemperatureExample"></a><a class="link" href="Examples.html#boost_units.Examples.AbsoluteRelativeTemperatureExample" title="Absolute and Relative Temperature Example">Absolute
      and Relative Temperature Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/temperature.cpp" target="_top">temperature.cpp</a>)
      </p>
<p>
        This example demonstrates using of absolute temperatures and relative temperature
        differences in Fahrenheit and converting between these and the Kelvin temperature
        scale. This issue touches on some surprisingly deep mathematical concepts
        (see <a href="http://en.wikipedia.org/wiki/Affine_space" target="_top">Wikipedia</a>
        for a basic review), but for our purposes here, we will simply observe that
        it is important to be able to differentiate between an absolute temperature
        measurement and a measurement of temperature difference. This is accomplished
        by using the <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/absolute.html" title="Class template absolute">absolute</a></code></span>
        wrapper class.
      </p>
<p>
        First we define a system using the predefined fahrenheit base unit:
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">typedef</span> <span class="identifier">temperature</span><span class="special">::</span><span class="identifier">fahrenheit_base_unit</span><span class="special">::</span><span class="identifier">unit_type</span>    <span class="identifier">temperature</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">get_system</span><span class="special">&lt;</span><span class="identifier">temperature</span><span class="special">&gt;::</span><span class="identifier">type</span>                   <span class="identifier">system</span><span class="special">;</span>

<span class="identifier">BOOST_UNITS_STATIC_CONSTANT</span><span class="special">(</span><span class="identifier">degree</span><span class="special">,</span><span class="identifier">temperature</span><span class="special">);</span>
<span class="identifier">BOOST_UNITS_STATIC_CONSTANT</span><span class="special">(</span><span class="identifier">degrees</span><span class="special">,</span><span class="identifier">temperature</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        Now we can create some quantities:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">fahrenheit</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">&gt;</span>    <span class="identifier">T1p</span><span class="special">(</span>
    <span class="number">32.0</span><span class="special">*</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">fahrenheit</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;());</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">fahrenheit</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span>               <span class="identifier">T1v</span><span class="special">(</span>
    <span class="number">32.0</span><span class="special">*</span><span class="identifier">fahrenheit</span><span class="special">::</span><span class="identifier">degrees</span><span class="special">);</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">&gt;</span>            <span class="identifier">T2p</span><span class="special">(</span><span class="identifier">T1p</span><span class="special">);</span>
<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span>                       <span class="identifier">T2v</span><span class="special">(</span><span class="identifier">T1v</span><span class="special">);</span>
</pre>
<p>
      </p>
<p>
        Note the use of <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/absolute.html" title="Class template absolute">absolute</a></code></span>
        to wrap a unit. The resulting output is:
      </p>
<p>
</p>
<pre class="programlisting"><span class="special">{</span> <span class="number">32</span> <span class="special">}</span> <span class="identifier">F</span>
<span class="special">{</span> <span class="number">273.15</span> <span class="special">}</span> <span class="identifier">K</span>
<span class="special">{</span> <span class="number">273.15</span> <span class="special">}</span> <span class="identifier">K</span>
<span class="special">[</span> <span class="number">32</span> <span class="special">]</span> <span class="identifier">F</span>
<span class="special">[</span> <span class="number">17.7778</span> <span class="special">]</span> <span class="identifier">K</span>
<span class="special">[</span> <span class="number">17.7778</span> <span class="special">]</span> <span class="identifier">K</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.RuntimeConversionFactorExample"></a><a class="link" href="Examples.html#boost_units.Examples.RuntimeConversionFactorExample" title="Runtime Conversion Factor Example">Runtime
      Conversion Factor Example</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/runtime_conversion_factor.cpp" target="_top">runtime_conversion_factor.cpp</a>)
      </p>
<p>
        The Boost.Units library does not require that the conversion factors be compile
        time constants, as is demonstrated in this example:
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">base_dimension</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">base_unit</span><span class="special">;</span>

<span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">long</span> <span class="identifier">currency_base</span> <span class="special">=</span> <span class="number">1</span><span class="special">;</span>

<span class="keyword">struct</span> <span class="identifier">currency_base_dimension</span> <span class="special">:</span> <span class="identifier">base_dimension</span><span class="special">&lt;</span><span class="identifier">currency_base_dimension</span><span class="special">,</span> <span class="number">1</span><span class="special">&gt;</span> <span class="special">{};</span>

<span class="keyword">typedef</span> <span class="identifier">currency_base_dimension</span><span class="special">::</span><span class="identifier">dimension_type</span> <span class="identifier">currency_type</span><span class="special">;</span>

<span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">long</span> <span class="identifier">N</span><span class="special">&gt;</span>
<span class="keyword">struct</span> <span class="identifier">currency_base_unit</span> <span class="special">:</span>
    <span class="identifier">base_unit</span><span class="special">&lt;</span><span class="identifier">currency_base_unit</span><span class="special">&lt;</span><span class="identifier">N</span><span class="special">&gt;,</span> <span class="identifier">currency_type</span><span class="special">,</span> <span class="identifier">currency_base</span> <span class="special">+</span> <span class="identifier">N</span><span class="special">&gt;</span> <span class="special">{};</span>

<span class="keyword">typedef</span> <span class="identifier">currency_base_unit</span><span class="special">&lt;</span><span class="number">0</span><span class="special">&gt;</span> <span class="identifier">us_dollar_base_unit</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">currency_base_unit</span><span class="special">&lt;</span><span class="number">1</span><span class="special">&gt;</span> <span class="identifier">euro_base_unit</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">us_dollar_base_unit</span><span class="special">::</span><span class="identifier">unit_type</span> <span class="identifier">us_dollar</span><span class="special">;</span>
<span class="keyword">typedef</span> <span class="identifier">euro_base_unit</span><span class="special">::</span><span class="identifier">unit_type</span> <span class="identifier">euro</span><span class="special">;</span>

<span class="comment">// an array of all possible conversions</span>
<span class="keyword">double</span> <span class="identifier">conversion_factors</span><span class="special">[</span><span class="number">2</span><span class="special">][</span><span class="number">2</span><span class="special">]</span> <span class="special">=</span> <span class="special">{</span>
    <span class="special">{</span><span class="number">1.0</span><span class="special">,</span> <span class="number">1.0</span><span class="special">},</span>
    <span class="special">{</span><span class="number">1.0</span><span class="special">,</span> <span class="number">1.0</span><span class="special">}</span>
<span class="special">};</span>

<span class="keyword">double</span> <span class="identifier">get_conversion_factor</span><span class="special">(</span><span class="keyword">long</span> <span class="identifier">from</span><span class="special">,</span> <span class="keyword">long</span> <span class="identifier">to</span><span class="special">)</span> <span class="special">{</span>
    <span class="keyword">return</span><span class="special">(</span><span class="identifier">conversion_factors</span><span class="special">[</span><span class="identifier">from</span><span class="special">][</span><span class="identifier">to</span><span class="special">]);</span>
<span class="special">}</span>

<span class="keyword">void</span> <span class="identifier">set_conversion_factor</span><span class="special">(</span><span class="keyword">long</span> <span class="identifier">from</span><span class="special">,</span> <span class="keyword">long</span> <span class="identifier">to</span><span class="special">,</span> <span class="keyword">double</span> <span class="identifier">value</span><span class="special">)</span> <span class="special">{</span>
    <span class="identifier">conversion_factors</span><span class="special">[</span><span class="identifier">from</span><span class="special">][</span><span class="identifier">to</span><span class="special">]</span> <span class="special">=</span> <span class="identifier">value</span><span class="special">;</span>
    <span class="identifier">conversion_factors</span><span class="special">[</span><span class="identifier">to</span><span class="special">][</span><span class="identifier">from</span><span class="special">]</span> <span class="special">=</span> <span class="number">1.0</span> <span class="special">/</span> <span class="identifier">value</span><span class="special">;</span>
<span class="special">}</span>

<span class="identifier">BOOST_UNITS_DEFINE_CONVERSION_FACTOR_TEMPLATE</span><span class="special">((</span><span class="keyword">long</span> <span class="identifier">N1</span><span class="special">)(</span><span class="keyword">long</span> <span class="identifier">N2</span><span class="special">),</span>
    <span class="identifier">currency_base_unit</span><span class="special">&lt;</span><span class="identifier">N1</span><span class="special">&gt;,</span>
    <span class="identifier">currency_base_unit</span><span class="special">&lt;</span><span class="identifier">N2</span><span class="special">&gt;,</span>
    <span class="keyword">double</span><span class="special">,</span> <span class="identifier">get_conversion_factor</span><span class="special">(</span><span class="identifier">N1</span><span class="special">,</span> <span class="identifier">N2</span><span class="special">));</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.UnitsWithNonbaseDimensions"></a><a class="link" href="Examples.html#boost_units.Examples.UnitsWithNonbaseDimensions" title="Units with Non-base Dimensions">Units
      with Non-base Dimensions</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/non_base_dimension.cpp" target="_top">non_base_dimension.cpp</a>)
      </p>
<p>
        It is also possible to define base units that have derived rather than base
        dimensions:
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">struct</span> <span class="identifier">imperial_gallon_tag</span> <span class="special">:</span>
    <span class="identifier">base_unit</span><span class="special">&lt;</span><span class="identifier">imperial_gallon_tag</span><span class="special">,</span> <span class="identifier">volume_dimension</span><span class="special">,</span> <span class="number">1</span><span class="special">&gt;</span> <span class="special">{</span> <span class="special">};</span>

<span class="keyword">typedef</span> <span class="identifier">make_system</span><span class="special">&lt;</span><span class="identifier">imperial_gallon_tag</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">imperial</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">volume_dimension</span><span class="special">,</span><span class="identifier">imperial</span><span class="special">&gt;</span> <span class="identifier">imperial_gallon</span><span class="special">;</span>

<span class="keyword">struct</span> <span class="identifier">us_gallon_tag</span> <span class="special">:</span> <span class="identifier">base_unit</span><span class="special">&lt;</span><span class="identifier">us_gallon_tag</span><span class="special">,</span> <span class="identifier">volume_dimension</span><span class="special">,</span> <span class="number">2</span><span class="special">&gt;</span> <span class="special">{</span> <span class="special">};</span>

<span class="keyword">typedef</span> <span class="identifier">make_system</span><span class="special">&lt;</span><span class="identifier">us_gallon_tag</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="identifier">us</span><span class="special">;</span>

<span class="keyword">typedef</span> <span class="identifier">unit</span><span class="special">&lt;</span><span class="identifier">volume_dimension</span><span class="special">,</span><span class="identifier">us</span><span class="special">&gt;</span> <span class="identifier">us_gallon</span><span class="special">;</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.OutputForCompositeUnits"></a><a class="link" href="Examples.html#boost_units.Examples.OutputForCompositeUnits" title="Output for Composite Units">Output
      for Composite Units</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/composite_output.cpp" target="_top">composite_output.cpp</a>)
      </p>
<p>
        If a unit has a special name and/or symbol, the free functions <code class="computeroutput"><span class="identifier">name_string</span></code> and <code class="computeroutput"><span class="identifier">symbol_string</span></code>
        can be overloaded directly.
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">name_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">cgs</span><span class="special">::</span><span class="identifier">force</span><span class="special">&amp;)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="string">"dyne"</span><span class="special">;</span>
<span class="special">}</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">symbol_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">cgs</span><span class="special">::</span><span class="identifier">force</span><span class="special">&amp;)</span>
<span class="special">{</span>
    <span class="keyword">return</span> <span class="string">"dyn"</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
      </p>
<p>
        In this case, any unit that reduces to the overloaded unit will be output
        with the replacement symbol.
      </p>
<p>
        Special names and symbols for the SI and CGS unit systems are found in <code class="computeroutput"><a class="link" href="Reference.html#header.boost.units.systems.si.io_hpp" title="Header &lt;boost/units/systems/si/io.hpp&gt;">boost/units/systems/si/io.hpp</a></code>
        and <code class="computeroutput"><a class="link" href="Reference.html#header.boost.units.systems.cgs.io_hpp" title="Header &lt;boost/units/systems/cgs/io.hpp&gt;">boost/units/systems/cgs/io.hpp</a></code>,
        respectively. If these headers are not included, the output will simply follow
        default rules using the appropriate fundamental dimensions. Note that neither
        of these functions is defined for quantities because doing so would require
        making assumptions on how the corresponding value type should be formatted.
      </p>
<p>
        Three <code class="computeroutput"><span class="identifier">ostream</span></code> formatters,
        <code class="computeroutput"><span class="identifier">symbol_format</span></code>, <code class="computeroutput"><span class="identifier">name_format</span></code>, and <code class="computeroutput"><span class="identifier">typename_format</span></code>
        are provided for convenience. These select the textual representation of
        units provided by <code class="computeroutput"><span class="identifier">symbol_string</span></code>
        or <code class="computeroutput"><span class="identifier">name_string</span></code> in the first
        two cases, while the latter returns a demangled typename for debugging purposes.
        Formatting of scaled unit is also done correctly.
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.autoscale"></a><a class="link" href="Examples.html#boost_units.Examples.autoscale" title="Automatically Scaled Units">Automatically Scaled
      Units</a>
</h3></div></div></div>
<p>
        It is often desirable to scale a <span class="underline"><code class="computeroutput"><a class="link" href="../boost/units/unit.html" title="Class template unit">unit</a></code></span>
        automatically, depending on its value, to keep the integral part in a limited
        range, usually between 1 and 999.
      </p>
<p>
        For example, using <a href="http://en.wikipedia.org/wiki/Engineering_notation" target="_top">engineering
        notation prefixes</a>,
      </p>
<pre class="programlisting"><span class="string">"1234.5 m"</span> <span class="identifier">is</span> <span class="identifier">more</span> <span class="identifier">helpfully</span> <span class="identifier">displayed</span> <span class="identifier">as</span> <span class="string">"1.234 km"</span>
<span class="string">"0.000000001234 m"</span> <span class="identifier">is</span> <span class="identifier">more</span> <span class="identifier">clearly</span> <span class="identifier">displayed</span> <span class="identifier">as</span> <span class="string">"1.2345 nanometer"</span><span class="special">.</span>
</pre>
<p>
        The iostream manipulators <code class="computeroutput"><span class="identifier">engineering_prefixes</span></code>
        or <code class="computeroutput"><span class="identifier">binary_prefixes</span></code> make this
        easy.
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">binary_prefix</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">engineering_prefix</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">no_prefix</span><span class="special">;</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">l</span> <span class="special">=</span> <span class="number">2.345</span> <span class="special">*</span> <span class="identifier">meters</span><span class="special">;</span>   <span class="comment">// A quantity of length, in units of meters.</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">l</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Outputs "2.345 m".</span>
<span class="identifier">l</span> <span class="special">=</span>  <span class="number">1000.0</span> <span class="special">*</span> <span class="identifier">l</span><span class="special">;</span> <span class="comment">// Increase it by 1000, so expect a k prefix.</span>
<span class="comment">// Note that a double 1000.0 is required - an integer will fail to compile.</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">l</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Output autoprefixed with k to "2.345 km".</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">energy</span><span class="special">&gt;</span> <span class="identifier">e</span> <span class="special">=</span> <span class="identifier">kilograms</span> <span class="special">*</span> <span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;(</span><span class="identifier">l</span> <span class="special">/</span> <span class="identifier">seconds</span><span class="special">);</span> <span class="comment">// A quantity of energy.</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">e</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 5.49902 MJ</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">name_format</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">e</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 5.49902 megaJoule</span>
</pre>
<p>
      </p>
<p>
        (The complete set of <a href="http://physics.nist.gov/cuu/Units/prefixes.html" target="_top">engineering
        and scientific multiples</a> is not used (not centi or deci for example),
        but only powers of ten that are multiples of three, 10^3).
      </p>
<p>
        Similarly, the equivalent <a href="http://en.wikipedia.org/wiki/Binary_prefixes" target="_top">binary
        prefixes</a> used for displaying computing kilobytes, megabytes, gigabytes...
      </p>
<p>
        These are the 2^10 = 1024, 2^20 = 1 048 576, 2^30 ... multiples.
      </p>
<p>
        (See also <a href="http://physics.nist.gov/cuu/Units/binary.html" target="_top">Prefixes
        for binary multiples</a>
      </p>
<p>
        This scale is specified in IEC 60027-2, Second edition, 2000-11, Letter symbols
        to be used in electrical technology - Part 2: Telecommunications and electronics).
      </p>
<p>
</p>
<pre class="programlisting"><span class="comment">// Don't forget that the units name or symbol format specification is persistent.</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">symbol_format</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Resets the format to the default symbol format.</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">byte_base_unit</span><span class="special">::</span><span class="identifier">unit_type</span><span class="special">&gt;</span> <span class="identifier">b</span> <span class="special">=</span> <span class="number">2048.</span> <span class="special">*</span> <span class="identifier">byte_base_unit</span><span class="special">::</span><span class="identifier">unit_type</span><span class="special">();</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">b</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>  <span class="comment">// 2.048 kb</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">symbol_format</span> <span class="special">&lt;&lt;</span> <span class="identifier">binary_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">b</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">//  "2 Kib"</span>
</pre>
<p>
      </p>
<p>
        But note that scalar dimensionless values, like int, float and double, are
        <span class="bold"><strong>not</strong></span> prefixed automatically by the engineering_prefix
        or binary_prefix iostream manipulators.
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">const</span> <span class="keyword">double</span> <span class="identifier">s1</span> <span class="special">=</span> <span class="number">2345.6</span><span class="special">;</span>
<span class="keyword">const</span> <span class="keyword">long</span> <span class="identifier">x1</span> <span class="special">=</span> <span class="number">23456</span><span class="special">;</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">s1</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 2345.6</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">engineering_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">x1</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 23456</span>

<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">binary_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">s1</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 2345.6</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">binary_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">x1</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 23456</span>
</pre>
<p>
      </p>
<p>
        You can output the name or symbol of a unit (rather than the most common
        quantity of a unit).
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">const</span> <span class="identifier">length</span> <span class="identifier">L</span><span class="special">;</span> <span class="comment">// A unit of length (but not a quantity of length).</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">L</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Default length unit is meter,</span>
<span class="comment">// but default is symbol format so output is just "m".</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">name_format</span> <span class="special">&lt;&lt;</span> <span class="identifier">L</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// default length name is "meter".</span>
</pre>
<p>
      </p>
<p>
        Note too that all the formatting flags are persistent, so that if you set
        engineering_prefix, then it applies to all future outputs, until you select
        binary_prefix, or explicitly switch autoprefix off. You can specify no prefix
        (the default of course) in two ways:
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">no_prefix</span><span class="special">(</span><span class="identifier">cout</span><span class="special">);</span> <span class="comment">// Clear any prefix flag.</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">no_prefix</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// Clear any prefix flag using `no_prefix` manipulator.</span>
</pre>
<p>
      </p>
<p>
        And you can get the format flags for diagnosing problems.
      </p>
<p>
</p>
<pre class="programlisting"><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">get_autoprefix</span><span class="special">(</span><span class="identifier">cout</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 8 is `autoprefix_binary` from `enum autoprefix_mode`.</span>
<span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">get_format</span><span class="special">(</span><span class="identifier">cout</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span> <span class="comment">// 1 is `name_fmt` from `enum format_mode`.</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.ConversionFactor"></a><a class="link" href="Examples.html#boost_units.Examples.ConversionFactor" title="Conversion Factor">Conversion Factor</a>
</h3></div></div></div>
<p>
        This code demonstrates the use of the <code class="computeroutput"><span class="identifier">conversion_factor</span></code>
        free function to determine the scale factor between two units.
      </p>
<p>
        (<a href="../../../libs/units/example/conversion_factor.cpp" target="_top">conversion_factor.cpp</a>)
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">double</span> <span class="identifier">dyne_to_newton</span> <span class="special">=</span>
    <span class="identifier">conversion_factor</span><span class="special">(</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">dyne</span><span class="special">,</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">newton</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">dyne_to_newton</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="keyword">double</span> <span class="identifier">force_over_mass_conversion</span> <span class="special">=</span>
    <span class="identifier">conversion_factor</span><span class="special">(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">newton</span><span class="special">/</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">kilogram</span><span class="special">,</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">dyne</span><span class="special">/</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">gram</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">force_over_mass_conversion</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="keyword">double</span> <span class="identifier">momentum_conversion</span> <span class="special">=</span>
    <span class="identifier">conversion_factor</span><span class="special">(</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">momentum</span><span class="special">(),</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">momentum</span><span class="special">());</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">momentum_conversion</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="keyword">double</span> <span class="identifier">momentum_over_mass_conversion</span> <span class="special">=</span>
    <span class="identifier">conversion_factor</span><span class="special">(</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">momentum</span><span class="special">()/</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">mass</span><span class="special">(),</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">momentum</span><span class="special">()/</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">gram</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">momentum_over_mass_conversion</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>

<span class="keyword">double</span> <span class="identifier">acceleration_conversion</span> <span class="special">=</span>
    <span class="identifier">conversion_factor</span><span class="special">(</span><span class="identifier">cgs</span><span class="special">::</span><span class="identifier">gal</span><span class="special">,</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">meter_per_second_squared</span><span class="special">);</span>
<span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="identifier">acceleration_conversion</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
</pre>
<p>
      </p>
<p>
        Produces
      </p>
<p>
</p>
<pre class="programlisting"><span class="number">1e-005</span>
<span class="number">100</span>
<span class="number">1e-005</span>
<span class="number">100</span>
<span class="number">0.01</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.RuntimeUnits"></a><a class="link" href="Examples.html#boost_units.Examples.RuntimeUnits" title="Runtime Units">Runtime Units</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/runtime_unit.cpp" target="_top">runtime_unit.cpp</a>)
      </p>
<p>
        This example shows how to implement an interface that allow different units
        at runtime while still maintaining type safety for internal calculations.
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">namespace</span> <span class="special">{</span>

<span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">;</span>
<span class="keyword">using</span> <span class="identifier">imperial</span><span class="special">::</span><span class="identifier">foot_base_unit</span><span class="special">;</span>

<span class="identifier">std</span><span class="special">::</span><span class="identifier">map</span><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span><span class="special">,</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="identifier">known_units</span><span class="special">;</span>

<span class="special">}</span>

<span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">calculate</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;&amp;</span> <span class="identifier">t</span><span class="special">)</span>
<span class="special">{</span>
    <span class="keyword">return</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">hypot</span><span class="special">(</span><span class="identifier">t</span><span class="special">,</span> <span class="number">2.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters</span><span class="special">));</span>
<span class="special">}</span>

<span class="keyword">int</span> <span class="identifier">main</span><span class="special">()</span>
<span class="special">{</span>
    <span class="identifier">known_units</span><span class="special">[</span><span class="string">"meter"</span><span class="special">]</span> <span class="special">=</span> <span class="number">1.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters</span><span class="special">;</span>
    <span class="identifier">known_units</span><span class="special">[</span><span class="string">"centimeter"</span><span class="special">]</span> <span class="special">=</span> <span class="special">.</span><span class="number">01</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters</span><span class="special">;</span>
    <span class="identifier">known_units</span><span class="special">[</span><span class="string">"foot"</span><span class="special">]</span> <span class="special">=</span>
        <span class="identifier">conversion_factor</span><span class="special">(</span><span class="identifier">foot_base_unit</span><span class="special">::</span><span class="identifier">unit_type</span><span class="special">(),</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">)</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">;</span>

    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">output_type</span><span class="special">(</span><span class="string">"meter"</span><span class="special">);</span>
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">string</span> <span class="identifier">input</span><span class="special">;</span>

    <span class="keyword">while</span><span class="special">((</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"&gt; "</span><span class="special">)</span> <span class="special">&amp;&amp;</span> <span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cin</span> <span class="special">&gt;&gt;</span> <span class="identifier">input</span><span class="special">))</span>
    <span class="special">{</span>
        <span class="keyword">if</span><span class="special">(!</span><span class="identifier">input</span><span class="special">.</span><span class="identifier">empty</span><span class="special">()</span> <span class="special">&amp;&amp;</span> <span class="identifier">input</span><span class="special">[</span><span class="number">0</span><span class="special">]</span> <span class="special">==</span> <span class="char">'#'</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">std</span><span class="special">::</span><span class="identifier">getline</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cin</span><span class="special">,</span> <span class="identifier">input</span><span class="special">);</span>
        <span class="special">}</span>
        <span class="keyword">else</span> <span class="keyword">if</span><span class="special">(</span><span class="identifier">input</span> <span class="special">==</span> <span class="string">"exit"</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="keyword">break</span><span class="special">;</span>
        <span class="special">}</span>
        <span class="keyword">else</span> <span class="keyword">if</span><span class="special">(</span><span class="identifier">input</span> <span class="special">==</span> <span class="string">"help"</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"type \"exit\" to exit\n"</span>
                <span class="string">"type \"return 'unit'\" to set the return units\n"</span>
                <span class="string">"type \"'number' 'unit'\" to do a simple calculation"</span>
                <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
        <span class="special">}</span>
        <span class="keyword">else</span> <span class="keyword">if</span><span class="special">(</span><span class="identifier">input</span> <span class="special">==</span> <span class="string">"return"</span><span class="special">)</span>
        <span class="special">{</span>
            <span class="keyword">if</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cin</span> <span class="special">&gt;&gt;</span> <span class="identifier">input</span><span class="special">)</span>
            <span class="special">{</span>
                <span class="keyword">if</span><span class="special">(</span><span class="identifier">known_units</span><span class="special">.</span><span class="identifier">find</span><span class="special">(</span><span class="identifier">input</span><span class="special">)</span> <span class="special">!=</span> <span class="identifier">known_units</span><span class="special">.</span><span class="identifier">end</span><span class="special">())</span>
                <span class="special">{</span>
                    <span class="identifier">output_type</span> <span class="special">=</span> <span class="identifier">input</span><span class="special">;</span>
                    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Done."</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
                <span class="special">}</span>
                <span class="keyword">else</span>
                <span class="special">{</span>
                    <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Unknown unit \""</span> <span class="special">&lt;&lt;</span> <span class="identifier">input</span> <span class="special">&lt;&lt;</span> <span class="string">"\""</span>
                         <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
                <span class="special">}</span>
            <span class="special">}</span>
            <span class="keyword">else</span>
            <span class="special">{</span>
                <span class="keyword">break</span><span class="special">;</span>
            <span class="special">}</span>
        <span class="special">}</span>
        <span class="keyword">else</span>
        <span class="special">{</span>
            <span class="keyword">try</span>
            <span class="special">{</span>
                <span class="keyword">double</span> <span class="identifier">value</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lexical_cast</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span><span class="identifier">input</span><span class="special">);</span>

                <span class="keyword">if</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">cin</span> <span class="special">&gt;&gt;</span> <span class="identifier">input</span><span class="special">)</span>
                <span class="special">{</span>
                    <span class="keyword">if</span><span class="special">(</span><span class="identifier">known_units</span><span class="special">.</span><span class="identifier">find</span><span class="special">(</span><span class="identifier">input</span><span class="special">)</span> <span class="special">!=</span> <span class="identifier">known_units</span><span class="special">.</span><span class="identifier">end</span><span class="special">())</span>
                    <span class="special">{</span>
                        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="keyword">static_cast</span><span class="special">&lt;</span><span class="keyword">double</span><span class="special">&gt;(</span>
                            <span class="identifier">calculate</span><span class="special">(</span><span class="identifier">value</span> <span class="special">*</span> <span class="identifier">known_units</span><span class="special">[</span><span class="identifier">input</span><span class="special">])</span> <span class="special">/</span>
                            <span class="identifier">known_units</span><span class="special">[</span><span class="identifier">output_type</span><span class="special">])</span>
                            <span class="special">&lt;&lt;</span> <span class="char">' '</span> <span class="special">&lt;&lt;</span> <span class="identifier">output_type</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
                    <span class="special">}</span>
                    <span class="keyword">else</span>
                    <span class="special">{</span>
                        <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Unknown unit \""</span> <span class="special">&lt;&lt;</span> <span class="identifier">input</span> <span class="special">&lt;&lt;</span> <span class="string">"\""</span>
                            <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
                    <span class="special">}</span>
                <span class="special">}</span>
                <span class="keyword">else</span>
                <span class="special">{</span>
                    <span class="keyword">break</span><span class="special">;</span>
                <span class="special">}</span>
            <span class="special">}</span>
            <span class="keyword">catch</span><span class="special">(...)</span>
            <span class="special">{</span>
                <span class="identifier">std</span><span class="special">::</span><span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Input error"</span> <span class="special">&lt;&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">endl</span><span class="special">;</span>
            <span class="special">}</span>
        <span class="special">}</span>
    <span class="special">}</span>
<span class="special">}</span>
</pre>
<p>
      </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="boost_units.Examples.lambda"></a><a class="link" href="Examples.html#boost_units.Examples.lambda" title="Interoperability with Boost.Lambda">Interoperability with Boost.Lambda</a>
</h3></div></div></div>
<p>
        (<a href="../../../libs/units/example/lambda.cpp" target="_top">lambda.cpp</a>)
      </p>
<p>
        The header <code class="computeroutput"><a class="link" href="Reference.html#header.boost.units.lambda_hpp" title="Header &lt;boost/units/lambda.hpp&gt;">boost/units/lambda.hpp</a></code>
        provides overloads and specializations needed to make Boost.Units usable
        with the Boost.Lambda library.
      </p>
<p>
</p>
<pre class="programlisting"><span class="keyword">int</span> <span class="identifier">main</span><span class="special">(</span><span class="keyword">int</span> <span class="identifier">argc</span><span class="special">,</span> <span class="keyword">char</span> <span class="special">**</span><span class="identifier">argv</span><span class="special">)</span> <span class="special">{</span>

   <span class="keyword">using</span> <span class="keyword">namespace</span> <span class="identifier">std</span><span class="special">;</span>
   <span class="keyword">namespace</span> <span class="identifier">bl</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">lambda</span><span class="special">;</span>
   <span class="keyword">namespace</span> <span class="identifier">bu</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">;</span>
   <span class="keyword">namespace</span> <span class="identifier">si</span> <span class="special">=</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">units</span><span class="special">::</span><span class="identifier">si</span><span class="special">;</span>


   <span class="comment">////////////////////////////////////////////////////////////////////////</span>
   <span class="comment">// Mechanical example: linear accelerated movement</span>
   <span class="comment">////////////////////////////////////////////////////////////////////////</span>

   <span class="comment">// Initial condition variables for acceleration, speed, and displacement</span>
   <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">acceleration</span><span class="special">&gt;</span> <span class="identifier">a</span> <span class="special">=</span> <span class="number">2.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters_per_second_squared</span><span class="special">;</span>
   <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">velocity</span><span class="special">&gt;</span> <span class="identifier">v</span> <span class="special">=</span> <span class="number">1.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters_per_second</span><span class="special">;</span>
   <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">s0</span> <span class="special">=</span> <span class="number">0.5</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">;</span>

   <span class="comment">// Displacement over time</span>
   <span class="identifier">boost</span><span class="special">::</span><span class="identifier">function</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="special">(</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">time</span><span class="special">&gt;)</span> <span class="special">&gt;</span>
       <span class="identifier">s</span> <span class="special">=</span> <span class="number">0.5</span> <span class="special">*</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">var</span><span class="special">(</span><span class="identifier">a</span><span class="special">)</span> <span class="special">*</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_1</span> <span class="special">*</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_1</span>
           <span class="special">+</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">var</span><span class="special">(</span><span class="identifier">v</span><span class="special">)</span> <span class="special">*</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_1</span>
           <span class="special">+</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">var</span><span class="special">(</span><span class="identifier">s0</span><span class="special">);</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Linear accelerated movement:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"a = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">a</span> <span class="special">&lt;&lt;</span> <span class="string">", v = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">v</span> <span class="special">&lt;&lt;</span> <span class="string">", s0 = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">s0</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"s(1.0 * si::second) = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">s</span><span class="special">(</span><span class="number">1.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>

   <span class="comment">// Change initial conditions</span>
   <span class="identifier">a</span> <span class="special">=</span> <span class="number">1.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters_per_second_squared</span><span class="special">;</span>
   <span class="identifier">v</span> <span class="special">=</span> <span class="number">2.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meters_per_second</span><span class="special">;</span>
   <span class="identifier">s0</span> <span class="special">=</span> <span class="special">-</span><span class="number">1.5</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">;</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"a = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">a</span> <span class="special">&lt;&lt;</span> <span class="string">", v = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">v</span> <span class="special">&lt;&lt;</span> <span class="string">", s0 = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">s0</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"s(1.0 * si::second) = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">s</span><span class="special">(</span><span class="number">1.0</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>


   <span class="comment">////////////////////////////////////////////////////////////////////////</span>
   <span class="comment">// Electrical example: oscillating current</span>
   <span class="comment">////////////////////////////////////////////////////////////////////////</span>

   <span class="comment">// Constants for the current amplitude, frequency, and offset current</span>
   <span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">current</span><span class="special">&gt;</span> <span class="identifier">iamp</span> <span class="special">=</span> <span class="number">1.5</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">ampere</span><span class="special">;</span>
   <span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">frequency</span><span class="special">&gt;</span> <span class="identifier">f</span> <span class="special">=</span> <span class="number">1.0e3</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">hertz</span><span class="special">;</span>
   <span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">current</span><span class="special">&gt;</span> <span class="identifier">i0</span> <span class="special">=</span> <span class="number">0.5</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">ampere</span><span class="special">;</span>

   <span class="comment">// The invocation of the sin function needs to be postponed using</span>
   <span class="comment">// bind to specify the oscillation function. A lengthy static_cast</span>
   <span class="comment">// to the function pointer referencing boost::units::sin() is needed</span>
   <span class="comment">// to avoid an "unresolved overloaded function type" error.</span>
   <span class="identifier">boost</span><span class="special">::</span><span class="identifier">function</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">current</span><span class="special">&gt;</span> <span class="special">(</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">time</span><span class="special">&gt;)</span> <span class="special">&gt;</span>
       <span class="identifier">i</span> <span class="special">=</span> <span class="identifier">iamp</span>
           <span class="special">*</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">bind</span><span class="special">(</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">dimensionless_quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">system</span><span class="special">,</span> <span class="keyword">double</span><span class="special">&gt;::</span><span class="identifier">type</span> <span class="special">(*)(</span><span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">plane_angle</span><span class="special">&gt;&amp;)&gt;(</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">sin</span><span class="special">),</span>
                      <span class="number">2.0</span> <span class="special">*</span> <span class="identifier">pi</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">radian</span> <span class="special">*</span> <span class="identifier">f</span> <span class="special">*</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_1</span><span class="special">)</span>
           <span class="special">+</span> <span class="identifier">i0</span><span class="special">;</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Oscillating current:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"iamp = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">iamp</span> <span class="special">&lt;&lt;</span> <span class="string">", f = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">f</span> <span class="special">&lt;&lt;</span> <span class="string">", i0 = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">i0</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"i(1.25e-3 * si::second) = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">i</span><span class="special">(</span><span class="number">1.25e-3</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">second</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>


   <span class="comment">////////////////////////////////////////////////////////////////////////</span>
   <span class="comment">// Geometric example: area calculation for a square</span>
   <span class="comment">////////////////////////////////////////////////////////////////////////</span>

   <span class="comment">// Length constant</span>
   <span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;</span> <span class="identifier">l</span> <span class="special">=</span> <span class="number">1.5</span> <span class="special">*</span> <span class="identifier">si</span><span class="special">::</span><span class="identifier">meter</span><span class="special">;</span>

   <span class="comment">// Again an ugly static_cast is needed to bind pow&lt;2&gt; to the first</span>
   <span class="comment">// function argument.</span>
   <span class="identifier">boost</span><span class="special">::</span><span class="identifier">function</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">area</span><span class="special">&gt;</span> <span class="special">(</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;)</span> <span class="special">&gt;</span>
       <span class="identifier">A</span> <span class="special">=</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">bind</span><span class="special">(</span><span class="keyword">static_cast</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">area</span><span class="special">&gt;</span> <span class="special">(*)(</span><span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">length</span><span class="special">&gt;&amp;)&gt;(</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">pow</span><span class="special">&lt;</span><span class="number">2</span><span class="special">&gt;),</span>
                    <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_1</span><span class="special">);</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Area of a square:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"A("</span> <span class="special">&lt;&lt;</span> <span class="identifier">l</span> <span class="special">&lt;&lt;</span><span class="string">") = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">A</span><span class="special">(</span><span class="identifier">l</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>


   <span class="comment">////////////////////////////////////////////////////////////////////////</span>
   <span class="comment">// Thermal example: temperature difference of two absolute temperatures</span>
   <span class="comment">////////////////////////////////////////////////////////////////////////</span>

   <span class="comment">// Absolute temperature constants</span>
   <span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">&gt;</span>
       <span class="identifier">Tref</span> <span class="special">=</span> <span class="number">273.15</span> <span class="special">*</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;();</span>
   <span class="keyword">const</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">&gt;</span>
       <span class="identifier">Tamb</span> <span class="special">=</span> <span class="number">300.00</span> <span class="special">*</span> <span class="identifier">bu</span><span class="special">::</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;();</span>

   <span class="identifier">boost</span><span class="special">::</span><span class="identifier">function</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">(</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">&gt;,</span>
                                                  <span class="identifier">bu</span><span class="special">::</span><span class="identifier">quantity</span><span class="special">&lt;</span><span class="identifier">bu</span><span class="special">::</span><span class="identifier">absolute</span><span class="special">&lt;</span><span class="identifier">si</span><span class="special">::</span><span class="identifier">temperature</span><span class="special">&gt;</span> <span class="special">&gt;)&gt;</span>
       <span class="identifier">dT</span> <span class="special">=</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_2</span> <span class="special">-</span> <span class="identifier">bl</span><span class="special">::</span><span class="identifier">_1</span><span class="special">;</span>

   <span class="identifier">cout</span> <span class="special">&lt;&lt;</span> <span class="string">"Temperature difference of two absolute temperatures:"</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="string">"dT("</span> <span class="special">&lt;&lt;</span> <span class="identifier">Tref</span> <span class="special">&lt;&lt;</span> <span class="string">", "</span> <span class="special">&lt;&lt;</span> <span class="identifier">Tamb</span> <span class="special">&lt;&lt;</span> <span class="string">") = "</span> <span class="special">&lt;&lt;</span> <span class="identifier">dT</span><span class="special">(</span><span class="identifier">Tref</span><span class="special">,</span> <span class="identifier">Tamb</span><span class="special">)</span> <span class="special">&lt;&lt;</span> <span class="identifier">endl</span>
        <span class="special">&lt;&lt;</span> <span class="identifier">endl</span><span class="special">;</span>


   <span class="keyword">return</span> <span class="number">0</span><span class="special">;</span>
<span class="special">}</span>
</pre>
<p>
      </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2008 Matthias Christian Schabel<br>Copyright © 2007-2010 Steven
      Watanabe<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="Quantities.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_units.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="Utilities.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
