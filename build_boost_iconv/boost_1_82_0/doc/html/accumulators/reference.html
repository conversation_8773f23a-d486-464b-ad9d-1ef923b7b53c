<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Reference</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../accumulators.html" title="Chapter 1. Boost.Accumulators">
<link rel="prev" href="acknowledgements.html" title="Acknowledgements">
<link rel="next" href="../boost/accumulators/tag/droppable.html" title="Struct template droppable">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="acknowledgements.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../accumulators.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/accumulators/tag/droppable.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="section">
<div class="titlepage"><div><div><h2 class="title" style="clear: both">
<a name="accumulators.reference"></a><a class="link" href="reference.html" title="Reference">Reference</a>
</h2></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#accumulators_framework_reference">Accumulators Framework Reference</a></span></dt>
<dt><span class="section"><a href="reference.html#statistics_library_reference">Statistics Library Reference</a></span></dt>
<dt><span class="section"><a href="reference.html#numeric_operators_library_reference">Numeric Operators Library Reference</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="accumulators_framework_reference"></a>Accumulators Framework Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#header.boost.accumulators.accumulators_hpp">Header &lt;boost/accumulators/accumulators.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.accumulators_fwd_hpp">Header &lt;boost/accumulators/accumulators_fwd.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulator_base_hpp">Header &lt;boost/accumulators/framework/accumulator_base.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulator_concept_hpp">Header &lt;boost/accumulators/framework/accumulator_concept.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulator_set_hpp">Header &lt;boost/accumulators/framework/accumulator_set.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulators.droppable_accumulator_hpp">Header &lt;boost/accumulators/framework/accumulators/droppable_accumulator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulators.external_accumulator_hpp">Header &lt;boost/accumulators/framework/accumulators/external_accumulator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulators.reference_accumulator_hpp">Header &lt;boost/accumulators/framework/accumulators/reference_accumulator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.accumulators.value_accumulator_hpp">Header &lt;boost/accumulators/framework/accumulators/value_accumulator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.depends_on_hpp">Header &lt;boost/accumulators/framework/depends_on.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.extractor_hpp">Header &lt;boost/accumulators/framework/extractor.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.features_hpp">Header &lt;boost/accumulators/framework/features.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.parameters.accumulator_hpp">Header &lt;boost/accumulators/framework/parameters/accumulator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.parameters.sample_hpp">Header &lt;boost/accumulators/framework/parameters/sample.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.parameters.weight_hpp">Header &lt;boost/accumulators/framework/parameters/weight.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.framework.parameters.weights_hpp">Header &lt;boost/accumulators/framework/parameters/weights.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.accumulators_hpp"></a>Header &lt;<a href="../../../boost/accumulators/accumulators.hpp" target="_top">boost/accumulators/accumulators.hpp</a>&gt;</h4></div></div></div>
<p>Includes all of the Accumulators Framework </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.accumulators_fwd_hpp"></a>Header &lt;<a href="../../../boost/accumulators/accumulators_fwd.hpp" target="_top">boost/accumulators/accumulators_fwd.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_ACCUMU_1_3_2_6_2_3_2.html" title="Macro BOOST_ACCUMULATORS_MAX_FEATURES">BOOST_ACCUMULATORS_MAX_FEATURES</a>
<a class="link" href="../BOOST_ACCUMU_1_3_2_6_2_3_3.html" title="Macro BOOST_ACCUMULATORS_MAX_ARGS">BOOST_ACCUMULATORS_MAX_ARGS</a>
<a class="link" href="../BOOST_ACCUMU_1_3_2_6_2_3_4.html" title="Macro BOOST_ACCUMULATORS_PROTO_DISABLE_IF_IS_CONST">BOOST_ACCUMULATORS_PROTO_DISABLE_IF_IS_CONST</a>(T)
<a class="link" href="../BOOST_ACCUMU_1_3_2_6_2_3_5.html" title="Macro BOOST_ACCUMULATORS_GCC_VERSION">BOOST_ACCUMULATORS_GCC_VERSION</a>
<a class="link" href="../BOOST_ACCUMU_1_3_2_6_2_3_6.html" title="Macro BOOST_ACCUMULATORS_IGNORE_GLOBAL">BOOST_ACCUMULATORS_IGNORE_GLOBAL</a>(X)</pre>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Features<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/accumulator_set.html" title="Struct template accumulator_set">accumulator_set</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feature.html" title="Struct template as_feature">as_feature</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weighted_feature.html" title="Struct template as_weighted_feature">as_weighted_feature</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature1<span class="special">,</span> <span class="keyword">typename</span> Feature2<span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/depends_on.html" title="Struct template depends_on">depends_on</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Accumulator<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/droppable_accumulator.html" title="Struct template droppable_accumulator">droppable_accumulator</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Accumulator<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/droppable_accumulator_base.html" title="Struct template droppable_accumulator_base">droppable_accumulator_base</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_of.html" title="Struct template feature_of">feature_of</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature1<span class="special">,</span> <span class="keyword">typename</span> Feature2<span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/features.html" title="Struct template features">features</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Accumulator<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_cached_result.html" title="Struct template with_cached_result">with_cached_result</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">,</span> <span class="keyword">typename</span> AccumulatorSet<span class="special">&gt;</span> 
      <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">apply</span><span class="special">&lt;</span> <span class="identifier">AccumulatorSet</span><span class="special">,</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">::</span><span class="identifier">result_type</span> 
      <a name="boost.accumulators.extract_result"></a><span class="identifier">extract_result</span><span class="special">(</span><span class="identifier">AccumulatorSet</span> <span class="keyword">const</span> <span class="special">&amp;</span> acc<span class="special">)</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/droppable.html" title="Struct template droppable">droppable</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">,</span> <span class="keyword">typename</span> AccumulatorSet<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/external.html" title="Struct template external">external</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Referent<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/reference.html" title="Struct template reference">reference</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/reference_tag.html" title="Struct template reference_tag">reference_tag</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/value.html" title="Struct template value">value</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/value_tag.html" title="Struct template value_tag">value_tag</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulator_base_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulator_base.hpp" target="_top">boost/accumulators/framework/accumulator_base.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/accumulator_base.html" title="Struct accumulator_base">accumulator_base</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/dont_care.html" title="Struct dont_care">dont_care</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulator_concept_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulator_concept.hpp" target="_top">boost/accumulators/framework/accumulator_concept.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Stat<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/accumulator_concept.html" title="Struct template accumulator_concept">accumulator_concept</a><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulator_set_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulator_set.hpp" target="_top">boost/accumulators/framework/accumulator_set.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">,</span> <span class="keyword">typename</span> AccumulatorSet<span class="special">&gt;</span> 
      <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">apply</span><span class="special">&lt;</span> <span class="identifier">AccumulatorSet</span><span class="special">,</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <span class="special">&amp;</span> 
      <a name="boost.accumulators.find_accumulator"></a><span class="identifier">find_accumulator</span><span class="special">(</span><span class="identifier">AccumulatorSet</span> <span class="special">&amp;</span><span class="identifier">acc</span>  BOOST_ACCUMULATORS_PROTO_DISABLE_IF_IS_CONST<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulators.droppable_accumulator_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulators/droppable_accumulator.hpp" target="_top">boost/accumulators/framework/accumulators/droppable_accumulator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_featu_1_3_2_6_2_7_1_1_1.html" title="Struct template as_feature&lt;tag::droppable&lt; Feature &gt;&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">droppable</span><span class="special">&lt;</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_2_7_1_1_2.html" title="Struct template as_weighted_feature&lt;tag::droppable&lt; Feature &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">droppable</span><span class="special">&lt;</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_2_7_1_1_3.html" title="Struct template feature_of&lt;tag::droppable&lt; Feature &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">droppable</span><span class="special">&lt;</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/as_droppable.html" title="Struct template as_droppable">as_droppable</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/as_dro_1_3_2_6_2_7_1_1_4_2.html" title="Struct template as_droppable&lt;droppable&lt; Feature &gt;&gt;">as_droppable</a><span class="special">&lt;</span><span class="identifier">droppable</span><span class="special">&lt;</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulators.external_accumulator_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulators/external_accumulator.hpp" target="_top">boost/accumulators/framework/accumulators/external_accumulator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">,</span> <span class="keyword">typename</span> AccumulatorSet<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_2_8_1_1_1.html" title="Struct template feature_of&lt;tag::external&lt; Feature, Tag, AccumulatorSet &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">external</span><span class="special">&lt;</span> <span class="identifier">Feature</span><span class="special">,</span> <span class="identifier">Tag</span><span class="special">,</span> <span class="identifier">AccumulatorSet</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/extern_1_3_2_6_2_8_1_1_3_1.html" title="Struct template external&lt;Feature, Tag, void&gt;">external</a><span class="special">&lt;</span><span class="identifier">Feature</span><span class="special">,</span> <span class="identifier">Tag</span><span class="special">,</span> <span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulators.reference_accumulator_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulators/reference_accumulator.hpp" target="_top">boost/accumulators/framework/accumulators/reference_accumulator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_2_9_1_1_1.html" title="Struct template feature_of&lt;tag::reference&lt; ValueType, Tag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">reference</span><span class="special">&lt;</span> <span class="identifier">ValueType</span><span class="special">,</span> <span class="identifier">Tag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Referent<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/reference_accumulator_impl.html" title="Struct template reference_accumulator_impl">reference_accumulator_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.accumulators.value_accumulator_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/accumulators/value_accumulator.hpp" target="_top">boost/accumulators/framework/accumulators/value_accumulator.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_2_10_1_1_1.html" title="Struct template feature_of&lt;tag::value&lt; ValueType, Tag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">value</span><span class="special">&lt;</span> <span class="identifier">ValueType</span><span class="special">,</span> <span class="identifier">Tag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ValueType<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/value_accumulator_impl.html" title="Struct template value_accumulator_impl">value_accumulator_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.depends_on_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/depends_on.hpp" target="_top">boost/accumulators/framework/depends_on.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.extractor_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/extractor.hpp" target="_top">boost/accumulators/framework/extractor.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis">

<a class="link" href="../BOOST_ACCUM_1_3_2_6_2_13_2.html" title="Macro BOOST_ACCUMULATORS_DEFINE_EXTRACTOR">BOOST_ACCUMULATORS_DEFINE_EXTRACTOR</a>(Tag, Feature, ParamSeq)</pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.features_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/features.hpp" target="_top">boost/accumulators/framework/features.hpp</a>&gt;</h4></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.parameters.accumulator_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/parameters/accumulator.hpp" target="_top">boost/accumulators/framework/parameters/accumulator.hpp</a>&gt;</h4></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.parameters.sample_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/parameters/sample.hpp" target="_top">boost/accumulators/framework/parameters/sample.hpp</a>&gt;</h4></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.parameters.weight_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/parameters/weight.hpp" target="_top">boost/accumulators/framework/parameters/weight.hpp</a>&gt;</h4></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.framework.parameters.weights_hpp"></a>Header &lt;<a href="../../../boost/accumulators/framework/parameters/weights.hpp" target="_top">boost/accumulators/framework/parameters/weights.hpp</a>&gt;</h4></div></div></div></div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="statistics_library_reference"></a>Statistics Library Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics_hpp">Header &lt;boost/accumulators/statistics.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.count_hpp">Header &lt;boost/accumulators/statistics/count.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.covariance_hpp">Header &lt;boost/accumulators/statistics/covariance.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.density_hpp">Header &lt;boost/accumulators/statistics/density.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.error_of_hpp">Header &lt;boost/accumulators/statistics/error_of.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.error_of_mean_hpp">Header &lt;boost/accumulators/statistics/error_of_mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.extended_p_square_hpp">Header &lt;boost/accumulators/statistics/extended_p_square.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.extended_p_square_quantile_hpp">Header &lt;boost/accumulators/statistics/extended_p_square_quantile.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.kurtosis_hpp">Header &lt;boost/accumulators/statistics/kurtosis.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.max_hpp">Header &lt;boost/accumulators/statistics/max.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.mean_hpp">Header &lt;boost/accumulators/statistics/mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.median_hpp">Header &lt;boost/accumulators/statistics/median.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.min_hpp">Header &lt;boost/accumulators/statistics/min.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.moment_hpp">Header &lt;boost/accumulators/statistics/moment.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.p_square_cumul_dist_hpp">Header &lt;boost/accumulators/statistics/p_square_cumul_dist.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.p_square_quantile_hpp">Header &lt;boost/accumulators/statistics/p_square_quantile.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.peaks_over_threshold_hpp">Header &lt;boost/accumulators/statistics/peaks_over_threshold.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.pot_quantile_hpp">Header &lt;boost/accumulators/statistics/pot_quantile.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.pot_tail_mean_hpp">Header &lt;boost/accumulators/statistics/pot_tail_mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.rolling_count_hpp">Header &lt;boost/accumulators/statistics/rolling_count.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.rolling_mean_hpp">Header &lt;boost/accumulators/statistics/rolling_mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.rolling_moment_hpp">Header &lt;boost/accumulators/statistics/rolling_moment.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.rolling_sum_hpp">Header &lt;boost/accumulators/statistics/rolling_sum.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.rolling_variance_hpp">Header &lt;boost/accumulators/statistics/rolling_variance.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.rolling_window_hpp">Header &lt;boost/accumulators/statistics/rolling_window.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.skewness_hpp">Header &lt;boost/accumulators/statistics/skewness.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.stats_hpp">Header &lt;boost/accumulators/statistics/stats.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.sum_hpp">Header &lt;boost/accumulators/statistics/sum.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.sum_kahan_hpp">Header &lt;boost/accumulators/statistics/sum_kahan.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.tail_hpp">Header &lt;boost/accumulators/statistics/tail.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.tail_mean_hpp">Header &lt;boost/accumulators/statistics/tail_mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.tail_quantile_hpp">Header &lt;boost/accumulators/statistics/tail_quantile.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.tail_variate_hpp">Header &lt;boost/accumulators/statistics/tail_variate.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.tail_variate_means_hpp">Header &lt;boost/accumulators/statistics/tail_variate_means.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.times2_iterator_hpp">Header &lt;boost/accumulators/statistics/times2_iterator.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.variance_hpp">Header &lt;boost/accumulators/statistics/variance.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.variates.covariate_hpp">Header &lt;boost/accumulators/statistics/variates/covariate.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_covariance_hpp">Header &lt;boost/accumulators/statistics/weighted_covariance.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_density_hpp">Header &lt;boost/accumulators/statistics/weighted_density.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_extended_p_square_hpp">Header &lt;boost/accumulators/statistics/weighted_extended_p_square.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_kurtosis_hpp">Header &lt;boost/accumulators/statistics/weighted_kurtosis.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_mean_hpp">Header &lt;boost/accumulators/statistics/weighted_mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_median_hpp">Header &lt;boost/accumulators/statistics/weighted_median.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_moment_hpp">Header &lt;boost/accumulators/statistics/weighted_moment.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_p_square_cumul_dist_hpp">Header &lt;boost/accumulators/statistics/weighted_p_square_cumul_dist.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_p_square_quantile_hpp">Header &lt;boost/accumulators/statistics/weighted_p_square_quantile.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_peaks_over_threshold_hpp">Header &lt;boost/accumulators/statistics/weighted_peaks_over_threshold.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_skewness_hpp">Header &lt;boost/accumulators/statistics/weighted_skewness.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_sum_hpp">Header &lt;boost/accumulators/statistics/weighted_sum.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_sum_kahan_hpp">Header &lt;boost/accumulators/statistics/weighted_sum_kahan.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_tail_mean_hpp">Header &lt;boost/accumulators/statistics/weighted_tail_mean.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_tail_quantile_hpp">Header &lt;boost/accumulators/statistics/weighted_tail_quantile.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_tail_variate_means_hpp">Header &lt;boost/accumulators/statistics/weighted_tail_variate_means.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.weighted_variance_hpp">Header &lt;boost/accumulators/statistics/weighted_variance.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics.with_error_hpp">Header &lt;boost/accumulators/statistics/with_error.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.statistics_fwd_hpp">Header &lt;boost/accumulators/statistics_fwd.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics.hpp" target="_top">boost/accumulators/statistics.hpp</a>&gt;</h4></div></div></div>
<p>Includes all of the Statistical Accumulators Library </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.count_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/count.hpp" target="_top">boost/accumulators/statistics/count.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/count.html" title="Struct count">tag::count</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/count.html" title="Global count">count</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/count_impl.html" title="Struct count_impl">count_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/count.html" title="Struct count">count</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.covariance_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/covariance.hpp" target="_top">boost/accumulators/statistics/covariance.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_3_4_1_1_1.html" title="Struct template as_weighted_feature&lt;tag::covariance&lt; VariateType, VariateTag &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">covariance</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_4_1_1_2.html" title="Struct template feature_of&lt;tag::covariance&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">covariance</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_4_1_1_3.html" title="Struct template feature_of&lt;tag::weighted_covariance&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_covariance</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstract_covariance.html" title="Struct abstract_covariance">tag::abstract_covariance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/covariance.html" title="Global covariance">covariance</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstract_covariance.html" title="Struct abstract_covariance">abstract_covariance</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">functional</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/outer_product.html" title="Struct template outer_product">outer_product</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/outer__1_3_2_6_3_4_1_2_1_2.html" title="Struct template outer_product&lt;Left, Right, std_vector_tag, std_vector_tag&gt;">outer_product</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/outer_product_base.html" title="Struct template outer_product_base">outer_product_base</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">op</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/outer_product.html" title="Struct outer_product">outer_product</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.density_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/density.hpp" target="_top">boost/accumulators/statistics/density.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_3_5_1_1_1.html" title="Struct as_weighted_feature&lt;tag::density&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">density</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_5_1_1_2.html" title="Struct feature_of&lt;tag::weighted_density&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_density</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/density.html" title="Struct density">tag::density</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/density.html" title="Global density">density</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/density.html" title="Struct density">density</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.error_of_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/error_of.hpp" target="_top">boost/accumulators/statistics/error_of.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_featu_1_3_2_6_3_6_1_1_1.html" title="Struct template as_feature&lt;tag::error_of&lt; Feature &gt;&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">error_of</span><span class="special">&lt;</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_3_6_1_1_2.html" title="Struct template as_weighted_feature&lt;tag::error_of&lt; Feature &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">error_of</span><span class="special">&lt;</span> <span class="identifier">Feature</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.error_of_mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/error_of_mean.hpp" target="_top">boost/accumulators/statistics/error_of_mean.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.extended_p_square_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/extended_p_square.hpp" target="_top">boost/accumulators/statistics/extended_p_square.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_3_8_1_1_1.html" title="Struct as_weighted_feature&lt;tag::extended_p_square&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_8_1_1_2.html" title="Struct feature_of&lt;tag::weighted_extended_p_square&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_extended_p_square</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/extended_p_square.html" title="Struct extended_p_square">tag::extended_p_square</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/extended_p_square.html" title="Global extended_p_square">extended_p_square</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/extended_p_square.html" title="Struct extended_p_square">extended_p_square</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.extended_p_square_quantile_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/extended_p_square_quantile.hpp" target="_top">boost/accumulators/statistics/extended_p_square_quantile.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_featu_1_3_2_6_3_9_1_1_1.html" title="Struct as_feature&lt;tag::extended_p_square_quantile(linear)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square_quantile</span><span class="special">(</span><span class="identifier">linear</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_featu_1_3_2_6_3_9_1_1_2.html" title="Struct as_feature&lt;tag::extended_p_square_quantile(quadratic)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square_quantile</span><span class="special">(</span><span class="identifier">quadratic</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_featu_1_3_2_6_3_9_1_1_3.html" title="Struct as_feature&lt;tag::weighted_extended_p_square_quantile(linear)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_extended_p_square_quantile</span><span class="special">(</span><span class="identifier">linear</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_featu_1_3_2_6_3_9_1_1_4.html" title="Struct as_feature&lt;tag::weighted_extended_p_square_quantile(quadratic)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_extended_p_square_quantile</span><span class="special">(</span><span class="identifier">quadratic</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_3_9_1_1_5.html" title="Struct as_weighted_feature&lt;tag::extended_p_square_quantile&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square_quantile</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weigh_1_3_2_6_3_9_1_1_6.html" title="Struct as_weighted_feature&lt;tag::extended_p_square_quantile_quadratic&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square_quantile_quadratic</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_9_1_1_7.html" title="Struct feature_of&lt;tag::extended_p_square_quantile&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square_quantile</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_9_1_1_8.html" title="Struct feature_of&lt;tag::extended_p_square_quantile_quadratic&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">extended_p_square_quantile_quadratic</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature__1_3_2_6_3_9_1_1_9.html" title="Struct feature_of&lt;tag::weighted_extended_p_square_quantile&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_extended_p_square_quantile</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_9_1_1_10.html" title="Struct feature_of&lt;tag::weighted_extended_p_square_quantile_quadratic&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_extended_p_square_quantile_quadratic</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/extended_p_square_quantile.html" title="Struct extended_p_square_quantile">tag::extended_p_square_quantile</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/extended_p_square_quantile.html" title="Global extended_p_square_quantile">extended_p_square_quantile</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/exten_1_3_2_6_3_9_1_1_13_2.html" title="Struct extended_p_square_quantile_quadratic">tag::extended_p_square_quantile_quadratic</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/exten_1_3_2_6_3_9_1_1_11_2.html" title="Global extended_p_square_quantile_quadratic">extended_p_square_quantile_quadratic</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_9_1_1_13_3.html" title="Struct weighted_extended_p_square_quantile">tag::weighted_extended_p_square_quantile</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_9_1_1_11_3.html" title="Global weighted_extended_p_square_quantile">weighted_extended_p_square_quantile</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_9_1_1_13_4.html" title="Struct weighted_extended_p_square_quantile_quadratic">tag::weighted_extended_p_square_quantile_quadratic</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_9_1_1_11_4.html" title="Global weighted_extended_p_square_quantile_quadratic">weighted_extended_p_square_quantile_quadratic</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/extended_p_square_quantile.html" title="Struct extended_p_square_quantile">extended_p_square_quantile</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/exten_1_3_2_6_3_9_1_1_13_2.html" title="Struct extended_p_square_quantile_quadratic">extended_p_square_quantile_quadratic</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_9_1_1_13_3.html" title="Struct weighted_extended_p_square_quantile">weighted_extended_p_square_quantile</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_9_1_1_13_4.html" title="Struct weighted_extended_p_square_quantile_quadratic">weighted_extended_p_square_quantile_quadratic</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.kurtosis_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/kurtosis.hpp" target="_top">boost/accumulators/statistics/kurtosis.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_10_1_1_1.html" title="Struct as_weighted_feature&lt;tag::kurtosis&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">kurtosis</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_10_1_1_2.html" title="Struct feature_of&lt;tag::weighted_kurtosis&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_kurtosis</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/kurtosis.html" title="Struct kurtosis">tag::kurtosis</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/kurtosis.html" title="Global kurtosis">kurtosis</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/kurtosis.html" title="Struct kurtosis">kurtosis</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.max_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/max.hpp" target="_top">boost/accumulators/statistics/max.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/max.html" title="Struct max">tag::max</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/max.html" title="Global max">max</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/max.html" title="Struct max">max</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/mean.hpp" target="_top">boost/accumulators/statistics/mean.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_12_1_1_1.html" title="Struct as_feature&lt;tag::mean(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_12_1_1_2.html" title="Struct as_feature&lt;tag::mean(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_12_1_1_3.html" title="Struct template as_feature&lt;tag::mean_of_variates&lt; VariateType, VariateTag &gt;(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_12_1_1_4.html" title="Struct template as_feature&lt;tag::mean_of_variates&lt; VariateType, VariateTag &gt;(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_12_1_1_5.html" title="Struct as_feature&lt;tag::mean_of_weights(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean_of_weights</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_12_1_1_6.html" title="Struct as_feature&lt;tag::mean_of_weights(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean_of_weights</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_12_1_1_7.html" title="Struct as_weighted_feature&lt;tag::immediate_mean&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_12_1_1_8.html" title="Struct template as_weighted_feature&lt;tag::immediate_mean_of_variates&lt; VariateType, VariateTag &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_12_1_1_9.html" title="Struct as_weighted_feature&lt;tag::mean&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_wei_1_3_2_6_3_12_1_1_10.html" title="Struct template as_weighted_feature&lt;tag::mean_of_variates&lt; VariateType, VariateTag &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_11.html" title="Struct feature_of&lt;tag::immediate_mean&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_12.html" title="Struct template feature_of&lt;tag::immediate_mean_of_variates&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_13.html" title="Struct feature_of&lt;tag::immediate_mean_of_weights&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_mean_of_weights</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_14.html" title="Struct feature_of&lt;tag::immediate_weighted_mean&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_weighted_mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_15.html" title="Struct template feature_of&lt;tag::immediate_weighted_mean_of_variates&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_weighted_mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_16.html" title="Struct feature_of&lt;tag::weighted_mean&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_12_1_1_17.html" title="Struct template feature_of&lt;tag::weighted_mean_of_variates&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/mean.html" title="Struct mean">tag::mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/mean.html" title="Global mean">mean</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/mean_of_weights.html" title="Struct mean_of_weights">tag::mean_of_weights</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/mean_of_weights.html" title="Global mean_of_weights">mean_of_weights</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/immediate_mean.html" title="Struct immediate_mean">immediate_mean</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/immediate_mean_of_weights.html" title="Struct immediate_mean_of_weights">immediate_mean_of_weights</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/mean.html" title="Struct mean">mean</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/mean_of_weights.html" title="Struct mean_of_weights">mean_of_weights</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.median_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/median.hpp" target="_top">boost/accumulators/statistics/median.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_13_1_1_1.html" title="Struct as_feature&lt;tag::median(with_density)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">median</span><span class="special">(</span><span class="identifier">with_density</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_13_1_1_2.html" title="Struct as_feature&lt;tag::median(with_p_square_cumulative_distribution)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">median</span><span class="special">(</span><span class="identifier">with_p_square_cumulative_distribution</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_13_1_1_3.html" title="Struct as_feature&lt;tag::median(with_p_square_quantile)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">median</span><span class="special">(</span><span class="identifier">with_p_square_quantile</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_13_1_1_4.html" title="Struct as_weighted_feature&lt;tag::median&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_13_1_1_5.html" title="Struct as_weighted_feature&lt;tag::with_density_median&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">with_density_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_13_1_1_6.html" title="Struct as_weighted_feature&lt;tag::with_p_square_cumulative_distribution_median&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">with_p_square_cumulative_distribution_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_13_1_1_7.html" title="Struct feature_of&lt;tag::weighted_median&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_13_1_1_8.html" title="Struct feature_of&lt;tag::with_density_median&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">with_density_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_13_1_1_9.html" title="Struct feature_of&lt;tag::with_density_weighted_median&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">with_density_weighted_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_13_1_1_10.html" title="Struct feature_of&lt;tag::with_p_square_cumulative_distribution_median&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">with_p_square_cumulative_distribution_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_13_1_1_11.html" title="Struct feature_of&lt;tag::with_p_square_cumulative_distribution_weighted_median&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">with_p_square_cumulative_distribution_weighted_median</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/median.html" title="Struct median">tag::median</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/median.html" title="Global median">median</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/with_density_median.html" title="Struct with_density_median">tag::with_density_median</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/with_density_median.html" title="Global with_density_median">with_density_median</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/with_1_3_2_6_3_13_1_1_14_3.html" title="Struct with_p_square_cumulative_distribution_median">tag::with_p_square_cumulative_distribution_median</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/with_1_3_2_6_3_13_1_1_12_3.html" title="Global with_p_square_cumulative_distribution_median">with_p_square_cumulative_distribution_median</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/median.html" title="Struct median">median</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/with_density_median.html" title="Struct with_density_median">with_density_median</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/with_1_3_2_6_3_13_1_1_14_3.html" title="Struct with_p_square_cumulative_distribution_median">with_p_square_cumulative_distribution_median</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.min_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/min.hpp" target="_top">boost/accumulators/statistics/min.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/min.html" title="Struct min">tag::min</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/min.html" title="Global min">min</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/min.html" title="Struct min">min</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.moment_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/moment.hpp" target="_top">boost/accumulators/statistics/moment.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">int</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_15_1_1_1.html" title="Struct template as_weighted_feature&lt;tag::moment&lt; N &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">moment</span><span class="special">&lt;</span> <span class="identifier">N</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">int</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_15_1_1_2.html" title="Struct template feature_of&lt;tag::weighted_moment&lt; N &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_moment</span><span class="special">&lt;</span> <span class="identifier">N</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.p_square_cumul_dist_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/p_square_cumul_dist.hpp" target="_top">boost/accumulators/statistics/p_square_cumul_dist.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_16_1_1_1.html" title="Struct as_weighted_feature&lt;tag::p_square_cumulative_distribution&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">p_square_cumulative_distribution</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_16_1_1_2.html" title="Struct feature_of&lt;tag::weighted_p_square_cumulative_distribution&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_p_square_cumulative_distribution</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/p_squ_1_3_2_6_3_16_1_1_5_1.html" title="Struct p_square_cumulative_distribution">tag::p_square_cumulative_distribution</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/p_squ_1_3_2_6_3_16_1_1_3_1.html" title="Global p_square_cumulative_distribution">p_square_cumulative_distribution</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/p_squ_1_3_2_6_3_16_1_1_5_1.html" title="Struct p_square_cumulative_distribution">p_square_cumulative_distribution</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.p_square_quantile_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/p_square_quantile.hpp" target="_top">boost/accumulators/statistics/p_square_quantile.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_18_1_1_1.html" title="Struct as_weighted_feature&lt;tag::p_square_quantile&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">p_square_quantile</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_18_1_1_2.html" title="Struct feature_of&lt;tag::weighted_p_square_quantile&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_p_square_quantile</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/p_square_quantile.html" title="Struct p_square_quantile">tag::p_square_quantile</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/p_square_quantile.html" title="Global p_square_quantile">p_square_quantile</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/p_squ_1_3_2_6_3_18_1_1_5_2.html" title="Struct p_square_quantile_for_median">tag::p_square_quantile_for_median</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/p_squ_1_3_2_6_3_18_1_1_3_2.html" title="Global p_square_quantile_for_median">p_square_quantile_for_median</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/p_square_quantile.html" title="Struct p_square_quantile">p_square_quantile</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/p_squ_1_3_2_6_3_18_1_1_5_2.html" title="Struct p_square_quantile_for_median">p_square_quantile_for_median</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.peaks_over_threshold_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/peaks_over_threshold.hpp" target="_top">boost/accumulators/statistics/peaks_over_threshold.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_19_1_1_1.html" title="Struct template as_feature&lt;tag::peaks_over_threshold&lt; LeftRight &gt;(with_threshold_probability)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_probability</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_19_1_1_2.html" title="Struct template as_feature&lt;tag::peaks_over_threshold&lt; LeftRight &gt;(with_threshold_value)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_value</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_19_1_1_3.html" title="Struct template as_weighted_feature&lt;tag::peaks_over_threshold&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_19_1_1_4.html" title="Struct template as_weighted_feature&lt;tag::peaks_over_threshold_prob&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">peaks_over_threshold_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_19_1_1_5.html" title="Struct template feature_of&lt;tag::peaks_over_threshold&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_19_1_1_6.html" title="Struct template feature_of&lt;tag::peaks_over_threshold_prob&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">peaks_over_threshold_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_19_1_1_7.html" title="Struct template feature_of&lt;tag::weighted_peaks_over_threshold&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_19_1_1_8.html" title="Struct template feature_of&lt;tag::weighted_peaks_over_threshold_prob&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_peaks_over_threshold_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_19_1_1_11_1.html" title="Struct abstract_peaks_over_threshold">tag::abstract_peaks_over_threshold</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/peaks_over_threshold.html" title="Global peaks_over_threshold">peaks_over_threshold</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/peaks_over_threshold_impl.html" title="Struct template peaks_over_threshold_impl">peaks_over_threshold_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_19_1_1_11_1.html" title="Struct abstract_peaks_over_threshold">abstract_peaks_over_threshold</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.pot_quantile_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/pot_quantile.hpp" target="_top">boost/accumulators/statistics/pot_quantile.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_20_1_1_1.html" title="Struct template as_feature&lt;tag::pot_quantile&lt; LeftRight &gt;(with_threshold_probability)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_probability</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_20_1_1_2.html" title="Struct template as_feature&lt;tag::pot_quantile&lt; LeftRight &gt;(with_threshold_value)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_value</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_20_1_1_3.html" title="Struct template as_feature&lt;tag::weighted_pot_quantile&lt; LeftRight &gt;(with_threshold_probability)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_probability</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_20_1_1_4.html" title="Struct template as_feature&lt;tag::weighted_pot_quantile&lt; LeftRight &gt;(with_threshold_value)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_value</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_20_1_1_5.html" title="Struct template as_weighted_feature&lt;tag::pot_quantile&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_20_1_1_6.html" title="Struct template as_weighted_feature&lt;tag::pot_quantile_prob&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_quantile_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_20_1_1_7.html" title="Struct template feature_of&lt;tag::pot_quantile&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_20_1_1_8.html" title="Struct template feature_of&lt;tag::pot_quantile_prob&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_quantile_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_20_1_1_9.html" title="Struct template feature_of&lt;tag::weighted_pot_quantile&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_20_1_1_10.html" title="Struct template feature_of&lt;tag::weighted_pot_quantile_prob&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_quantile_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.pot_tail_mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/pot_tail_mean.hpp" target="_top">boost/accumulators/statistics/pot_tail_mean.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_21_1_1_1.html" title="Struct template as_feature&lt;tag::pot_tail_mean&lt; LeftRight &gt;(with_threshold_probability)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_probability</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_21_1_1_2.html" title="Struct template as_feature&lt;tag::pot_tail_mean&lt; LeftRight &gt;(with_threshold_value)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_value</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_21_1_1_3.html" title="Struct template as_feature&lt;tag::weighted_pot_tail_mean&lt; LeftRight &gt;(with_threshold_probability)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_probability</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_21_1_1_4.html" title="Struct template as_feature&lt;tag::weighted_pot_tail_mean&lt; LeftRight &gt;(with_threshold_value)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_value</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_21_1_1_5.html" title="Struct template as_weighted_feature&lt;tag::pot_tail_mean&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_21_1_1_6.html" title="Struct template as_weighted_feature&lt;tag::pot_tail_mean_prob&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_tail_mean_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_21_1_1_7.html" title="Struct template feature_of&lt;tag::pot_tail_mean&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_21_1_1_8.html" title="Struct template feature_of&lt;tag::pot_tail_mean_prob&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">pot_tail_mean_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_21_1_1_9.html" title="Struct template feature_of&lt;tag::weighted_pot_tail_mean&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/featur_1_3_2_6_3_21_1_1_10.html" title="Struct template feature_of&lt;tag::weighted_pot_tail_mean_prob&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_pot_tail_mean_prob</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.rolling_count_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/rolling_count.hpp" target="_top">boost/accumulators/statistics/rolling_count.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/rolling_count.html" title="Struct rolling_count">tag::rolling_count</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/rolling_count.html" title="Global rolling_count">rolling_count</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_count.html" title="Struct rolling_count">rolling_count</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.rolling_mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/rolling_mean.hpp" target="_top">boost/accumulators/statistics/rolling_mean.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_23_1_1_1.html" title="Struct as_feature&lt;tag::rolling_mean(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">rolling_mean</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_23_1_1_2.html" title="Struct as_feature&lt;tag::rolling_mean(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">rolling_mean</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_23_1_1_3.html" title="Struct feature_of&lt;tag::immediate_rolling_mean&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_rolling_mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_23_1_1_4.html" title="Struct feature_of&lt;tag::lazy_rolling_mean&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">lazy_rolling_mean</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/lazy_rolling_mean.html" title="Struct lazy_rolling_mean">tag::lazy_rolling_mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/lazy_rolling_mean.html" title="Global lazy_rolling_mean">lazy_rolling_mean</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/immediate_rolling_mean.html" title="Struct immediate_rolling_mean">tag::immediate_rolling_mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/immediate_rolling_mean.html" title="Global immediate_rolling_mean">immediate_rolling_mean</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/rolling_mean.html" title="Struct rolling_mean">tag::rolling_mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/rolling_mean.html" title="Global rolling_mean">rolling_mean</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/immed_1_3_2_6_3_23_1_1_6_1.html" title="Struct template immediate_rolling_mean_impl">immediate_rolling_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/lazy_rolling_mean_impl.html" title="Struct template lazy_rolling_mean_impl">lazy_rolling_mean_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/immediate_rolling_mean.html" title="Struct immediate_rolling_mean">immediate_rolling_mean</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/lazy_rolling_mean.html" title="Struct lazy_rolling_mean">lazy_rolling_mean</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_mean.html" title="Struct rolling_mean">rolling_mean</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.rolling_moment_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/rolling_moment.hpp" target="_top">boost/accumulators/statistics/rolling_moment.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> N<span class="special">,</span> <span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/rolling_moment_impl.html" title="Struct template rolling_moment_impl">rolling_moment_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">int</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_moment.html" title="Struct template rolling_moment">rolling_moment</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.rolling_sum_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/rolling_sum.hpp" target="_top">boost/accumulators/statistics/rolling_sum.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/rolling_sum.html" title="Struct rolling_sum">tag::rolling_sum</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/rolling_sum.html" title="Global rolling_sum">rolling_sum</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_sum.html" title="Struct rolling_sum">rolling_sum</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.rolling_variance_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/rolling_variance.hpp" target="_top">boost/accumulators/statistics/rolling_variance.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_26_1_1_1.html" title="Struct as_feature&lt;tag::rolling_variance(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">rolling_variance</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_26_1_1_2.html" title="Struct as_feature&lt;tag::rolling_variance(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">rolling_variance</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_26_1_1_3.html" title="Struct feature_of&lt;tag::immediate_rolling_variance&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">immediate_rolling_variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_26_1_1_4.html" title="Struct feature_of&lt;tag::lazy_rolling_variance&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">lazy_rolling_variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/lazy_rolling_variance.html" title="Struct lazy_rolling_variance">tag::lazy_rolling_variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/lazy_rolling_variance.html" title="Global lazy_rolling_variance">lazy_rolling_variance</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/immediate_rolling_variance.html" title="Struct immediate_rolling_variance">tag::immediate_rolling_variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/immediate_rolling_variance.html" title="Global immediate_rolling_variance">immediate_rolling_variance</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/rolling_variance.html" title="Struct rolling_variance">tag::rolling_variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/rolling_variance.html" title="Global rolling_variance">rolling_variance</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/immed_1_3_2_6_3_26_1_1_6_1.html" title="Struct template immediate_rolling_variance_impl">immediate_rolling_variance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/lazy_rolling_variance_impl.html" title="Struct template lazy_rolling_variance_impl">lazy_rolling_variance_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/immediate_rolling_variance.html" title="Struct immediate_rolling_variance">immediate_rolling_variance</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/lazy_rolling_variance.html" title="Struct lazy_rolling_variance">lazy_rolling_variance</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_variance.html" title="Struct rolling_variance">rolling_variance</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.rolling_window_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/rolling_window.hpp" target="_top">boost/accumulators/statistics/rolling_window.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/rolling_window_plus1.html" title="Struct rolling_window_plus1">tag::rolling_window_plus1</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/rolling_window_plus1.html" title="Global rolling_window_plus1">rolling_window_plus1</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/rolling_window.html" title="Struct rolling_window">tag::rolling_window</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/rolling_window.html" title="Global rolling_window">rolling_window</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Args<span class="special">&gt;</span> 
        <span class="keyword">bool</span> <a name="boost.accumulators.impl.is_ro_1_3_2_6_3_27_1_1_2_1"></a><span class="identifier">is_rolling_window_plus1_full</span><span class="special">(</span><span class="identifier">Args</span> <span class="keyword">const</span> <span class="special">&amp;</span> args<span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_window.html" title="Struct rolling_window">rolling_window</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rolling_window_plus1.html" title="Struct rolling_window_plus1">rolling_window_plus1</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
  <span class="keyword">namespace</span> <span class="identifier">serialization</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.save_1_3_2_6_3_27_1_2_1"></a><span class="identifier">save</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">circular_buffer</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span> b<span class="special">,</span> 
                <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.load_1_3_2_6_3_27_1_2_2"></a><span class="identifier">load</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="identifier">circular_buffer</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span> b<span class="special">,</span> <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span><span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Archive<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">&gt;</span> 
      <span class="keyword">void</span> <a name="boost.serialization.seriali_1_3_2_6_3_27_1_2_3"></a><span class="identifier">serialize</span><span class="special">(</span><span class="identifier">Archive</span> <span class="special">&amp;</span> ar<span class="special">,</span> <span class="identifier">circular_buffer</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&amp;</span> b<span class="special">,</span> 
                     <span class="keyword">const</span> <span class="keyword">unsigned</span> <span class="keyword">int</span> version<span class="special">)</span><span class="special">;</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.skewness_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/skewness.hpp" target="_top">boost/accumulators/statistics/skewness.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_28_1_1_1.html" title="Struct as_weighted_feature&lt;tag::skewness&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">skewness</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_28_1_1_2.html" title="Struct feature_of&lt;tag::weighted_skewness&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_skewness</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/skewness.html" title="Struct skewness">tag::skewness</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/skewness.html" title="Global skewness">skewness</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/skewness.html" title="Struct skewness">skewness</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.stats_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/stats.hpp" target="_top">boost/accumulators/statistics/stats.hpp</a>&gt;</h4></div></div></div>
<p>Contains the stats&lt;&gt; template. </p>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.sum_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/sum.hpp" target="_top">boost/accumulators/statistics/sum.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_30_1_1_1.html" title="Struct as_weighted_feature&lt;tag::sum&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_30_1_1_2.html" title="Struct template feature_of&lt;tag::sum_of_variates&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_30_1_1_3.html" title="Struct feature_of&lt;tag::weighted_sum&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_sum</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/sum.html" title="Struct sum">tag::sum</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/sum.html" title="Global sum">sum</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/sum_of_weights.html" title="Struct sum_of_weights">tag::sum_of_weights</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/sum_of_weights.html" title="Global sum_of_weights">sum_of_weights</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstract_sum_of_variates.html" title="Struct abstract_sum_of_variates">tag::abstract_sum_of_variates</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/sum_of_variates.html" title="Global sum_of_variates">sum_of_variates</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstract_sum_of_variates.html" title="Struct abstract_sum_of_variates">abstract_sum_of_variates</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/sum.html" title="Struct sum">sum</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/sum_of_weights.html" title="Struct sum_of_weights">sum_of_weights</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.sum_kahan_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/sum_kahan.hpp" target="_top">boost/accumulators/statistics/sum_kahan.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_31_1_1_1.html" title="Struct as_feature&lt;tag::sum(kahan)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum</span><span class="special">(</span><span class="identifier">kahan</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_31_1_1_2.html" title="Struct as_feature&lt;tag::sum_of_weights(kahan)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum_of_weights</span><span class="special">(</span><span class="identifier">kahan</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_31_1_1_3.html" title="Struct as_weighted_feature&lt;tag::sum_kahan&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum_kahan</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_31_1_1_4.html" title="Struct feature_of&lt;tag::sum_kahan&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum_kahan</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_31_1_1_5.html" title="Struct template feature_of&lt;tag::sum_of_variates_kahan&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum_of_variates_kahan</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_31_1_1_6.html" title="Struct feature_of&lt;tag::sum_of_weights_kahan&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">sum_of_weights_kahan</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_31_1_1_7.html" title="Struct feature_of&lt;tag::weighted_sum_kahan&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_sum_kahan</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/sum_kahan.html" title="Struct sum_kahan">tag::sum_kahan</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/sum_kahan.html" title="Global sum_kahan">sum_kahan</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/sum_of_weights_kahan.html" title="Struct sum_of_weights_kahan">tag::sum_of_weights_kahan</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/sum_of_weights_kahan.html" title="Global sum_of_weights_kahan">sum_of_weights_kahan</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstract_sum_of_variates.html" title="Struct abstract_sum_of_variates">tag::abstract_sum_of_variates</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/sum_of_variates_kahan.html" title="Global sum_of_variates_kahan">sum_of_variates_kahan</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/sum_kahan.html" title="Struct sum_kahan">sum_kahan</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/sum_of_weights_kahan.html" title="Struct sum_of_weights_kahan">sum_of_weights_kahan</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.tail_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/tail.hpp" target="_top">boost/accumulators/statistics/tail.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_32_1_1_1.html" title="Struct template feature_of&lt;tag::tail&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tail_cache_size_named_arg.html" title="Struct template tail_cache_size_named_arg">tail_cache_size_named_arg</a><span class="special">;</span>

    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tail_ca_1_3_2_6_3_32_1_1_3.html" title="Struct tail_cache_size_named_arg&lt;left&gt;">tail_cache_size_named_arg</a><span class="special">&lt;</span><span class="identifier">left</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tail_ca_1_3_2_6_3_32_1_1_4.html" title="Struct tail_cache_size_named_arg&lt;right&gt;">tail_cache_size_named_arg</a><span class="special">&lt;</span><span class="identifier">right</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstract_tail.html" title="Struct abstract_tail">tag::abstract_tail</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/tail.html" title="Global tail">tail</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstract_tail.html" title="Struct abstract_tail">abstract_tail</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.tail_mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/tail_mean.hpp" target="_top">boost/accumulators/statistics/tail_mean.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_33_1_1_1.html" title="Struct template as_weighted_feature&lt;tag::non_coherent_tail_mean&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">non_coherent_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_33_1_1_2.html" title="Struct template feature_of&lt;tag::coherent_tail_mean&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">coherent_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_33_1_1_3.html" title="Struct template feature_of&lt;tag::non_coherent_tail_mean&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">non_coherent_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_33_1_1_4.html" title="Struct template feature_of&lt;tag::non_coherent_weighted_tail_mean&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">non_coherent_weighted_tail_mean</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstr_1_3_2_6_3_33_1_1_7_1.html" title="Struct abstract_non_coherent_tail_mean">tag::abstract_non_coherent_tail_mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/non_coherent_tail_mean.html" title="Global non_coherent_tail_mean">non_coherent_tail_mean</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/tail_mean.html" title="Struct tail_mean">tag::tail_mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/coherent_tail_mean.html" title="Global coherent_tail_mean">coherent_tail_mean</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstr_1_3_2_6_3_33_1_1_7_1.html" title="Struct abstract_non_coherent_tail_mean">abstract_non_coherent_tail_mean</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.tail_quantile_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/tail_quantile.hpp" target="_top">boost/accumulators/statistics/tail_quantile.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_34_1_1_1.html" title="Struct template as_weighted_feature&lt;tag::tail_quantile&lt; LeftRight &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_34_1_1_2.html" title="Struct template feature_of&lt;tag::tail_quantile&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_34_1_1_3.html" title="Struct template feature_of&lt;tag::weighted_tail_quantile&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_tail_quantile</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/quantile.html" title="Struct quantile">tag::quantile</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/tail_quantile.html" title="Global tail_quantile">tail_quantile</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.tail_variate_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/tail_variate.hpp" target="_top">boost/accumulators/statistics/tail_variate.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_35_1_1_1.html" title="Struct template feature_of&lt;tag::tail_variate&lt; VariateType, VariateTag, LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail_variate</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span><span class="special">,</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_35_1_1_2.html" title="Struct template feature_of&lt;tag::tail_weights&lt; LeftRight &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail_weights</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstract_tail_variate.html" title="Struct abstract_tail_variate">tag::abstract_tail_variate</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/tail_variate.html" title="Global tail_variate">tail_variate</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstract_tail_weights.html" title="Struct abstract_tail_weights">tag::abstract_tail_weights</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/tail_weights.html" title="Global tail_weights">tail_weights</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstract_tail_variate.html" title="Struct abstract_tail_variate">abstract_tail_variate</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstract_tail_weights.html" title="Struct abstract_tail_weights">abstract_tail_weights</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.tail_variate_means_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/tail_variate_means.hpp" target="_top">boost/accumulators/statistics/tail_variate_means.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_36_1_1_1.html" title="Struct template as_feature&lt;tag::tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;(absolute)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">absolute</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_36_1_1_2.html" title="Struct template as_feature&lt;tag::tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;(relative)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">relative</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_36_1_1_3.html" title="Struct template as_weighted_feature&lt;tag::absolute_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">absolute_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_36_1_1_4.html" title="Struct template as_weighted_feature&lt;tag::relative_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">relative_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_36_1_1_5.html" title="Struct template feature_of&lt;tag::absolute_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">absolute_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_36_1_1_6.html" title="Struct template feature_of&lt;tag::absolute_weighted_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">absolute_weighted_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_36_1_1_7.html" title="Struct template feature_of&lt;tag::relative_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">relative_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_36_1_1_8.html" title="Struct template feature_of&lt;tag::relative_weighted_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">relative_weighted_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_36_1_1_11_1.html" title="Struct abstract_absolute_tail_variate_means">tag::abstract_absolute_tail_variate_means</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/tail_variate_means.html" title="Global tail_variate_means">tail_variate_means</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_36_1_1_11_2.html" title="Struct abstract_relative_tail_variate_means">tag::abstract_relative_tail_variate_means</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/relat_1_3_2_6_3_36_1_1_9_2.html" title="Global relative_tail_variate_means">relative_tail_variate_means</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_36_1_1_11_1.html" title="Struct abstract_absolute_tail_variate_means">abstract_absolute_tail_variate_means</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_36_1_1_11_2.html" title="Struct abstract_relative_tail_variate_means">abstract_relative_tail_variate_means</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.times2_iterator_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/times2_iterator.hpp" target="_top">boost/accumulators/statistics/times2_iterator.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.variance_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/variance.hpp" target="_top">boost/accumulators/statistics/variance.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_38_1_1_1.html" title="Struct as_feature&lt;tag::variance(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">variance</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_38_1_1_2.html" title="Struct as_feature&lt;tag::variance(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">variance</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_38_1_1_3.html" title="Struct as_weighted_feature&lt;tag::lazy_variance&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">lazy_variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_weig_1_3_2_6_3_38_1_1_4.html" title="Struct as_weighted_feature&lt;tag::variance&gt;">as_weighted_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_38_1_1_5.html" title="Struct feature_of&lt;tag::lazy_variance&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">lazy_variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_38_1_1_6.html" title="Struct feature_of&lt;tag::lazy_weighted_variance&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">lazy_weighted_variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_38_1_1_7.html" title="Struct feature_of&lt;tag::weighted_variance&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_variance</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/lazy_variance.html" title="Struct lazy_variance">tag::lazy_variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/lazy_variance.html" title="Global lazy_variance">lazy_variance</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/variance.html" title="Struct variance">tag::variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/variance.html" title="Global variance">variance</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/lazy_variance.html" title="Struct lazy_variance">lazy_variance</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/variance.html" title="Struct variance">variance</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.variates.covariate_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/variates/covariate.hpp" target="_top">boost/accumulators/statistics/variates/covariate.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">parameter</span><span class="special">::</span><span class="identifier">keyword</span><span class="special">&lt;</span> <span class="identifier">tag</span> <span class="special">::</span><span class="identifier">covariate1</span> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/covariate1.html" title="Global covariate1">covariate1</a><span class="special">;</span>
    <span class="identifier">boost</span><span class="special">::</span><span class="identifier">parameter</span><span class="special">::</span><span class="identifier">keyword</span><span class="special">&lt;</span> <span class="identifier">tag</span> <span class="special">::</span><span class="identifier">covariate2</span> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/covariate2.html" title="Global covariate2">covariate2</a><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/covariate1.html" title="Struct covariate1">covariate1</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/covariate2.html" title="Struct covariate2">covariate2</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_covariance_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_covariance.hpp" target="_top">boost/accumulators/statistics/weighted_covariance.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_density_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_density.hpp" target="_top">boost/accumulators/statistics/weighted_density.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/density.html" title="Struct density">tag::density</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_density.html" title="Global weighted_density">weighted_density</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_density.html" title="Struct weighted_density">weighted_density</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_extended_p_square_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_extended_p_square.hpp" target="_top">boost/accumulators/statistics/weighted_extended_p_square.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_extended_p_square.html" title="Struct weighted_extended_p_square">tag::weighted_extended_p_square</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_extended_p_square.html" title="Global weighted_extended_p_square">weighted_extended_p_square</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_extended_p_square.html" title="Struct weighted_extended_p_square">weighted_extended_p_square</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_kurtosis_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_kurtosis.hpp" target="_top">boost/accumulators/statistics/weighted_kurtosis.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_kurtosis.html" title="Struct weighted_kurtosis">tag::weighted_kurtosis</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_kurtosis.html" title="Global weighted_kurtosis">weighted_kurtosis</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_kurtosis.html" title="Struct weighted_kurtosis">weighted_kurtosis</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_mean.hpp" target="_top">boost/accumulators/statistics/weighted_mean.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_44_1_1_1.html" title="Struct as_feature&lt;tag::weighted_mean(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_mean</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_44_1_1_2.html" title="Struct as_feature&lt;tag::weighted_mean(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_mean</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_44_1_1_3.html" title="Struct template as_feature&lt;tag::weighted_mean_of_variates&lt; VariateType, VariateTag &gt;(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_44_1_1_4.html" title="Struct template as_feature&lt;tag::weighted_mean_of_variates&lt; VariateType, VariateTag &gt;(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_mean_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/mean.html" title="Struct mean">tag::mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_mean.html" title="Global weighted_mean">weighted_mean</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/immediate_weighted_mean.html" title="Struct immediate_weighted_mean">immediate_weighted_mean</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_mean.html" title="Struct weighted_mean">weighted_mean</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_median_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_median.hpp" target="_top">boost/accumulators/statistics/weighted_median.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_45_1_1_1.html" title="Struct as_feature&lt;tag::weighted_median(with_density)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_median</span><span class="special">(</span><span class="identifier">with_density</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_45_1_1_2.html" title="Struct as_feature&lt;tag::weighted_median(with_p_square_cumulative_distribution)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_median</span><span class="special">(</span><span class="identifier">with_p_square_cumulative_distribution</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_45_1_1_3.html" title="Struct as_feature&lt;tag::weighted_median(with_p_square_quantile)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_median</span><span class="special">(</span><span class="identifier">with_p_square_quantile</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/median.html" title="Struct median">tag::median</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_median.html" title="Global weighted_median">weighted_median</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_median.html" title="Struct weighted_median">weighted_median</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/with__1_3_2_6_3_45_1_1_6_2.html" title="Struct with_density_weighted_median">with_density_weighted_median</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/with__1_3_2_6_3_45_1_1_6_3.html" title="Struct with_p_square_cumulative_distribution_weighted_median">with_p_square_cumulative_distribution_weighted_median</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_moment_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_moment.hpp" target="_top">boost/accumulators/statistics/weighted_moment.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_p_square_cumul_dist_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_p_square_cumul_dist.hpp" target="_top">boost/accumulators/statistics/weighted_p_square_cumul_dist.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_47_1_1_3_1.html" title="Struct weighted_p_square_cumulative_distribution">tag::weighted_p_square_cumulative_distribution</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_47_1_1_1_1.html" title="Global weighted_p_square_cumulative_distribution">weighted_p_square_cumulative_distribution</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_47_1_1_3_1.html" title="Struct weighted_p_square_cumulative_distribution">weighted_p_square_cumulative_distribution</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_p_square_quantile_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_p_square_quantile.hpp" target="_top">boost/accumulators/statistics/weighted_p_square_quantile.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_p_square_quantile.html" title="Struct weighted_p_square_quantile">tag::weighted_p_square_quantile</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_p_square_quantile.html" title="Global weighted_p_square_quantile">weighted_p_square_quantile</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_49_1_1_3_2.html" title="Struct weighted_p_square_quantile_for_median">tag::weighted_p_square_quantile_for_median</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_49_1_1_1_2.html" title="Global weighted_p_square_quantile_for_median">weighted_p_square_quantile_for_median</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_p_square_quantile.html" title="Struct weighted_p_square_quantile">weighted_p_square_quantile</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_49_1_1_3_2.html" title="Struct weighted_p_square_quantile_for_median">weighted_p_square_quantile_for_median</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_peaks_over_threshold_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_peaks_over_threshold.hpp" target="_top">boost/accumulators/statistics/weighted_peaks_over_threshold.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_50_1_1_1.html" title="Struct template as_feature&lt;tag::weighted_peaks_over_threshold&lt; LeftRight &gt;(with_threshold_probability)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_probability</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_50_1_1_2.html" title="Struct template as_feature&lt;tag::weighted_peaks_over_threshold&lt; LeftRight &gt;(with_threshold_value)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_peaks_over_threshold</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">with_threshold_value</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_19_1_1_11_1.html" title="Struct abstract_peaks_over_threshold">tag::abstract_peaks_over_threshold</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_50_1_1_3_1.html" title="Global weighted_peaks_over_threshold">weighted_peaks_over_threshold</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_skewness_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_skewness.hpp" target="_top">boost/accumulators/statistics/weighted_skewness.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_skewness.html" title="Struct weighted_skewness">tag::weighted_skewness</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_skewness.html" title="Global weighted_skewness">weighted_skewness</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_skewness.html" title="Struct weighted_skewness">weighted_skewness</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_sum_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_sum.hpp" target="_top">boost/accumulators/statistics/weighted_sum.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_52_1_1_1.html" title="Struct template feature_of&lt;tag::weighted_sum_of_variates&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_sum_of_variates</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_sum.html" title="Struct weighted_sum">tag::weighted_sum</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_sum.html" title="Global weighted_sum">weighted_sum</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstr_1_3_2_6_3_52_1_1_4_1.html" title="Struct abstract_weighted_sum_of_variates">tag::abstract_weighted_sum_of_variates</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_sum_of_variates.html" title="Global weighted_sum_of_variates">weighted_sum_of_variates</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abstr_1_3_2_6_3_52_1_1_4_1.html" title="Struct abstract_weighted_sum_of_variates">abstract_weighted_sum_of_variates</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_sum.html" title="Struct weighted_sum">weighted_sum</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_sum_kahan_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_sum_kahan.hpp" target="_top">boost/accumulators/statistics/weighted_sum_kahan.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_53_1_1_1.html" title="Struct as_feature&lt;tag::weighted_sum(kahan)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_sum</span><span class="special">(</span><span class="identifier">kahan</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/feature_1_3_2_6_3_53_1_1_2.html" title="Struct template feature_of&lt;tag::weighted_sum_of_variates_kahan&lt; VariateType, VariateTag &gt;&gt;">feature_of</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_sum_of_variates_kahan</span><span class="special">&lt;</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_sum_kahan.html" title="Struct weighted_sum_kahan">tag::weighted_sum_kahan</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_sum_kahan.html" title="Global weighted_sum_kahan">weighted_sum_kahan</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abstr_1_3_2_6_3_52_1_1_4_1.html" title="Struct abstract_weighted_sum_of_variates">tag::abstract_weighted_sum_of_variates</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_53_1_1_3_2.html" title="Global weighted_sum_of_variates_kahan">weighted_sum_of_variates_kahan</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_sum_kahan.html" title="Struct weighted_sum_kahan">weighted_sum_kahan</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weigh_1_3_2_6_3_53_1_1_5_2.html" title="Struct template weighted_sum_of_variates_kahan">weighted_sum_of_variates_kahan</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_tail_mean_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_tail_mean.hpp" target="_top">boost/accumulators/statistics/weighted_tail_mean.hpp</a>&gt;</h4></div></div></div></div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_tail_quantile_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_tail_quantile.hpp" target="_top">boost/accumulators/statistics/weighted_tail_quantile.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_tail_variate_means_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_tail_variate_means.hpp" target="_top">boost/accumulators/statistics/weighted_tail_variate_means.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_56_1_1_1.html" title="Struct template as_feature&lt;tag::weighted_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;(absolute)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">absolute</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_56_1_1_2.html" title="Struct template as_feature&lt;tag::weighted_tail_variate_means&lt; LeftRight, VariateType, VariateTag &gt;(relative)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_tail_variate_means</span><span class="special">&lt;</span> <span class="identifier">LeftRight</span><span class="special">,</span> <span class="identifier">VariateType</span><span class="special">,</span> <span class="identifier">VariateTag</span> <span class="special">&gt;</span><span class="special">(</span><span class="identifier">relative</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_36_1_1_11_1.html" title="Struct abstract_absolute_tail_variate_means">tag::abstract_absolute_tail_variate_means</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weigh_1_3_2_6_3_56_1_1_3_1.html" title="Global weighted_tail_variate_means">weighted_tail_variate_means</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/abst_1_3_2_6_3_36_1_1_11_2.html" title="Struct abstract_relative_tail_variate_means">tag::abstract_relative_tail_variate_means</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/relat_1_3_2_6_3_56_1_1_3_2.html" title="Global relative_weighted_tail_variate_means">relative_weighted_tail_variate_means</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
    <span class="special">}</span>
  <span class="special">}</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">functional</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/multi_1_3_2_6_3_56_1_2_1_1.html" title="Struct template multiply_and_promote_to_double">multiply_and_promote_to_double</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.weighted_variance_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/weighted_variance.hpp" target="_top">boost/accumulators/statistics/weighted_variance.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_57_1_1_1.html" title="Struct as_feature&lt;tag::weighted_variance(immediate)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_variance</span><span class="special">(</span><span class="identifier">immediate</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/as_feat_1_3_2_6_3_57_1_1_2.html" title="Struct as_feature&lt;tag::weighted_variance(lazy)&gt;">as_feature</a><span class="special">&lt;</span><span class="identifier">tag</span><span class="special">::</span><span class="identifier">weighted_variance</span><span class="special">(</span><span class="identifier">lazy</span><span class="special">)</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/lazy_weighted_variance.html" title="Struct lazy_weighted_variance">tag::lazy_weighted_variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/lazy_weighted_variance.html" title="Global lazy_weighted_variance">lazy_weighted_variance</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/weighted_variance.html" title="Struct weighted_variance">tag::weighted_variance</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/weighted_variance.html" title="Global weighted_variance">weighted_variance</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/lazy_weighted_variance.html" title="Struct lazy_weighted_variance">lazy_weighted_variance</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_variance.html" title="Struct weighted_variance">weighted_variance</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section"><div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics.with_error_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics/with_error.hpp" target="_top">boost/accumulators/statistics/with_error.hpp</a>&gt;</h4></div></div></div></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.statistics_fwd_hpp"></a>Header &lt;<a href="../../../boost/accumulators/statistics_fwd.hpp" target="_top">boost/accumulators/statistics_fwd.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">accumulators</span> <span class="special">{</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/absolute.html" title="Struct absolute">absolute</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/for_median.html" title="Struct for_median">for_median</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/immediate.html" title="Struct immediate">immediate</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/kahan.html" title="Struct kahan">kahan</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/lazy.html" title="Struct lazy">lazy</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/left.html" title="Struct left">left</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/linear.html" title="Struct linear">linear</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/quadratic.html" title="Struct quadratic">quadratic</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/regular.html" title="Struct regular">regular</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/relative.html" title="Struct relative">relative</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/right.html" title="Struct right">right</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Stat1<span class="special">,</span> <span class="keyword">typename</span> Stat2<span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/stats.html" title="Struct template stats">stats</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/unweighted.html" title="Struct unweighted">unweighted</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/weighted.html" title="Struct weighted">weighted</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_density.html" title="Struct with_density">with_density</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature1<span class="special">,</span> <span class="keyword">typename</span> Feature2<span class="special">,</span> <span class="special">...</span> <span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_error.html" title="Struct template with_error">with_error</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_p_1_3_2_6_3_59_1_1_17.html" title="Struct with_p_square_cumulative_distribution">with_p_square_cumulative_distribution</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_p_square_quantile.html" title="Struct with_p_square_quantile">with_p_square_quantile</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_threshold_probability.html" title="Struct with_threshold_probability">with_threshold_probability</a><span class="special">;</span>
    <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/with_threshold_value.html" title="Struct with_threshold_value">with_threshold_value</a><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">extract</span> <span class="special">{</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/quantile.html" title="Struct quantile">tag::quantile</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/quantile.html" title="Global quantile">quantile</a><span class="special">;</span>
      <a class="link" href="../boost/accumulators/extractor.html" title="Struct template extractor">extractor</a><span class="special">&lt;</span> <a class="link" href="../boost/accumulators/tag/tail_mean.html" title="Struct tail_mean">tag::tail_mean</a> <span class="special">&gt;</span> <span class="keyword">const</span> <a class="link" href="../boost/accumulators/extract/tail_mean.html" title="Global tail_mean">tail_mean</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">impl</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/coherent_tail_mean_impl.html" title="Struct template coherent_tail_mean_impl">coherent_tail_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/covariance_impl.html" title="Struct template covariance_impl">covariance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/density_impl.html" title="Struct template density_impl">density_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Variance<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/error_of_mean_impl.html" title="Struct template error_of_mean_impl">error_of_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/extended_p_square_impl.html" title="Struct template extended_p_square_impl">extended_p_square_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Impl1<span class="special">,</span> <span class="keyword">typename</span> Impl2<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/exte_1_3_2_6_3_59_1_1_22_6.html" title="Struct template extended_p_square_quantile_impl">extended_p_square_quantile_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/immediate_mean_impl.html" title="Struct template immediate_mean_impl">immediate_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/imme_1_3_2_6_3_59_1_1_22_8.html" title="Struct template immediate_weighted_mean_impl">immediate_weighted_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/kurtosis_impl.html" title="Struct template kurtosis_impl">kurtosis_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> MeanFeature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/lazy_variance_impl.html" title="Struct template lazy_variance_impl">lazy_variance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> MeanFeature<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/laz_1_3_2_6_3_59_1_1_22_11.html" title="Struct template lazy_weighted_variance_impl">lazy_weighted_variance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/max_impl.html" title="Struct template max_impl">max_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> SumFeature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/mean_impl.html" title="Struct template mean_impl">mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/median_impl.html" title="Struct template median_impl">median_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/min_impl.html" title="Struct template min_impl">min_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> N<span class="special">,</span> <span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/moment_impl.html" title="Struct template moment_impl">moment_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/non_1_3_2_6_3_59_1_1_22_17.html" title="Struct template non_coherent_tail_mean_impl">non_coherent_tail_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/non_1_3_2_6_3_59_1_1_22_18.html" title="Struct template non_coherent_weighted_tail_mean_impl">non_coherent_weighted_tail_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/p_s_1_3_2_6_3_59_1_1_22_19.html" title="Struct template p_square_cumulative_distribution_impl">p_square_cumulative_distribution_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Impl<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/p_square_quantile_impl.html" title="Struct template p_square_quantile_impl">p_square_quantile_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/pea_1_3_2_6_3_59_1_1_22_21.html" title="Struct template peaks_over_threshold_prob_impl">peaks_over_threshold_prob_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Impl<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/pot_quantile_impl.html" title="Struct template pot_quantile_impl">pot_quantile_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Impl<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/pot_tail_mean_impl.html" title="Struct template pot_tail_mean_impl">pot_tail_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/rolling_count_impl.html" title="Struct template rolling_count_impl">rolling_count_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/rolling_mean_impl.html" title="Struct template rolling_mean_impl">rolling_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/rolling_sum_impl.html" title="Struct template rolling_sum_impl">rolling_sum_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/rolling_window_impl.html" title="Struct template rolling_window_impl">rolling_window_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/rolling_window_plus1_impl.html" title="Struct template rolling_window_plus1_impl">rolling_window_plus1_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/skewness_impl.html" title="Struct template skewness_impl">skewness_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/sum_impl.html" title="Struct template sum_impl">sum_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/sum_kahan_impl.html" title="Struct template sum_kahan_impl">sum_kahan_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/tail_impl.html" title="Struct template tail_impl">tail_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/tail_quantile_impl.html" title="Struct template tail_quantile_impl">tail_quantile_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/tail_variate_impl.html" title="Struct template tail_variate_impl">tail_variate_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Impl<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">,</span> 
               <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/tail_variate_means_impl.html" title="Struct template tail_variate_means_impl">tail_variate_means_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> MeanFeature<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/variance_impl.html" title="Struct template variance_impl">variance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> 
               <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_covariance_impl.html" title="Struct template weighted_covariance_impl">weighted_covariance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_density_impl.html" title="Struct template weighted_density_impl">weighted_density_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_39.html" title="Struct template weighted_extended_p_square_impl">weighted_extended_p_square_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_kurtosis_impl.html" title="Struct template weighted_kurtosis_impl">weighted_kurtosis_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_mean_impl.html" title="Struct template weighted_mean_impl">weighted_mean_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_median_impl.html" title="Struct template weighted_median_impl">weighted_median_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> N<span class="special">,</span> <span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_moment_impl.html" title="Struct template weighted_moment_impl">weighted_moment_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_44.html" title="Struct template weighted_p_square_cumulative_distribution_impl">weighted_p_square_cumulative_distribution_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> Impl<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_45.html" title="Struct template weighted_p_square_quantile_impl">weighted_p_square_quantile_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_46.html" title="Struct template weighted_peaks_over_threshold_impl">weighted_peaks_over_threshold_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_47.html" title="Struct template weighted_peaks_over_threshold_prob_impl">weighted_peaks_over_threshold_prob_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_skewness_impl.html" title="Struct template weighted_skewness_impl">weighted_skewness_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_sum_impl.html" title="Struct template weighted_sum_impl">weighted_sum_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_sum_kahan_impl.html" title="Struct template weighted_sum_kahan_impl">weighted_sum_kahan_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_51.html" title="Struct template weighted_tail_quantile_impl">weighted_tail_quantile_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> Impl<span class="special">,</span> 
               <span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wei_1_3_2_6_3_59_1_1_22_52.html" title="Struct template weighted_tail_variate_means_impl">weighted_tail_variate_means_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">,</span> <span class="keyword">typename</span> MeanFeature<span class="special">,</span> 
               <span class="keyword">typename</span> Tag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/weighted_variance_impl.html" title="Struct template weighted_variance_impl">weighted_variance_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/with_density_median_impl.html" title="Struct template with_density_median_impl">with_density_median_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wit_1_3_2_6_3_59_1_1_22_55.html" title="Struct template with_density_weighted_median_impl">with_density_weighted_median_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wit_1_3_2_6_3_59_1_1_22_56.html" title="Struct template with_p_square_cumulative_distribution_median_impl">with_p_square_cumulative_distribution_median_impl</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Sample<span class="special">,</span> <span class="keyword">typename</span> Weight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/impl/wit_1_3_2_6_3_59_1_1_22_57.html" title="Struct template with_p_square_cumulative_distribution_weighted_median_impl">with_p_square_cumulative_distribution_weighted_median_impl</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">tag</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abso_1_3_2_6_3_59_1_1_23_1.html" title="Struct template absolute_tail_variate_means">absolute_tail_variate_means</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/abso_1_3_2_6_3_59_1_1_23_2.html" title="Struct template absolute_weighted_tail_variate_means">absolute_weighted_tail_variate_means</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/coherent_tail_mean.html" title="Struct template coherent_tail_mean">coherent_tail_mean</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/covariance.html" title="Struct template covariance">covariance</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Feature<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/error_of.html" title="Struct template error_of">error_of</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/immediate_mean_of_variates.html" title="Struct template immediate_mean_of_variates">immediate_mean_of_variates</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/imme_1_3_2_6_3_59_1_1_23_7.html" title="Struct template immediate_weighted_mean_of_variates">immediate_weighted_mean_of_variates</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/left_tail_variate.html" title="Struct template left_tail_variate">left_tail_variate</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/mean_of_variates.html" title="Struct template mean_of_variates">mean_of_variates</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">int</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/moment.html" title="Struct template moment">moment</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/non_coherent_tail_mean.html" title="Struct template non_coherent_tail_mean">non_coherent_tail_mean</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/non_1_3_2_6_3_59_1_1_23_12.html" title="Struct template non_coherent_weighted_tail_mean">non_coherent_weighted_tail_mean</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/peaks_over_threshold.html" title="Struct template peaks_over_threshold">peaks_over_threshold</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/peaks_over_threshold_prob.html" title="Struct template peaks_over_threshold_prob">peaks_over_threshold_prob</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/pot_quantile.html" title="Struct template pot_quantile">pot_quantile</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/pot_quantile_prob.html" title="Struct template pot_quantile_prob">pot_quantile_prob</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/pot_tail_mean.html" title="Struct template pot_tail_mean">pot_tail_mean</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/pot_tail_mean_prob.html" title="Struct template pot_tail_mean_prob">pot_tail_mean_prob</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/quantile.html" title="Struct quantile">quantile</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rel_1_3_2_6_3_59_1_1_23_20.html" title="Struct template relative_tail_variate_means">relative_tail_variate_means</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/rel_1_3_2_6_3_59_1_1_23_21.html" title="Struct template relative_weighted_tail_variate_means">relative_weighted_tail_variate_means</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/right_tail_variate.html" title="Struct template right_tail_variate">right_tail_variate</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/sum_of_variates.html" title="Struct template sum_of_variates">sum_of_variates</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/sum_of_variates_kahan.html" title="Struct template sum_of_variates_kahan">sum_of_variates_kahan</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/tail.html" title="Struct template tail">tail</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/tail_mean.html" title="Struct tail_mean">tail_mean</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/tail_quantile.html" title="Struct template tail_quantile">tail_quantile</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">,</span> <span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/tail_variate.html" title="Struct template tail_variate">tail_variate</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/tail_variate_means.html" title="Struct template tail_variate_means">tail_variate_means</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/tail_weights.html" title="Struct template tail_weights">tail_weights</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_covariance.html" title="Struct template weighted_covariance">weighted_covariance</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_mean_of_variates.html" title="Struct template weighted_mean_of_variates">weighted_mean_of_variates</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">int</span> N<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_moment.html" title="Struct template weighted_moment">weighted_moment</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/wei_1_3_2_6_3_59_1_1_23_34.html" title="Struct template weighted_peaks_over_threshold">weighted_peaks_over_threshold</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/wei_1_3_2_6_3_59_1_1_23_35.html" title="Struct template weighted_peaks_over_threshold_prob">weighted_peaks_over_threshold_prob</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_pot_quantile.html" title="Struct template weighted_pot_quantile">weighted_pot_quantile</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_pot_quantile_prob.html" title="Struct template weighted_pot_quantile_prob">weighted_pot_quantile_prob</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_pot_tail_mean.html" title="Struct template weighted_pot_tail_mean">weighted_pot_tail_mean</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/wei_1_3_2_6_3_59_1_1_23_39.html" title="Struct template weighted_pot_tail_mean_prob">weighted_pot_tail_mean_prob</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_sum_of_variates.html" title="Struct template weighted_sum_of_variates">weighted_sum_of_variates</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/weighted_tail_quantile.html" title="Struct template weighted_tail_quantile">weighted_tail_quantile</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> LeftRight<span class="special">,</span> <span class="keyword">typename</span> VariateType<span class="special">,</span> <span class="keyword">typename</span> VariateTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/accumulators/tag/wei_1_3_2_6_3_59_1_1_23_42.html" title="Struct template weighted_tail_variate_means">weighted_tail_variate_means</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
<div class="section">
<div class="titlepage"><div><div><h3 class="title">
<a name="numeric_operators_library_reference"></a>Numeric Operators Library Reference</h3></div></div></div>
<div class="toc"><dl class="toc">
<dt><span class="section"><a href="reference.html#header.boost.accumulators.numeric.functional_hpp">Header &lt;boost/accumulators/numeric/functional.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.numeric.functional.complex_hpp">Header &lt;boost/accumulators/numeric/functional/complex.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.numeric.functional.valarray_hpp">Header &lt;boost/accumulators/numeric/functional/valarray.hpp&gt;</a></span></dt>
<dt><span class="section"><a href="reference.html#header.boost.accumulators.numeric.functional.vector_hpp">Header &lt;boost/accumulators/numeric/functional/vector.hpp&gt;</a></span></dt>
</dl></div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.numeric.functional_hpp"></a>Header &lt;<a href="../../../boost/accumulators/numeric/functional.hpp" target="_top">boost/accumulators/numeric/functional.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/default_.html" title="Struct template default_">default_</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/one.html" title="Struct template one">one</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/one_or_default.html" title="Struct template one_or_default">one_or_default</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/zero.html" title="Struct template zero">zero</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/zero_or_default.html" title="Struct template zero_or_default">zero_or_default</a><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">,</span> <span class="keyword">typename</span> From<span class="special">&gt;</span> 
      <span class="identifier">lazy_disable_if</span><span class="special">&lt;</span> <span class="identifier">is_const</span><span class="special">&lt;</span> <span class="identifier">From</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">if_</span><span class="special">&lt;</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">To</span><span class="special">,</span> <span class="identifier">From</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">To</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">To</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
      <a name="boost.numeric.promote_1_3_2_6_4_2_1_1_8"></a><span class="identifier">promote</span><span class="special">(</span><span class="identifier">From</span> <span class="special">&amp;</span> from<span class="special">)</span><span class="special">;</span>
    <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">,</span> <span class="keyword">typename</span> From<span class="special">&gt;</span> 
      <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">if_</span><span class="special">&lt;</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">To</span> <span class="keyword">const</span><span class="special">,</span> <span class="identifier">From</span> <span class="keyword">const</span>  <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">To</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">To</span> <span class="keyword">const</span>  <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
      <a name="boost.numeric.promote_1_3_2_6_4_2_1_1_9"></a><span class="identifier">promote</span><span class="special">(</span><span class="identifier">From</span> <span class="keyword">const</span> <span class="special">&amp;</span> from<span class="special">)</span><span class="special">;</span>
    <span class="keyword">namespace</span> <span class="identifier">functional</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_max.html" title="Struct template as_max">as_max</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_max_base.html" title="Struct template as_max_base">as_max_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_min.html" title="Struct template as_min">as_min</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_min_base.html" title="Struct template as_min_base">as_min_base</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_min_1_3_2_6_4_2_1_1_6_5.html" title="Struct template as_min_base&lt;Arg, typename enable_if&lt; is_floating_point&lt; Arg &gt; &gt;::type&gt;">as_min_base</a><span class="special">&lt;</span><span class="identifier">Arg</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">enable_if</span><span class="special">&lt;</span> <span class="identifier">is_floating_point</span><span class="special">&lt;</span> <span class="identifier">Arg</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_one.html" title="Struct template as_one">as_one</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_one_base.html" title="Struct template as_one_base">as_one_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_zero.html" title="Struct template as_zero">as_zero</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_zero_base.html" title="Struct template as_zero_base">as_zero_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/assign.html" title="Struct template assign">assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/assign_base.html" title="Struct template assign_base">assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Arg</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/complement.html" title="Struct template complement">complement</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/complement_base.html" title="Struct template complement_base">complement_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/divides.html" title="Struct template divides">divides</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/divides_assign.html" title="Struct template divides_assign">divides_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/divides_assign_base.html" title="Struct template divides_assign_base">divides_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/divides_base.html" title="Struct template divides_base">divides_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/equal_to.html" title="Struct template equal_to">equal_to</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/equal_to_base.html" title="Struct template equal_to_base">equal_to_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> LeftTag<span class="special">,</span> 
               <span class="keyword">typename</span> RightTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/fdiv.html" title="Struct template fdiv">fdiv</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/fdiv_base.html" title="Struct template fdiv_base">fdiv_base</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/fdiv__1_3_2_6_4_2_1_1_6_22.html" title="Struct template fdiv_base&lt;Left, Right, typename enable_if&lt; are_integral&lt; Left, Right &gt; &gt;::type&gt;">fdiv_base</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="keyword">typename</span> <span class="identifier">enable_if</span><span class="special">&lt;</span> <span class="identifier">are_integral</span><span class="special">&lt;</span> <span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/greater.html" title="Struct template greater">greater</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/greater_base.html" title="Struct template greater_base">greater_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/greater_equal.html" title="Struct template greater_equal">greater_equal</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/greater_equal_base.html" title="Struct template greater_equal_base">greater_equal_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/left_ref.html" title="Struct template left_ref">left_ref</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/less.html" title="Struct template less">less</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/less_base.html" title="Struct template less_base">less_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/less_equal.html" title="Struct template less_equal">less_equal</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/less_equal_base.html" title="Struct template less_equal_base">less_equal_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Arg</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/logical_not.html" title="Struct template logical_not">logical_not</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/logical_not_base.html" title="Struct template logical_not_base">logical_not_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> LeftTag<span class="special">,</span> 
               <span class="keyword">typename</span> RightTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/max_assign.html" title="Struct template max_assign">max_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/max_assign_base.html" title="Struct template max_assign_base">max_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> LeftTag<span class="special">,</span> 
               <span class="keyword">typename</span> RightTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/min_assign.html" title="Struct template min_assign">min_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/min_assign_base.html" title="Struct template min_assign_base">min_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/minus.html" title="Struct template minus">minus</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/minus_assign.html" title="Struct template minus_assign">minus_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/minus_assign_base.html" title="Struct template minus_assign_base">minus_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/minus_base.html" title="Struct template minus_base">minus_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/modulus.html" title="Struct template modulus">modulus</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/modulus_assign.html" title="Struct template modulus_assign">modulus_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/modulus_assign_base.html" title="Struct template modulus_assign_base">modulus_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/modulus_base.html" title="Struct template modulus_base">modulus_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/multiplies.html" title="Struct template multiplies">multiplies</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/multiplies_assign.html" title="Struct template multiplies_assign">multiplies_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/multiplies_assign_base.html" title="Struct template multiplies_assign_base">multiplies_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/multiplies_base.html" title="Struct template multiplies_base">multiplies_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/not_equal_to.html" title="Struct template not_equal_to">not_equal_to</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/not_equal_to_base.html" title="Struct template not_equal_to_base">not_equal_to_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/plus.html" title="Struct template plus">plus</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> 
               <span class="keyword">typename</span> LeftTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">,</span> 
               <span class="keyword">typename</span> RightTag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Right</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/plus_assign.html" title="Struct template plus_assign">plus_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/plus_assign_base.html" title="Struct template plus_assign_base">plus_assign_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/plus_base.html" title="Struct template plus_base">plus_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">,</span> <span class="keyword">typename</span> From<span class="special">,</span> <span class="keyword">typename</span> ToTag<span class="special">,</span> <span class="keyword">typename</span> FromTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promote.html" title="Struct template promote">promote</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">,</span> <span class="keyword">typename</span> From<span class="special">,</span> <span class="keyword">typename</span> EnableIf<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promote_base.html" title="Struct template promote_base">promote_base</a><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ToFrom<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promo_1_3_2_6_4_2_1_1_6_58.html" title="Struct template promote_base&lt;ToFrom, ToFrom, void&gt;">promote_base</a><span class="special">&lt;</span><span class="identifier">ToFrom</span><span class="special">,</span> <span class="identifier">ToFrom</span><span class="special">,</span> <span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>

      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Arg</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/unary_minus.html" title="Struct template unary_minus">unary_minus</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/unary_minus_base.html" title="Struct template unary_minus_base">unary_minus_base</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> Tag <span class="special">=</span> <span class="keyword">typename</span> <span class="identifier">tag</span><span class="special">&lt;</span><span class="identifier">Arg</span><span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span><span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/unary_plus.html" title="Struct template unary_plus">unary_plus</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/unary_plus_base.html" title="Struct template unary_plus_base">unary_plus_base</a><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">op</span> <span class="special">{</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/as_max.html" title="Struct as_max">as_max</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/as_min.html" title="Struct as_min">as_min</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/as_one.html" title="Struct as_one">as_one</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/as_zero.html" title="Struct as_zero">as_zero</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/assign.html" title="Struct assign">assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/complement.html" title="Struct complement">complement</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/divides.html" title="Struct divides">divides</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/divides_assign.html" title="Struct divides_assign">divides_assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/equal_to.html" title="Struct equal_to">equal_to</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/fdiv.html" title="Struct fdiv">fdiv</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/greater.html" title="Struct greater">greater</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/greater_equal.html" title="Struct greater_equal">greater_equal</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/less.html" title="Struct less">less</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/less_equal.html" title="Struct less_equal">less_equal</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/logical_not.html" title="Struct logical_not">logical_not</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/max_assign.html" title="Struct max_assign">max_assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/min_assign.html" title="Struct min_assign">min_assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/minus.html" title="Struct minus">minus</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/minus_assign.html" title="Struct minus_assign">minus_assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/modulus.html" title="Struct modulus">modulus</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/modulus_assign.html" title="Struct modulus_assign">modulus_assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/multiplies.html" title="Struct multiplies">multiplies</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/multiplies_assign.html" title="Struct multiplies_assign">multiplies_assign</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/not_equal_to.html" title="Struct not_equal_to">not_equal_to</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/plus.html" title="Struct plus">plus</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/plus_assign.html" title="Struct plus_assign">plus_assign</a><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/promote.html" title="Struct template promote">promote</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/unary_minus.html" title="Struct unary_minus">unary_minus</a><span class="special">;</span>
      <span class="keyword">struct</span> <a class="link" href="../boost/numeric/op/unary_plus.html" title="Struct unary_plus">unary_plus</a><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.numeric.functional.complex_hpp"></a>Header &lt;<a href="../../../boost/accumulators/numeric/functional/complex.hpp" target="_top">boost/accumulators/numeric/functional/complex.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">operators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> 
        <span class="identifier">disable_if</span><span class="special">&lt;</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">or_</span><span class="special">&lt;</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">U</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_3_1_1_1_1"></a><span class="keyword">operator</span><span class="special">*</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> ri<span class="special">,</span> <span class="identifier">U</span> <span class="keyword">const</span> <span class="special">&amp;</span> u<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> U<span class="special">&gt;</span> 
        <span class="identifier">disable_if</span><span class="special">&lt;</span> <span class="identifier">mpl</span><span class="special">::</span><span class="identifier">or_</span><span class="special">&lt;</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">U</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">U</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_3_1_1_1_2"></a><span class="keyword">operator</span><span class="special">/</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">complex</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span> ri<span class="special">,</span> <span class="identifier">U</span> <span class="keyword">const</span> <span class="special">&amp;</span> u<span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.numeric.functional.valarray_hpp"></a>Header &lt;<a href="../../../boost/accumulators/numeric/functional/valarray.hpp" target="_top">boost/accumulators/numeric/functional/valarray.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">functional</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_max_1_3_2_6_4_4_1_1_1_1.html" title="Struct template as_max&lt;T, std_valarray_tag&gt;">as_max</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_min_1_3_2_6_4_4_1_1_1_2.html" title="Struct template as_min&lt;T, std_valarray_tag&gt;">as_min</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_one_1_3_2_6_4_4_1_1_1_3.html" title="Struct template as_one&lt;T, std_valarray_tag&gt;">as_one</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_zer_1_3_2_6_4_4_1_1_1_4.html" title="Struct template as_zero&lt;T, std_valarray_tag&gt;">as_zero</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> RightTag<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/fdiv_L_1_3_2_6_4_4_1_1_1_5.html" title="Struct template fdiv&lt;Left, Right, std_valarray_tag, RightTag&gt;">fdiv</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">,</span> <span class="identifier">RightTag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/max_as_1_3_2_6_4_4_1_1_1_6.html" title="Struct template max_assign&lt;Left, Right, std_valarray_tag, std_valarray_tag&gt;">max_assign</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/min_as_1_3_2_6_4_4_1_1_1_7.html" title="Struct template min_assign&lt;Left, Right, std_valarray_tag, std_valarray_tag&gt;">min_assign</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> From<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promot_1_3_2_6_4_4_1_1_1_8.html" title="Struct template promote&lt;bool const, From, void, std_valarray_tag&gt;">promote</a><span class="special">&lt;</span><span class="keyword">bool</span> <span class="keyword">const</span><span class="special">,</span> <span class="identifier">From</span><span class="special">,</span> <span class="keyword">void</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> From<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promot_1_3_2_6_4_4_1_1_1_9.html" title="Struct template promote&lt;bool, From, void, std_valarray_tag&gt;">promote</a><span class="special">&lt;</span><span class="keyword">bool</span><span class="special">,</span> <span class="identifier">From</span><span class="special">,</span> <span class="keyword">void</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">,</span> <span class="keyword">typename</span> From<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promo_1_3_2_6_4_4_1_1_1_10.html" title="Struct template promote&lt;To, From, std_valarray_tag, std_valarray_tag&gt;">promote</a><span class="special">&lt;</span><span class="identifier">To</span><span class="special">,</span> <span class="identifier">From</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ToFrom<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promo_1_3_2_6_4_4_1_1_1_11.html" title="Struct template promote&lt;ToFrom, ToFrom, std_valarray_tag, std_valarray_tag&gt;">promote</a><span class="special">&lt;</span><span class="identifier">ToFrom</span><span class="special">,</span> <span class="identifier">ToFrom</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">,</span> <span class="identifier">std_valarray_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/tag_s_1_3_2_6_4_4_1_1_1_12.html" title="Struct template tag&lt;std::valarray&lt; T &gt;&gt;">tag</a><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">valarray</span><span class="special">&lt;</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">operators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="boost.numeric.operators.operat_1_3_2_6_4_4_1_1_2_1"></a><span class="keyword">operator</span><span class="special">/</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">valarray</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                              <span class="identifier">Right</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="boost.numeric.operators.operat_1_3_2_6_4_4_1_1_2_2"></a><span class="keyword">operator</span><span class="special">*</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">valarray</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                              <span class="identifier">Right</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="boost.numeric.operators.operat_1_3_2_6_4_4_1_1_2_3"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">valarray</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                              <span class="identifier">std</span><span class="special">::</span><span class="identifier">valarray</span><span class="special">&lt;</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
<div class="section">
<div class="titlepage"><div><div><h4 class="title">
<a name="header.boost.accumulators.numeric.functional.vector_hpp"></a>Header &lt;<a href="../../../boost/accumulators/numeric/functional/vector.hpp" target="_top">boost/accumulators/numeric/functional/vector.hpp</a>&gt;</h4></div></div></div>
<pre class="synopsis"><span class="keyword">namespace</span> <span class="identifier">boost</span> <span class="special">{</span>
  <span class="keyword">namespace</span> <span class="identifier">numeric</span> <span class="special">{</span>
    <span class="keyword">namespace</span> <span class="identifier">functional</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_max_1_3_2_6_4_5_1_1_1_1.html" title="Struct template as_max&lt;T, std_vector_tag&gt;">as_max</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_min_1_3_2_6_4_5_1_1_1_2.html" title="Struct template as_min&lt;T, std_vector_tag&gt;">as_min</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_one_1_3_2_6_4_5_1_1_1_3.html" title="Struct template as_one&lt;T, std_vector_tag&gt;">as_one</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/as_zer_1_3_2_6_4_5_1_1_1_4.html" title="Struct template as_zero&lt;T, std_vector_tag&gt;">as_zero</a><span class="special">&lt;</span><span class="identifier">T</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/fdiv_L_1_3_2_6_4_5_1_1_1_5.html" title="Struct template fdiv&lt;Left, Right, std_vector_tag, void&gt;">fdiv</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">,</span> <span class="keyword">void</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/max_as_1_3_2_6_4_5_1_1_1_6.html" title="Struct template max_assign&lt;Left, Right, std_vector_tag, std_vector_tag&gt;">max_assign</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/min_as_1_3_2_6_4_5_1_1_1_7.html" title="Struct template min_assign&lt;Left, Right, std_vector_tag, std_vector_tag&gt;">min_assign</a><span class="special">&lt;</span><span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> To<span class="special">,</span> <span class="keyword">typename</span> From<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promot_1_3_2_6_4_5_1_1_1_8.html" title="Struct template promote&lt;To, From, std_vector_tag, std_vector_tag&gt;">promote</a><span class="special">&lt;</span><span class="identifier">To</span><span class="special">,</span> <span class="identifier">From</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> ToFrom<span class="special">&gt;</span> 
        <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/promot_1_3_2_6_4_5_1_1_1_9.html" title="Struct template promote&lt;ToFrom, ToFrom, std_vector_tag, std_vector_tag&gt;">promote</a><span class="special">&lt;</span><span class="identifier">ToFrom</span><span class="special">,</span> <span class="identifier">ToFrom</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">,</span> <span class="identifier">std_vector_tag</span><span class="special">&gt;</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Al<span class="special">&gt;</span> <span class="keyword">struct</span> <a class="link" href="../boost/numeric/functional/tag_s_1_3_2_6_4_5_1_1_1_10.html" title="Struct template tag&lt;std::vector&lt; T, Al &gt;&gt;">tag</a><span class="special">&lt;</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Al</span> <span class="special">&gt;</span><span class="special">&gt;</span><span class="special">;</span>
    <span class="special">}</span>
    <span class="keyword">namespace</span> <span class="identifier">operators</span> <span class="special">{</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_1"></a><span class="keyword">operator</span><span class="special">/</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                              <span class="identifier">Right</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="keyword">typename</span> <a class="link" href="../boost/numeric/functional/divides.html" title="Struct template divides">functional::divides</a><span class="special">&lt;</span> <span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">result_type</span> <span class="special">&gt;</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_2"></a><span class="keyword">operator</span><span class="special">/</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                  <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_3"></a><span class="keyword">operator</span><span class="special">*</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                              <span class="identifier">Right</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="emphasis"><em><span class="identifier">unspecified</span></em></span> <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_4"></a><span class="keyword">operator</span><span class="special">*</span><span class="special">(</span><span class="identifier">Left</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                              <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="keyword">typename</span> <a class="link" href="../boost/numeric/functional/multiplies.html" title="Struct template multiplies">functional::multiplies</a><span class="special">&lt;</span> <span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">result_type</span> <span class="special">&gt;</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_5"></a><span class="keyword">operator</span><span class="special">*</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                  <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="keyword">typename</span> <a class="link" href="../boost/numeric/functional/plus.html" title="Struct template plus">functional::plus</a><span class="special">&lt;</span> <span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">result_type</span> <span class="special">&gt;</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_6"></a><span class="keyword">operator</span><span class="special">+</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                  <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">&gt;</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="keyword">typename</span> <a class="link" href="../boost/numeric/functional/minus.html" title="Struct template minus">functional::minus</a><span class="special">&lt;</span> <span class="identifier">Left</span><span class="special">,</span> <span class="identifier">Right</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">result_type</span> <span class="special">&gt;</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_7"></a><span class="keyword">operator</span><span class="special">-</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                  <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Right</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">&gt;</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
        <a name="boost.numeric.operators.operator+="></a><span class="keyword">operator</span><span class="special">+=</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="special">&amp;</span> left<span class="special">,</span> 
                   <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Left</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> right<span class="special">)</span><span class="special">;</span>
      <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Arg<span class="special">&gt;</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="keyword">typename</span> <a class="link" href="../boost/numeric/functional/unary_minus.html" title="Struct template unary_minus">functional::unary_minus</a><span class="special">&lt;</span> <span class="identifier">Arg</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">result_type</span> <span class="special">&gt;</span> 
        <a name="boost.numeric.operators.operat_1_3_2_6_4_5_1_1_2_9"></a><span class="keyword">operator</span><span class="special">-</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">vector</span><span class="special">&lt;</span> <span class="identifier">Arg</span> <span class="special">&gt;</span> <span class="keyword">const</span> <span class="special">&amp;</span> arg<span class="special">)</span><span class="special">;</span>
    <span class="special">}</span>
  <span class="special">}</span>
<span class="special">}</span></pre>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2005, 2006 Eric Niebler<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="acknowledgements.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../accumulators.html"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../boost/accumulators/tag/droppable.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
