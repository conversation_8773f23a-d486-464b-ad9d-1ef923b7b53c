<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>BoostBook element librarycategorylist</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../reference.html" title="Reference">
<link rel="prev" href="library.html" title="BoostBook element library">
<link rel="next" href="using-namespace.html" title="BoostBook element using-namespace">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="library.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../reference.html"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="using-namespace.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boostbook.dtd.librarycategorylist"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">
          BoostBook element <code class="sgmltag-element">librarycategorylist</code></span></h2>
<p>librarycategorylist — Categorized listing of libraries</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv">librarycategorylist ::= 
  (<a class="link" href="librarycategorydef.html" title="BoostBook element librarycategorydef">librarycategorydef</a>)*
</div>
<div class="refsection">
<a name="id-*******.66.4"></a><h2>Description</h2>
<p>This element is not intended for use by developers, but is
  used by <code class="filename">doc/src/boost.xml</code> as a
  placeholder.</p>
</div>
<div class="refsection">
<a name="id-*******.66.5"></a><h2>Attributes</h2>
<div class="informaltable"><table class="table">
<colgroup>
<col>
<col>
<col>
<col>
</colgroup>
<thead><tr>
<th>Name</th>
<th>Type</th>
<th>Value</th>
<th>Purpose</th>
</tr></thead>
<tbody>
<tr>
<td>last-revision</td>
<td>#IMPLIED</td>
<td>CDATA</td>
<td>Set to $Date$ to keep "last revised" information in sync with CVS changes</td>
</tr>
<tr>
<td>id</td>
<td>#IMPLIED</td>
<td>CDATA</td>
<td>A global identifier for this element</td>
</tr>
<tr>
<td>xml:base</td>
<td>#IMPLIED</td>
<td>CDATA</td>
<td>Implementation detail used by XIncludes</td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2005 Douglas Gregor<p>Distributed under the Boost Software License, Version 1.0.
      (See accompanying file LICENSE_1_0.txt or copy at
      <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>).
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="library.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../reference.html"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="using-namespace.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
