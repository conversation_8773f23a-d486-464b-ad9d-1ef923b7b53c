<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template basic_string</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../boost_container_header_reference.html#header.boost.container.string_hpp" title="Header &lt;boost/container/string.hpp&gt;">
<link rel="prev" href="swap_idm27713.html" title="Function template swap">
<link rel="next" href="string.html" title="Type definition string">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="swap_idm27713.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_container_header_reference.html#header.boost.container.string_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="string.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.container.basic_string"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template basic_string</span></h2>
<p>boost::container::basic_string</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_container_header_reference.html#header.boost.container.string_hpp" title="Header &lt;boost/container/string.hpp&gt;">boost/container/string.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> CharT<span class="special">,</span> <span class="keyword">typename</span> Traits <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span><span class="special">,</span> 
         <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">:</span>
  <span class="keyword">private</span> <span class="identifier">dtl</span><span class="special">::</span><span class="identifier">basic_string_base</span><span class="special">&lt;</span> <span class="identifier">real_allocator</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span> <span class="special">&gt;</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">Traits</span>                                                                  <a name="boost.container.basic_string.traits_type"></a><span class="identifier">traits_type</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">CharT</span>                                                                   <a name="boost.container.basic_string.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>            
  <span class="keyword">typedef</span> <span class="identifier">real_allocator</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Allocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">type</span>                                <a name="boost.container.basic_string.allocator_type"></a><span class="identifier">allocator_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">pointer</span>         <a name="boost.container.basic_string.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>               
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_pointer</span>   <a name="boost.container.basic_string.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">reference</span>       <a name="boost.container.basic_string.reference"></a><span class="identifier">reference</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_reference</span> <a name="boost.container.basic_string.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">size_type</span>       <a name="boost.container.basic_string.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">difference_type</span> <a name="boost.container.basic_string.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.basic_string.stored_allocator_type"></a><span class="identifier">stored_allocator_type</span><span class="special">;</span> 
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.basic_string.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.basic_string.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.basic_string.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.basic_string.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="basic_string.html#boost.container.basic_stringconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="basic_string.html#idm29652-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="basic_string.html#idm29659-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29668-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="keyword">explicit</span> <a class="link" href="basic_string.html#idm29680-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> 
                          <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29695-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29706-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29720-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29733-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29746-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
               <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29760-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29769-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29780-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29787-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29796-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29805-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29816-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <a class="link" href="default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29826-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <a class="link" href="default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> <a class="link" href="basic_string.html#idm29838-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html#idm29849-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29862-bb"><span class="identifier">basic_string</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> 
               <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm29881-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_string.html#idm29894-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm29907-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm29915-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm29923-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm29934-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html#idm29872-bb"><span class="special">~</span><span class="identifier">basic_string</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="basic_string.html#idm27810-bb">public member functions</a></span>
  <span class="identifier">allocator_type</span> <a class="link" href="basic_string.html#idm27811-bb"><span class="identifier">get_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm27820-bb"><span class="identifier">get_stored_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm27831-bb"><span class="identifier">get_stored_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm27842-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="basic_string.html#idm27851-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm27860-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="basic_string.html#idm27869-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="basic_string.html#idm27878-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="basic_string.html#idm27887-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="basic_string.html#idm27896-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="basic_string.html#idm27905-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="basic_string.html#idm27914-bb"><span class="identifier">cbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="basic_string.html#idm27923-bb"><span class="identifier">cend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="basic_string.html#idm27932-bb"><span class="identifier">crbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="basic_string.html#idm27941-bb"><span class="identifier">crend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="basic_string.html#idm27950-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm27959-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm27968-bb"><span class="identifier">length</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm27977-bb"><span class="identifier">max_size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm27986-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm27999-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28010-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <a class="link" href="default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">)</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm28026-bb"><span class="identifier">capacity</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28035-bb"><span class="identifier">reserve</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28044-bb"><span class="identifier">shrink_to_fit</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="basic_string.html#idm28053-bb"><span class="identifier">front</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="basic_string.html#idm28064-bb"><span class="identifier">front</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="basic_string.html#idm28075-bb"><span class="identifier">back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="basic_string.html#idm28086-bb"><span class="identifier">back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="basic_string.html#idm28097-bb"><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="basic_string.html#idm28110-bb"><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="basic_string.html#idm28123-bb"><span class="identifier">at</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="basic_string.html#idm28136-bb"><span class="identifier">at</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28149-bb"><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28160-bb"><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28172-bb"><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28182-bb"><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28192-bb"><span class="keyword">operator</span><span class="special">+=</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28200-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28211-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28222-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28243-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28260-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28272-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28284-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="identifier">InputIter</span><span class="special">,</span> <span class="identifier">InputIter</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28300-bb"><span class="identifier">append</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28308-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28315-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28326-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28339-bb"><span class="identifier">assign</span></a><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28352-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28372-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28389-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28401-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28413-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28424-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">InputIter</span><span class="special">,</span> <span class="identifier">InputIter</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28438-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28446-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_string.html#idm28464-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28487-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28507-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28525-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28543-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm28557-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm28570-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm28585-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">InputIter</span><span class="special">,</span> <span class="identifier">InputIter</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm28602-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28613-bb"><span class="identifier">pop_back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28622-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm28641-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="basic_string.html#idm28652-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28667-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28676-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
    <a class="link" href="basic_string.html#idm28696-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_string.html#idm28712-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
          <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
    <a class="link" href="basic_string.html#idm28737-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
            <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28763-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28786-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28807-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28829-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_string.html#idm28848-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28868-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a class="link" href="basic_string.html#idm28886-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">CharT</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
    <a class="link" href="basic_string.html#idm28906-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">InputIter</span><span class="special">,</span> <span class="identifier">InputIter</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
    <a class="link" href="basic_string.html#idm28928-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a class="link" href="basic_string.html#idm28948-bb"><span class="identifier">replace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm28964-bb"><span class="identifier">copy</span></a><span class="special">(</span><span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="basic_string.html#idm28983-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_swap</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> <a class="link" href="basic_string.html#idm28993-bb"><span class="identifier">c_str</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> <a class="link" href="basic_string.html#idm29002-bb"><span class="identifier">data</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">CharT</span> <span class="special">*</span> <a class="link" href="basic_string.html#idm29011-bb"><span class="identifier">data</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <a class="link" href="basic_string.html#idm29018-bb"><span class="keyword">operator</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicStringView<span class="special">&gt;</span> <span class="identifier">BasicStringView</span> <a class="link" href="basic_string.html#idm29028-bb"><span class="identifier">to_view</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29039-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29054-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29071-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29086-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29100-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29112-bb"><span class="identifier">rfind</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29127-bb"><span class="identifier">rfind</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29144-bb"><span class="identifier">rfind</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29159-bb"><span class="identifier">rfind</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29173-bb"><span class="identifier">rfind</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29185-bb"><span class="identifier">find_first_of</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29200-bb"><span class="identifier">find_first_of</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29217-bb"><span class="identifier">find_first_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29232-bb"><span class="identifier">find_first_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29246-bb"><span class="identifier">find_first_of</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29260-bb"><span class="identifier">find_last_of</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29275-bb"><span class="identifier">find_last_of</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29292-bb"><span class="identifier">find_last_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29307-bb"><span class="identifier">find_last_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29321-bb"><span class="identifier">find_last_of</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29333-bb"><span class="identifier">find_first_not_of</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29348-bb"><span class="identifier">find_first_not_of</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> 
                                <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29365-bb"><span class="identifier">find_first_not_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29380-bb"><span class="identifier">find_first_not_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29394-bb"><span class="identifier">find_first_not_of</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29406-bb"><span class="identifier">find_last_not_of</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29421-bb"><span class="identifier">find_last_not_of</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> 
                               <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29438-bb"><span class="identifier">find_last_not_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29453-bb"><span class="identifier">find_last_not_of</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="basic_string.html#idm29467-bb"><span class="identifier">find_last_not_of</span></a><span class="special">(</span><span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <a class="link" href="basic_string.html#idm29479-bb"><span class="identifier">substr</span></a><span class="special">(</span><span class="identifier">size_type</span> <span class="special">=</span> <span class="number">0</span><span class="special">,</span> <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29498-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29510-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29522-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29541-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29560-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> 
              <span class="identifier">size_type</span> <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
    <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29584-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">,</span> 
                <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29609-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29618-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="basic_string.html#idm29636-bb"><span class="identifier">compare</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="identifier">size_type</span> <span class="identifier">npos</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.41.3.4"></a><h2>Description</h2>
<p>The <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> class represents a Sequence of characters. It contains all the usual operations of a Sequence, and, additionally, it contains standard string operations such as search and concatenation.</p>
<p>The <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> class is parameterized by character type, and by that type's Character Traits.</p>
<p>This class has performance characteristics very much like vector&lt;&gt;, meaning, for example, that it does not perform reference-count or copy-on-write, and that concatenation of two strings is an O(N) operation.</p>
<p>Some of <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a>'s member functions use an unusual method of specifying positions and ranges. In addition to the conventional method using iterators, many of <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a>'s member functions use a single value pos of type size_type to represent a position (in which case the position is begin() + pos, and many of <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a>'s member functions use two values, pos and n, to represent a range. In that case pos is the beginning of the range and n is its size. That is, the range is [begin() + pos, begin() + pos + n).</p>
<p>Note that the C++ standard does not specify the complexity of <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> operations. In this implementation, <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> has performance characteristics very similar to those of vector: access to a single character is O(1), while copy and concatenation are O(N).</p>
<p>In this implementation, begin(), end(), rbegin(), rend(), operator[], c_str(), and data() do not invalidate iterators. In this implementation, iterators are only invalidated by member functions that explicitly change the string's contents.</p>
<p>
</p>
<div class="refsect2">
<a name="id-********.41.3.4.9"></a><h3>Template Parameters</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> CharT</pre>
<p>The type of character it contains. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Traits <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">char_traits</span><span class="special">&lt;</span><span class="identifier">CharT</span><span class="special">&gt;</span></pre>
<p>The Character Traits type, which encapsulates basic character operations </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span></pre>
<p>The allocator, used for internal memory management. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.41.3.4.10"></a><h3>
<a name="boost.container.basic_stringconstruct-copy-destruct"></a><code class="computeroutput">basic_string</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm29652-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Default constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code>.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm29659-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29668-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code>.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x == *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor or allocation throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="keyword">explicit</span> <a name="idm29680-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> 
                        <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Same as <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code>(sv.data(), sv.size(), a).</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor or allocation throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29695-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span> s<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructor. Moves s's resources to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29706-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> using the specified allocator.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x == *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocation throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29720-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span> s<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructor using the specified allocator. Moves s's resources to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocation throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant if a == s.get_allocator(), linear otherwise. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29733-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> with a default-constructed allocator, and is initialized by a specific number of characters of the s string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29746-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> 
             <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter, and is initialized by a specific number of characters of the s string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29760-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking a default-constructed allocator, and is initialized by a specific number of characters of the s c-string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29769-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter, and is initialized by a specific number of characters of the s c-string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29780-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> with a default-constructed allocator, and is initialized by the null-terminated s c-string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29787-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter, and is initialized by the null-terminated s c-string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29796-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> with a default-constructed allocator, and is initialized by n copies of c. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29805-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter, and is initialized by n copies of c. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29816-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <a class="link" href="default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> with a default-constructed allocator, and is initialized by n default-initialized characters. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29826-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <a class="link" href="default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter, and is initialized by n default-initialized characters. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm29838-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">InputIterator</span> f<span class="special">,</span> <span class="identifier">InputIterator</span> l<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> with a default-constructed allocator, and a range of iterators. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm29849-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">InputIterator</span> f<span class="special">,</span> <span class="identifier">InputIterator</span> l<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code> taking the allocator as parameter, and a range of iterators. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29862-bb"></a><span class="identifier">basic_string</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> 
             <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Same as <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code>(il.begin(), il.end(), a). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm29881-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a string.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x == *this.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the elements x contains. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm29894-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructor. Moves x's resources to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_traits_type::propagate_on_container_move_assignment is false and allocation throws</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant if allocator_traits_type:: propagate_on_container_move_assignment is true or this-&gt;get&gt;allocator() == x.get_allocator(). Linear otherwise. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm29907-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Assignment from a null-terminated c-string. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm29915-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns *this = basic_string(1, c). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm29923-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to return assign(sv). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm29934-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns *this = basic_string(il); </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm29872-bb"></a><span class="special">~</span><span class="identifier">basic_string</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Destroys the <code class="computeroutput"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a></code>. All used memory is deallocated.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.41.3.4.11"></a><h3>
<a name="idm27810-bb"></a><code class="computeroutput">basic_string</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">allocator_type</span> <a name="idm27811-bb"></a><span class="identifier">get_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a copy of the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a name="idm27820-bb"></a><span class="identifier">get_stored_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a name="idm27831-bb"></a><span class="identifier">get_stored_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm27842-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element contained in the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm27851-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the first element contained in the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm27860-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the end of the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm27869-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the end of the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm27878-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the beginning of the reversed vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm27887-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm27896-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the end of the reversed vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm27905-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm27914-bb"></a><span class="identifier">cbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the first element contained in the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm27923-bb"></a><span class="identifier">cend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the end of the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm27932-bb"></a><span class="identifier">crbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm27941-bb"></a><span class="identifier">crend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm27950-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if the vector contains no elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm27959-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of the elements contained in the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm27968-bb"></a><span class="identifier">length</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of the elements contained in the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm27977-bb"></a><span class="identifier">max_size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the largest possible size of the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm27986-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts or erases elements at the end such that the size becomes n. New elements are copy constructed from x.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the difference between size() and new_size. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm27999-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts or erases elements at the end such that the size becomes n. New elements are value initialized.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the difference between size() and new_size. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28010-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <a class="link" href="default_init_t.html" title="Struct default_init_t">default_init_t</a><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts or erases elements at the end such that the size becomes n. New elements are uninitialized.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the difference between size() and new_size.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm28026-bb"></a><span class="identifier">capacity</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Number of elements for which memory has been allocated. capacity() is always greater than or equal to size().</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28035-bb"></a><span class="identifier">reserve</span><span class="special">(</span><span class="identifier">size_type</span> res_arg<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: If n is less than or equal to capacity(), this call has no effect. Otherwise, it is a request for allocation of additional memory. If the request is successful, then capacity() is greater than or equal to n; otherwise, capacity() is unchanged. In either case, size() is unchanged.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation allocation throws </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28044-bb"></a><span class="identifier">shrink_to_fit</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Tries to deallocate the excess of memory created with previous allocations. The size of the string is unchanged</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to size(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm28053-bb"></a><span class="identifier">front</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the first element of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm28064-bb"></a><span class="identifier">front</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the first element of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm28075-bb"></a><span class="identifier">back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the last element of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm28086-bb"></a><span class="identifier">back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the last element of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm28097-bb"></a><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: size() &gt; n.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the nth element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm28110-bb"></a><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: size() &gt; n.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the nth element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm28123-bb"></a><span class="identifier">at</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: size() &gt; n.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the nth element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: range_error if n &gt;= size()</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm28136-bb"></a><span class="identifier">at</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: size() &gt; n.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the nth element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: range_error if n &gt;= size()</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28149-bb"></a><span class="keyword">operator</span><span class="special">+=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Calls append(str.data, str.size()).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28160-bb"></a><span class="keyword">operator</span><span class="special">+=</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Same as <code class="computeroutput">return append(sv)</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28172-bb"></a><span class="keyword">operator</span><span class="special">+=</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Calls append(s).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28182-bb"></a><span class="keyword">operator</span><span class="special">+=</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Calls append(1, c).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28192-bb"></a><span class="keyword">operator</span><span class="special">+=</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns append(il) </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28200-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Calls append(str.data(), str.size()).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28211-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Same as return append(sv.data(), sv.size()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28222-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= str.size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to append as the smaller of n and str.size() - pos and calls append(str.data() + pos, rlen).</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws and <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; str.size()</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28243-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: The function replaces the string controlled by *this with a string of length size() + n whose irst size() elements are a copy of the original string controlled by *this and whose remaining elements are a copy of the initial n elements of s.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if size() + n &gt; max_size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28260-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls append(s, traits::length(s)).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28272-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to append(basic_string(n, c)).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28284-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="identifier">InputIter</span> first<span class="special">,</span> <span class="identifier">InputIter</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [first,last) is a valid range.</p>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to append(basic_string(first, last)).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28300-bb"></a><span class="identifier">append</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns append(il.begin(), il.size()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28308-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to append(static_cast&lt;size_type&gt;(1), c). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28315-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to assign(str, 0, npos).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28326-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to return assign(sv.data(), sv.size()).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28339-bb"></a><span class="identifier">assign</span><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;&amp;</span> ms<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: The function replaces the string controlled by *this with a string of length str.size() whose elements are a copy of the string controlled by str. Leaves str in a valid but unspecified state.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28352-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= str.size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to assign as the smaller of n and str.size() - pos and calls assign(str.data() + pos rlen).</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; str.size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28372-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: Replaces the string controlled by *this with a string of length n whose elements are a copy of those pointed to by s.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if n &gt; max_size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28389-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls assign(s, traits::length(s)).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28401-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to assign(basic_string(n, c)).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28413-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> first<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to assign(basic_string(first, last)). <span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28424-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">InputIter</span> first<span class="special">,</span> <span class="identifier">InputIter</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to assign(basic_string(first, last)).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28438-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns assign(il.begin(), il.size()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28446-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">size_type</span> pos<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= size().</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls insert(pos, str.data(), str.size()).</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28464-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos2<span class="special">,</span> 
       <span class="identifier">size_type</span> n <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size() and pos2 &lt;= str.size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to insert as the smaller of n and str.size() - pos2 and calls insert(pos1, str.data() + pos2, rlen).</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or pos2 &gt; str.size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28487-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">size_type</span> pos<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT and pos &lt;= size().</p>
<p><span class="bold"><strong>Effects</strong></span>: Replaces the string controlled by *this with a string of length size() + n whose first pos elements are a copy of the initial elements of the original string controlled by *this and whose next n elements are a copy of the elements in s and whose remaining elements are a copy of the remaining elements of the original string controlled by *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size() or <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if size() + n &gt; max_size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28507-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">size_type</span> pos<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= size() and s points to an array of at least traits::length(s) + 1 elements of CharT</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls insert(pos, s, traits::length(s)).</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size() <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if size() &gt; max_size() - Traits::length(s)</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28525-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to insert(pos, basic_string(n, c)).</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size() <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if size() &gt; max_size() - n</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28543-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Same as <code class="computeroutput">return insert(pos, sv.data(), sv.size())</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm28557-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p is a valid iterator on *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: inserts a copy of c before the character referred to by p.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator which refers to the copy of the inserted character. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm28570-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p is a valid iterator on *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts n copies of c before the character referred to by p.</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the first inserted element or p if n is 0. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm28585-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">InputIter</span> first<span class="special">,</span> <span class="identifier">InputIter</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p is a valid iterator on *this. [first,last) is a valid range.</p>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to insert(p - begin(), basic_string(first, last)).</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the first inserted element or p if first == last. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm28602-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: As if by insert(p, il.begin(), il.end()).</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator which refers to the copy of the first inserted character, or p if i1 is empty. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28613-bb"></a><span class="identifier">pop_back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes the last element from the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28622-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">,</span> <span class="identifier">size_type</span> n <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length xlen of the string to be removed as the smaller of n and size() - pos. The function then replaces the string controlled by *this with a string of length size() - xlen whose first pos elements are a copy of the initial elements of the original string controlled by *this, and whose remaining elements are a copy of the elements of the original string controlled by *this beginning at position pos + xlen.</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm28641-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes the character referred to by p.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator which points to the element immediately following p prior to the element being erased. If no such element exists, end() is returned. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm28652-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> first<span class="special">,</span> <span class="identifier">const_iterator</span> last<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: first and last are valid iterators on *this, defining a range [first,last).</p>
<p><span class="bold"><strong>Effects</strong></span>: Removes the characters in the range [first,last).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator which points to the element pointed to by last prior to the other elements being erased. If no such element exists, end() is returned. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28667-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements of the vector.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the vector. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28676-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size().</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(pos1, n1, str.data(), str.size()).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws or <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a name="idm28696-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Calls <code class="computeroutput">return replace(pos1, n1, sv.data(), sv.size());</code>. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28712-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> 
        <span class="identifier">size_type</span> pos2<span class="special">,</span> <span class="identifier">size_type</span> n2 <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size() and pos2 &lt;= str.size().</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to be inserted as the smaller of n2 and str.size() - pos2 and calls replace(pos1, n1, str.data() + pos2, rlen).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or pos2 &gt; str.size().</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a name="idm28737-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> 
          <span class="identifier">size_type</span> pos2<span class="special">,</span> <span class="identifier">size_type</span> n2 <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or pos2 &gt; sv.size().</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to be inserted as the smaller of n2 and sv.size() - pos2 and calls <code class="computeroutput">replace(pos1, n1, sv.data() + pos2, rlen)</code>.</p>
<p><span class="bold"><strong>Returns</strong></span>: *this. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28763-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n2<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size() and s points to an array of at least n2 elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length xlen of the string to be removed as the smaller of n1 and size() - pos1. If size() - xlen &gt;= max_size() - n2 throws <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code>. Otherwise, the function replaces the string controlled by *this with a string of length size() - xlen + n2 whose first pos1 elements are a copy of the initial elements of the original string controlled by *this, whose next n2 elements are a copy of the initial n2 elements of s, and whose remaining elements are a copy of the elements of the original string controlled by *this beginning at position pos + xlen.</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if the length of the resulting string would exceed max_size()</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28786-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size() and s points to an array of at least n2 elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length xlen of the string to be removed as the smaller of n1 and size() - pos1. If size() - xlen &gt;= max_size() - n2 throws <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code>. Otherwise, the function replaces the string controlled by *this with a string of length size() - xlen + n2 whose first pos1 elements are a copy of the initial elements of the original string controlled by *this, whose next n2 elements are a copy of the initial n2 elements of s, and whose remaining elements are a copy of the elements of the original string controlled by *this beginning at position pos + xlen.</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if the length of the resulting string would exceed max_size()</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28807-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="identifier">size_type</span> n2<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size().</p>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to replace(pos1, n1, basic_string(n2, c)).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or <code class="computeroutput"><a class="link" href="length_error.html" title="Class length_error">length_error</a></code> if the length of the resulting string would exceed max_size()</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28829-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(),i1) and [i1,i2) are valid ranges.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(i1 - begin(), i2 - i1, str).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28848-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(),i1) and [i1,i2) are valid ranges and s points to an array of at least n elements</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(i1 - begin(), i2 - i1, s, n).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> <a name="idm28868-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(),i1) and [i1,i2) are valid ranges and s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(i1 - begin(), i2 - i1, s, traits::length(s)).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28886-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">CharT</span> c<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(),i1) and [i1,i2) are valid ranges.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(i1 - begin(), i2 - i1, basic_string(n, c)).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIter<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a name="idm28906-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> <span class="identifier">InputIter</span> j1<span class="special">,</span> <span class="identifier">InputIter</span> j2<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(),i1), [i1,i2) and [j1,j2) are valid ranges.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(i1 - begin(), i2 - i1, basic_string(j1, j2)).</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws</p>
<p><span class="bold"><strong>Returns</strong></span>: *this </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
  <a name="idm28928-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> 
          <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(), i1) and [i1, i2) are valid ranges.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls <code class="computeroutput">replace(i1 - begin(), i2 - i1, sv).</code>.</p>
<p><span class="bold"><strong>Returns</strong></span>: *this. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> 
<a name="idm28948-bb"></a><span class="identifier">replace</span><span class="special">(</span><span class="identifier">const_iterator</span> i1<span class="special">,</span> <span class="identifier">const_iterator</span> i2<span class="special">,</span> 
        <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">CharT</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [begin(), i1) and [i1, i2) are valid ranges.</p>
<p><span class="bold"><strong>Effects</strong></span>: Calls replace(i1 - begin(), i2 - i1, il.begin(), il.size()).</p>
<p><span class="bold"><strong>Returns</strong></span>: *this. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm28964-bb"></a><span class="identifier">copy</span><span class="special">(</span><span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to copy as the smaller of n and size() - pos. s shall designate an array of at least rlen elements. The function then replaces the string designated by s with a string of length rlen whose elements are a copy of the string controlled by *this beginning at position pos. The function does not append a null object to the string designated by s.</p>
<p><span class="bold"><strong>Throws</strong></span>: if memory allocation throws, <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size().</p>
<p><span class="bold"><strong>Returns</strong></span>: rlen </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28983-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_swap</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: *this contains the same sequence of characters that was in s, s contains the same sequence of characters that was in *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> <a name="idm28993-bb"></a><span class="identifier">c_str</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: The program shall not alter any of the values stored in the character array.</p>
<p><span class="bold"><strong>Returns</strong></span>: A pointer p such that p + i == &amp;operator[](i) for each i in [0,size()].</p>
<p><span class="bold"><strong>Complexity</strong></span>: constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> <a name="idm29002-bb"></a><span class="identifier">data</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: The program shall not alter any of the values stored in the character array.</p>
<p><span class="bold"><strong>Returns</strong></span>: A pointer p such that p + i == &amp;operator[](i) for each i in [0,size()].</p>
<p><span class="bold"><strong>Complexity</strong></span>: constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">CharT</span> <span class="special">*</span> <a name="idm29011-bb"></a><span class="identifier">data</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: A pointer p such that p + i == &amp;operator[](i) for each i in [0,size()].</p>
<p><span class="bold"><strong>Complexity</strong></span>: constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <a name="idm29018-bb"></a><span class="keyword">operator</span> <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: a string_view to the characters in the string.</p>
<p><span class="bold"><strong>Complexity</strong></span>: constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BasicStringView<span class="special">&gt;</span> <span class="identifier">BasicStringView</span> <a name="idm29028-bb"></a><span class="identifier">to_view</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: a string_view to the characters in the string.</p>
<p><span class="bold"><strong>Complexity</strong></span>: constant time.</p>
<p><span class="bold"><strong>Note</strong></span>: This function is available to write portable code for compilers that don't support templated conversion operators. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29039-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the lowest position xpos, if possible, such that both of the following conditions hold: 1) pos &lt;= xpos and xpos + str.size() &lt;= size(); 2) traits::eq(at(xpos+I), str.at(I)) for all elements I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm29054-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the lowest position xpos, if possible, such that both of the following conditions hold: 1) pos &lt;= xpos and xpos + sv.size() &lt;= size(); 2) traits::eq(at(xpos+I), sv.at(I)) for all elements I of the string controlled by sv.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29071-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find(basic_string&lt;CharT,traits,allocator_type&gt;(s,n),pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29086-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find(basic_string(s), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29100-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find(basic_string&lt;CharT,traits,allocator_type&gt;(1,c), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29112-bb"></a><span class="identifier">rfind</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the highest position xpos, if possible, such that both of the following conditions obtain: a) xpos &lt;= pos and xpos + str.size() &lt;= size(); b) traits::eq(at(xpos+I), str.at(I)) for all elements I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm29127-bb"></a><span class="identifier">rfind</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the highest position xpos, if possible, such that both of the following conditions obtain: a) xpos &lt;= pos and xpos + sv.size() &lt;= size(); b) traits::eq(at(xpos+I), sv.at(I)) for all elements I of the string controlled by sv.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29144-bb"></a><span class="identifier">rfind</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: rfind(basic_string(s, n), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29159-bb"></a><span class="identifier">rfind</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos &lt;= size() and s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: rfind(basic_string(s), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29173-bb"></a><span class="identifier">rfind</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: rfind(basic_string&lt;CharT,traits,allocator_type&gt;(1,c),pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29185-bb"></a><span class="identifier">find_first_of</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the lowest position xpos, if possible, such that both of the following conditions obtain: a) pos &lt;= xpos and xpos &lt; size(); b) traits::eq(at(xpos), str.at(I)) for some element I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm29200-bb"></a><span class="identifier">find_first_of</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> 
                          <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the lowest position xpos, if possible, such that both of the following conditions obtain: a) pos &lt;= xpos and xpos &lt; size(); b) traits::eq(at(xpos), sv.at(I)) for some element I of the string controlled by sv.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29217-bb"></a><span class="identifier">find_first_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_first_of(basic_string(s, n), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29232-bb"></a><span class="identifier">find_first_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_first_of(basic_string(s), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29246-bb"></a><span class="identifier">find_first_of</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_first_of(basic_string&lt;CharT,traits,allocator_type&gt;(1,c), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29260-bb"></a><span class="identifier">find_last_of</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the highest position xpos, if possible, such that both of the following conditions obtain: a) xpos &lt;= pos and xpos &lt; size(); b) traits::eq(at(xpos), str.at(I)) for some element I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm29275-bb"></a><span class="identifier">find_last_of</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> 
                         <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the highest position xpos, if possible, such that both of the following conditions obtain: a) xpos &lt;= pos and xpos &lt; size(); b) traits::eq(at(xpos), str.at(I)) for some element I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29292-bb"></a><span class="identifier">find_last_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_last_of(basic_string(s, n), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29307-bb"></a><span class="identifier">find_last_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_last_of(basic_string&lt;CharT,traits,allocator_type&gt;(1,c),pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29321-bb"></a><span class="identifier">find_last_of</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_last_of(basic_string(s), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29333-bb"></a><span class="identifier">find_first_not_of</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the lowest position xpos, if possible, such that both of the following conditions obtain: a) pos &lt;= xpos and xpos &lt; size(); b) traits::eq(at(xpos), str.at(I)) for no element I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm29348-bb"></a><span class="identifier">find_first_not_of</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> 
                              <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the lowest position xpos, if possible, such that both of the following conditions obtain: a) pos &lt;= xpos and xpos &lt; size(); b) traits::eq(at(xpos), sv.at(I)) for no element I of the string controlled by sv.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29365-bb"></a><span class="identifier">find_first_not_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_first_not_of(basic_string(s, n), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29380-bb"></a><span class="identifier">find_first_not_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_first_not_of(basic_string(s), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29394-bb"></a><span class="identifier">find_first_not_of</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_first_not_of(basic_string(1, c), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29406-bb"></a><span class="identifier">find_last_not_of</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the highest position xpos, if possible, such that both of the following conditions obtain: a) xpos &lt;= pos and xpos &lt; size(); b) traits::eq(at(xpos), str.at(I)) for no element I of the string controlled by str.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm29421-bb"></a><span class="identifier">find_last_not_of</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> 
                             <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the highest position xpos, if possible, such that both of the following conditions obtain: a) xpos &lt;= pos and xpos &lt; size(); b) traits::eq(at(xpos), sv.at(I)) for no element I of the string controlled by sv.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: xpos if the function can determine such a value for xpos. Otherwise, returns npos. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29438-bb"></a><span class="identifier">find_last_not_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least n elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_last_not_of(basic_string(s, n), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29453-bb"></a><span class="identifier">find_last_not_of</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_last_not_of(basic_string(s), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm29467-bb"></a><span class="identifier">find_last_not_of</span><span class="special">(</span><span class="identifier">CharT</span> c<span class="special">,</span> <span class="identifier">size_type</span> pos <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: find_last_not_of(basic_string(1, c), pos). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <a name="idm29479-bb"></a><span class="identifier">substr</span><span class="special">(</span><span class="identifier">size_type</span> pos <span class="special">=</span> <span class="number">0</span><span class="special">,</span> <span class="identifier">size_type</span> n <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Requires: pos &lt;= size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to copy as the smaller of n and size() - pos.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos &gt; size().</p>
<p><span class="bold"><strong>Returns</strong></span>: basic_string&lt;CharT,traits,allocator_type&gt;(data()+pos,rlen). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="idm29498-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to compare as the smaller of size() and str.size(). The function then compares the two strings by calling traits::compare(data(), str.data(), rlen).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: The nonzero result if the result of the comparison is nonzero. Otherwise, returns a value &lt; 0 if size() &lt; str.size(), a 0 value if size() == str.size(), and value &gt; 0 if size() &gt; str.size() </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="keyword">int</span> <a name="idm29510-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: compare(basic_string(sv)). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="idm29522-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to compare as the smaller of (this-&gt;size() - pos1), n1 and str.size(). The function then compares the two strings by calling traits::compare(data()+pos1, str.data(), rlen).</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size()</p>
<p><span class="bold"><strong>Returns</strong></span>:basic_string(*this,pos1,n1).compare(str). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="keyword">int</span> <a name="idm29541-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> 
              <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size()</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size()</p>
<p><span class="bold"><strong>Returns</strong></span>:basic_string(*this,pos1,n1).compare(sv). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="idm29560-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="basic_string.html" title="Class template basic_string">basic_string</a> <span class="special">&amp;</span> str<span class="special">,</span> 
            <span class="identifier">size_type</span> pos2<span class="special">,</span> <span class="identifier">size_type</span> n2 <span class="special">=</span> <span class="identifier">npos</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size() and pos2 &lt;= str.size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to copy as the smaller of</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or pos2 &gt; str.size()</p>
<p><span class="bold"><strong>Returns</strong></span>: basic_string(*this, pos1, n1).compare(basic_string(str, pos2, n2)). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">template</span><span class="special">&lt;</span> <span class="keyword">class</span><span class="special">,</span> <span class="keyword">class</span> <span class="special">&gt;</span> <span class="keyword">class</span> BasicStringView<span class="special">&gt;</span> 
  <span class="keyword">int</span> <a name="idm29584-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> 
              <span class="identifier">BasicStringView</span><span class="special">&lt;</span> <span class="identifier">CharT</span><span class="special">,</span> <span class="identifier">Traits</span> <span class="special">&gt;</span> sv<span class="special">,</span> <span class="identifier">size_type</span> pos2<span class="special">,</span> 
              <span class="identifier">size_type</span> n2<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &lt;= size() and pos2 &lt;= str.size()</p>
<p><span class="bold"><strong>Effects</strong></span>: Determines the effective length rlen of the string to copy as the smaller of</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size() or pos2 &gt; sv.size()</p>
<p><span class="bold"><strong>Returns</strong></span>: basic_string(*this, pos1, n1).compare(BasicStringView&lt;CharT, Traits&gt;(sv, pos2, n2)). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="idm29609-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Returns</strong></span>: compare(basic_string(s)). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="idm29618-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">,</span> <span class="identifier">size_type</span> n2<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &gt; size() and s points to an array of at least n2 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size()</p>
<p><span class="bold"><strong>Returns</strong></span>: basic_string(*this, pos, n1).compare(basic_string(s, n2)). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="idm29636-bb"></a><span class="identifier">compare</span><span class="special">(</span><span class="identifier">size_type</span> pos1<span class="special">,</span> <span class="identifier">size_type</span> n1<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">CharT</span> <span class="special">*</span> s<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: pos1 &gt; size() and s points to an array of at least traits::length(s) + 1 elements of CharT.</p>
<p><span class="bold"><strong>Throws</strong></span>: <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if pos1 &gt; size()</p>
<p><span class="bold"><strong>Returns</strong></span>: basic_string(*this, pos, n1).compare(basic_string(s, n2)). </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2018 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="swap_idm27713.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_container_header_reference.html#header.boost.container.string_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="string.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
