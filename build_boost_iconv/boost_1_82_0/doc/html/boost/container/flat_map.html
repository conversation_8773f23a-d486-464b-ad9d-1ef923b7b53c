<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template flat_map</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../boost_container_header_reference.html#header.boost.container.flat_map_hpp" title="Header &lt;boost/container/flat_map.hpp&gt;">
<link rel="prev" href="pmr/devector_of.html" title="Struct template devector_of">
<link rel="next" href="flat_multimap.html" title="Class template flat_multimap">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pmr/devector_of.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_container_header_reference.html#header.boost.container.flat_map_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="flat_multimap.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.container.flat_map"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template flat_map</span></h2>
<p>boost::container::flat_map</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_container_header_reference.html#header.boost.container.flat_map_hpp" title="Header &lt;boost/container/flat_map.hpp&gt;">boost/container/flat_map.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Key<span class="special">,</span> <span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span><span class="special">,</span> 
         <span class="keyword">typename</span> AllocatorOrContainer <span class="special">=</span> <a class="link" href="new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="special">&gt;</span> <span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">Key</span>                                                    <a name="boost.container.flat_map.key_type"></a><span class="identifier">key_type</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">T</span>                                                      <a name="boost.container.flat_map.mapped_type"></a><span class="identifier">mapped_type</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">Compare</span>                                                <a name="boost.container.flat_map.key_compare"></a><span class="identifier">key_compare</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span>                                    <a name="boost.container.flat_map.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>            
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                 <a name="boost.container.flat_map.sequence_type"></a><span class="identifier">sequence_type</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">allocator_type</span>                          <a name="boost.container.flat_map.allocator_type"></a><span class="identifier">allocator_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">allocator_type</span> <span class="special">&gt;</span> <a name="boost.container.flat_map.allocator_traits_type"></a><span class="identifier">allocator_traits_type</span><span class="special">;</span> 
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">pointer</span>                                 <a name="boost.container.flat_map.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>               
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">const_pointer</span>                           <a name="boost.container.flat_map.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">reference</span>                               <a name="boost.container.flat_map.reference"></a><span class="identifier">reference</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">const_reference</span>                         <a name="boost.container.flat_map.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">size_type</span>                               <a name="boost.container.flat_map.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">difference_type</span>                         <a name="boost.container.flat_map.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                 <a name="boost.container.flat_map.stored_allocator_type"></a><span class="identifier">stored_allocator_type</span><span class="special">;</span> 
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                 <a name="boost.container.flat_map.value_compare"></a><span class="identifier">value_compare</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">iterator</span>                                <a name="boost.container.flat_map.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">const_iterator</span>                          <a name="boost.container.flat_map.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">reverse_iterator</span>                        <a name="boost.container.flat_map.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <span class="identifier">sequence_type</span><span class="special">::</span><span class="identifier">const_reverse_iterator</span>                  <a name="boost.container.flat_map.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                 <a name="boost.container.flat_map.movable_value_type"></a><span class="identifier">movable_value_type</span><span class="special">;</span>    

  <span class="comment">// <a class="link" href="flat_map.html#boost.container.flat_mapconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="flat_map.html#idm9050-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&amp;&amp;</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="flat_map.html#idm9057-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="flat_map.html#idm9066-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9075-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> <a class="link" href="flat_map.html#idm9086-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9099-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9114-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9129-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">,</span> 
             <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9146-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9166-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
             <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9188-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
             <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="flat_map.html#idm9212-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
             <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9234-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9243-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9254-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9265-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9278-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9294-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9312-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9332-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9342-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_move_constructible</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9354-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html#idm9366-bb"><span class="identifier">flat_map</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm9378-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm9389-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span> <span class="special">&amp;&amp;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_move_assignable</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm9403-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="flat_map.html#idm8024-bb">public member functions</a></span>
   <a class="link" href="flat_map.html#idm8025-bb"><span class="identifier">BOOST_STATIC_ASSERT</span></a><span class="special">(</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">allocator_type</span> <a class="link" href="flat_map.html#idm8029-bb"><span class="identifier">get_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">get_stored_allocator_noconst_return_t</span> <a class="link" href="flat_map.html#idm8036-bb"><span class="identifier">get_stored_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">get_stored_allocator_const_return_t</span> <a class="link" href="flat_map.html#idm8047-bb"><span class="identifier">get_stored_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8058-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8067-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8076-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8085-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="flat_map.html#idm8094-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="flat_map.html#idm8103-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="flat_map.html#idm8112-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="flat_map.html#idm8121-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8130-bb"><span class="identifier">cbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8139-bb"><span class="identifier">cend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="flat_map.html#idm8148-bb"><span class="identifier">crbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="flat_map.html#idm8157-bb"><span class="identifier">crend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm8166-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8175-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8184-bb"><span class="identifier">max_size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8193-bb"><span class="identifier">capacity</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8202-bb"><span class="identifier">reserve</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8214-bb"><span class="identifier">shrink_to_fit</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm8223-bb"><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm8233-bb"><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span></a><span class="special">(</span><span class="identifier">key_type</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8243-bb"><span class="identifier">insert_or_assign</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8256-bb"><span class="identifier">insert_or_assign</span></a><span class="special">(</span><span class="identifier">key_type</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8269-bb"><span class="identifier">insert_or_assign</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8284-bb"><span class="identifier">insert_or_assign</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">key_type</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8299-bb"><span class="identifier">nth</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8314-bb"><span class="identifier">nth</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8329-bb"><span class="identifier">index_of</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8344-bb"><span class="identifier">index_of</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">T</span> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm8359-bb"><span class="identifier">at</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm8368-bb"><span class="identifier">at</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8377-bb"><span class="identifier">emplace</span></a><span class="special">(</span><span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8393-bb"><span class="identifier">emplace_hint</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8411-bb"><span class="identifier">try_emplace</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8429-bb"><span class="identifier">try_emplace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8449-bb"><span class="identifier">try_emplace</span></a><span class="special">(</span><span class="identifier">key_type</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8467-bb"><span class="identifier">try_emplace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">key_type</span> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8487-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8500-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">value_type</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pair<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span> <span class="identifier">BOOST_MOVE_I</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8513-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">Pair</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8528-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8543-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">value_type</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pair<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8558-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Pair</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8575-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8592-bb"><span class="identifier">insert</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8616-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8627-bb"><span class="identifier">insert</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8645-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8661-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8677-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="flat_multimap.html" title="Class template flat_multimap">flat_multimap</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8693-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="flat_multimap.html" title="Class template flat_multimap">flat_multimap</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8709-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8722-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8733-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8748-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&amp;&amp;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_swappable</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm8760-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">key_compare</span> <a class="link" href="flat_map.html#idm8769-bb"><span class="identifier">key_comp</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">value_compare</span> <a class="link" href="flat_map.html#idm8776-bb"><span class="identifier">value_comp</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8783-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8792-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8801-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8814-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8827-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">size_type</span> <a class="link" href="flat_map.html#idm8836-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm8849-bb"><span class="identifier">contains</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm8858-bb"><span class="identifier">contains</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8871-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8880-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8889-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8902-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8915-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8924-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="flat_map.html#idm8933-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">const_iterator</span> <a class="link" href="flat_map.html#idm8946-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8959-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="flat_map.html#idm8968-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8977-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> <a class="link" href="flat_map.html#idm8990-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">sequence_type</span> <a class="link" href="flat_map.html#idm9003-bb"><span class="identifier">extract_sequence</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm9014-bb"><span class="identifier">adopt_sequence</span></a><span class="special">(</span><span class="identifier">sequence_type</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="flat_map.html#idm9025-bb"><span class="identifier">adopt_sequence</span></a><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">sequence_type</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">sequence_type</span> <span class="special">&amp;</span> <a class="link" href="flat_map.html#idm9041-bb"><span class="identifier">sequence</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="flat_map.html#idm9410-bb">friend functions</a></span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm9411-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm9424-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm9437-bb"><span class="keyword">operator</span><span class="special">&lt;</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm9450-bb"><span class="keyword">operator</span><span class="special">&gt;</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm9463-bb"><span class="keyword">operator</span><span class="special">&lt;=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="flat_map.html#idm9476-bb"><span class="keyword">operator</span><span class="special">&gt;=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">void</span> <a class="link" href="flat_map.html#idm9489-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">swap</span><span class="special">(</span><span class="identifier">y</span><span class="special">)</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.10.3.4"></a><h2>Description</h2>
<p>A <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> is a kind of associative container that supports unique keys (contains at most one of each key value) and provides for fast retrieval of values of another type T based on the keys.</p>
<p>A <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> satisfies all of the requirements of a container, a reversible container and an associative container. A <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> also provides most operations described for unique keys. For a flat_map&lt;Key,T&gt; the key_type is Key and the value_type is std::pair&lt;Key,T&gt; (unlike std::map&lt;Key, T&gt; which value_type is std::pair&lt;<span class="bold"><strong>const</strong></span> Key, T&gt;).</p>
<p><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> is similar to std::map but it's implemented by as an ordered sequence container. The underlying sequence container is by default <span class="emphasis"><em>vector</em></span> but it can also work user-provided vector-like SequenceContainers (like <span class="emphasis"><em><a class="link" href="static_vector.html" title="Class template static_vector">static_vector</a></em></span> or <span class="emphasis"><em><a class="link" href="small_vector.html" title="Class template small_vector">small_vector</a></em></span>).</p>
<p>Using vector-like sequence containers means that inserting a new element into a <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> might invalidate previous iterators and references (unless that sequence container is <span class="emphasis"><em><a class="link" href="stable_vector.html" title="Class template stable_vector">stable_vector</a></em></span> or a similar container that offers stable pointers and references). Similarly, erasing an element might invalidate iterators and references pointing to elements that come after (their keys are bigger) the erased element.</p>
<p>This container provides random-access iterators.</p>
<p>
</p>
<div class="refsect2">
<a name="id-********.10.3.4.8"></a><h3>Template Parameters</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Key</pre>
<p>is the key_type of the map </p>
</li>
<li class="listitem"><pre class="literallayout"><span class="keyword">typename</span> T</pre></li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Compare <span class="special">=</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">less</span><span class="special">&lt;</span><span class="identifier">Key</span><span class="special">&gt;</span></pre>
<p>is the ordering function for Keys (e.g. <span class="emphasis"><em>std::less&lt;Key&gt;</em></span>). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> AllocatorOrContainer <span class="special">=</span> <a class="link" href="new_allocator.html" title="Class template new_allocator">new_allocator</a><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">&gt;</span> <span class="special">&gt;</span></pre>
<p>is either:</p>
<div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; ">
<li class="listitem"><p>The allocator to allocate <code class="computeroutput">value_type</code>s (e.g. <span class="emphasis"><em>allocator&lt; std::pair&lt;Key, T&gt; &gt; </em></span>). (in this case <span class="emphasis"><em>sequence_type</em></span> will be vector&lt;value_type, AllocatorOrContainer&gt;)</p></li>
<li class="listitem"><p>The SequenceContainer to be used as the underlying <span class="emphasis"><em>sequence_type</em></span>. It must be a vector-like sequence container with random-access iterators. </p></li>
</ul></div>
<p>
</p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.10.3.4.9"></a><h3>
<a name="boost.container.flat_mapconstruct-copy-destruct"></a><code class="computeroutput">flat_map</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm9050-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&amp;&amp;</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Default constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm9057-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified allocator.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm9066-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9075-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and allocator.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9086-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> and and inserts elements from the range [first ,last ).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [first ,last ) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9099-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified allocator, and inserts elements from the range [first ,last ).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [first ,last ) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9114-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and and inserts elements from the range [first ,last ).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [first ,last ) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9129-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and allocator, and inserts elements from the range [first ,last ).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [first ,last ) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9146-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> and inserts elements from the ordered range [first ,last). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [first ,last) must be ordered according to the predicate.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9166-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and inserts elements from the ordered range [first ,last). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [first ,last) must be ordered according to the predicate.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9188-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and allocator, and inserts elements from the ordered range [first ,last). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [first ,last) must be ordered according to the predicate.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm9212-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified allocator and inserts elements from the ordered range [first ,last). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [first ,last) must be ordered according to the predicate.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9234-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> and inserts elements from the range [il.begin() ,il.end()).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [il.begin(), il.end()) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9243-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified allocator, and inserts elements from the range [il.begin() ,il.end()).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [il.begin(), il.end()) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9254-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and inserts elements from the range [il.begin() ,il.end()).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [il.begin(), il.end()) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9265-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and allocator, and inserts elements from the range [il.begin() ,il.end()).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if the range [il.begin(), il.end()) is already sorted using the predicate and otherwise N logN, where N is last - first. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9278-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using and inserts elements from the ordered unique range [il.begin(), il.end()). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [il.begin(), il.end()) must be ordered according to the predicate and must be unique values.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9294-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and inserts elements from the ordered unique range [il.begin(), il.end()). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [il.begin(), il.end()) must be ordered according to the predicate and must be unique values.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9312-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">Compare</span> <span class="special">&amp;</span> comp<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified comparison object and allocator, and inserts elements from the ordered unique range [il.begin(), il.end()). This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Requires</strong></span>: [il.begin(), il.end()) must be ordered according to the predicate and must be unique values.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9332-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in x.size(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9342-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_move_constructible</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructs a <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>. Constructs *this using x's resources.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x is emptied. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9354-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified allocator.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in x.size(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm9366-bb"></a><span class="identifier">flat_map</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructs a <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> using the specified allocator. Constructs *this using x's resources.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant if x.get_allocator() == a, linear otherwise. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> <a name="idm9378-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Makes *this a copy of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in x.size(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> <a name="idm9389-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span> <span class="special">&amp;&amp;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_move_assignable</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructs a <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>. Constructs *this using x's resources.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_traits_type::propagate_on_container_move_assignment is false and (allocation throws or value_type's move constructor throws)</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant if allocator_traits_type:: propagate_on_container_move_assignment is true or this-&gt;get&gt;allocator() == x.get_allocator(). Linear otherwise. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> <a name="idm9403-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<span class="bold"><strong>Effects</strong></span>: Assign elements from il to *this </li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.10.3.4.10"></a><h3>
<a name="idm8024-bb"></a><code class="computeroutput">flat_map</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"> <a name="idm8025-bb"></a><span class="identifier">BOOST_STATIC_ASSERT</span><span class="special">(</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_same</span><span class="special">&lt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&gt;</span><span class="special">,</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">allocator_type</span> <a name="idm8029-bb"></a><span class="identifier">get_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a copy of the allocator that was passed to the object's constructor.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">get_stored_allocator_noconst_return_t</span> <a name="idm8036-bb"></a><span class="identifier">get_stored_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">get_stored_allocator_const_return_t</span> <a name="idm8047-bb"></a><span class="identifier">get_stored_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8058-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element contained in the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8067-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the first element contained in the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8076-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the end of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8085-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the end of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm8094-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm8103-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm8112-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm8121-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8130-bb"></a><span class="identifier">cbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the first element contained in the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8139-bb"></a><span class="identifier">cend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the end of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm8148-bb"></a><span class="identifier">crbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm8157-bb"></a><span class="identifier">crend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm8166-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if the container contains no elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8175-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of the elements contained in the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8184-bb"></a><span class="identifier">max_size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the largest possible size of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8193-bb"></a><span class="identifier">capacity</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Number of elements for which memory has been allocated. capacity() is always greater than or equal to size().</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm8202-bb"></a><span class="identifier">reserve</span><span class="special">(</span><span class="identifier">size_type</span> cnt<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: If n is less than or equal to capacity(), or the underlying container has no <code class="computeroutput">reserve</code> member, this call has no effect. Otherwise, it is a request for allocation of additional memory. If the request is successful, then capacity() is greater than or equal to n; otherwise, capacity() is unchanged. In either case, size() is unchanged.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Note</strong></span>: If capacity() is less than "cnt", iterators and references to to values might be invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm8214-bb"></a><span class="identifier">shrink_to_fit</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<span class="bold"><strong>Effects</strong></span>: Tries to deallocate the excess of memory created <p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to size(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a name="idm8223-bb"></a><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">)</span><span class="special">;</span></pre>
<p>Effects: If there is no key equivalent to x in the <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>, inserts value_type(x, T()) into the <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>.</p>
<p>Returns: A reference to the mapped_type corresponding to x in *this.</p>
<p>Complexity: Logarithmic search time plus linear insertion time in case no equivalent key is present. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">mapped_type</span> <span class="special">&amp;</span> <a name="idm8233-bb"></a><span class="keyword">operator</span><span class="special">[</span><span class="special">]</span><span class="special">(</span><span class="identifier">key_type</span> <span class="special">&amp;&amp;</span> k<span class="special">)</span><span class="special">;</span></pre>
<p>Effects: If there is no key equivalent to x in the <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code>, inserts value_type(move(x), T()) into the <code class="computeroutput"><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a></code> (the key is move-constructed)</p>
<p>Returns: A reference to the mapped_type corresponding to x in *this.</p>
<p>Complexity: Logarithmic search time plus linear insertion time in case no equivalent key is present. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8243-bb"></a><span class="identifier">insert_or_assign</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span> obj<span class="special">)</span><span class="special">;</span></pre>
<p>Effects: If a key equivalent to k already exists in the container, assigns forward&lt;M&gt;(obj) to the mapped_type corresponding to the key k. If the key does not exist, inserts the new value as if by insert, constructing it from value_type(k, forward&lt;M&gt;(obj)).</p>
<p>No iterators or references are invalidated. If the insertion is successful, pointers and references to the element obtained while it is held in the node handle are invalidated, and pointers and references obtained to that element before it was extracted become valid.</p>
<p>Returns: The bool component is true if the insertion took place and false if the assignment took place. The iterator component is pointing at the element that was inserted or updated.</p>
<p>Complexity: Logarithmic search time plus linear insertion time in case no equivalent key is present. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8256-bb"></a><span class="identifier">insert_or_assign</span><span class="special">(</span><span class="identifier">key_type</span> <span class="special">&amp;&amp;</span> k<span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span> obj<span class="special">)</span><span class="special">;</span></pre>
<p>Effects: If a key equivalent to k already exists in the container, assigns forward&lt;M&gt;(obj) to the mapped_type corresponding to the key k. If the key does not exist, inserts the new value as if by insert, constructing it from value_type(k, move(obj)).</p>
<p>No iterators or references are invalidated. If the insertion is successful, pointers and references to the element obtained while it is held in the node handle are invalidated, and pointers and references obtained to that element before it was extracted become valid.</p>
<p>Returns: The bool component is true if the insertion took place and false if the assignment took place. The iterator component is pointing at the element that was inserted or updated.</p>
<p>Complexity: Logarithmic in the size of the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm8269-bb"></a><span class="identifier">insert_or_assign</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span> obj<span class="special">)</span><span class="special">;</span></pre>
<p>Effects: If a key equivalent to k already exists in the container, assigns forward&lt;M&gt;(obj) to the mapped_type corresponding to the key k. If the key does not exist, inserts the new value as if by insert, constructing it from value_type(k, forward&lt;M&gt;(obj)) and the new element to the container as close as possible to the position just before hint.</p>
<p>No iterators or references are invalidated. If the insertion is successful, pointers and references to the element obtained while it is held in the node handle are invalidated, and pointers and references obtained to that element before it was extracted become valid.</p>
<p>Returns: The bool component is true if the insertion took place and false if the assignment took place. The iterator component is pointing at the element that was inserted or updated.</p>
<p>Complexity: Logarithmic in the size of the container in general, but amortized constant if the new element is inserted just before hint. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> M<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm8284-bb"></a><span class="identifier">insert_or_assign</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">key_type</span> <span class="special">&amp;&amp;</span> k<span class="special">,</span> <span class="identifier">M</span> <span class="special">&amp;&amp;</span> obj<span class="special">)</span><span class="special">;</span></pre>
<p>Effects: If a key equivalent to k already exists in the container, assigns forward&lt;M&gt;(obj) to the mapped_type corresponding to the key k. If the key does not exist, inserts the new value as if by insert, constructing it from value_type(k, move(obj)) and the new element to the container as close as possible to the position just before hint.</p>
<p>No iterators or references are invalidated. If the insertion is successful, pointers and references to the element obtained while it is held in the node handle are invalidated, and pointers and references obtained to that element before it was extracted become valid.</p>
<p>Returns: The bool component is true if the insertion took place and false if the assignment took place. The iterator component is pointing at the element that was inserted or updated.</p>
<p>Complexity: Logarithmic in the size of the container in general, but amortized constant if the new element is inserted just before hint. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8299-bb"></a><span class="identifier">nth</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: size() &gt;= n.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the nth element from the beginning of the container. Returns end() if n == size().</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8314-bb"></a><span class="identifier">nth</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: size() &gt;= n.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the nth element from the beginning of the container. Returns end() if n == size().</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8329-bb"></a><span class="identifier">index_of</span><span class="special">(</span><span class="identifier">iterator</span> p<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: begin() &lt;= p &lt;= end().</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns the index of the element pointed by p and size() if p == end().</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8344-bb"></a><span class="identifier">index_of</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: begin() &lt;= p &lt;= end().</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns the index of the element pointed by p and size() if p == end().</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">T</span> <span class="special">&amp;</span> <a name="idm8359-bb"></a><span class="identifier">at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">)</span><span class="special">;</span></pre>
<p>Returns: A reference to the element whose key is equivalent to x.</p>
<p>Throws: An exception object of type <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if no such element is present.</p>
<p>Complexity: logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> <a name="idm8368-bb"></a><span class="identifier">at</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Returns: A reference to the element whose key is equivalent to x.</p>
<p>Throws: An exception object of type <code class="computeroutput"><a class="link" href="out_of_range.html" title="Class out_of_range">out_of_range</a></code> if no such element is present.</p>
<p>Complexity: logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8377-bb"></a><span class="identifier">emplace</span><span class="special">(</span><span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an object x of type T constructed with std::forward&lt;Args&gt;(args)... if and only if there is no element in the container with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Returns</strong></span>: The bool component of the returned pair is true if and only if the insertion takes place, and the iterator component of the pair points to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus linear insertion to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm8393-bb"></a><span class="identifier">emplace_hint</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an object of type T constructed with std::forward&lt;Args&gt;(args)... in the container if and only if there is no element in the container with key equivalent to the key of x. p is a hint pointing to where the insert should start to search.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time (constant if x is inserted right before p) plus insertion linear to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8411-bb"></a><span class="identifier">try_emplace</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value_type shall be EmplaceConstructible into map from piecewise_construct, forward_as_tuple(k), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Effects</strong></span>: If the map already contains an element whose key is equivalent to k, there is no effect. Otherwise inserts an object of type value_type constructed with piecewise_construct, forward_as_tuple(k), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Returns</strong></span>: The bool component of the returned pair is true if and only if the insertion took place. The returned iterator points to the map element whose key is equivalent to k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm8429-bb"></a><span class="identifier">try_emplace</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> k<span class="special">,</span> 
                       <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value_type shall be EmplaceConstructible into map from piecewise_construct, forward_as_tuple(k), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Effects</strong></span>: If the map already contains an element whose key is equivalent to k, there is no effect. Otherwise inserts an object of type value_type constructed with piecewise_construct, forward_as_tuple(k), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Returns</strong></span>: The returned iterator points to the map element whose key is equivalent to k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but amortized constant if value is inserted right before p. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8449-bb"></a><span class="identifier">try_emplace</span><span class="special">(</span><span class="identifier">key_type</span> <span class="special">&amp;&amp;</span> k<span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value_type shall be EmplaceConstructible into map from piecewise_construct, forward_as_tuple(move(k)), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Effects</strong></span>: If the map already contains an element whose key is equivalent to k, there is no effect. Otherwise inserts an object of type value_type constructed with piecewise_construct, forward_as_tuple(move(k)), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Returns</strong></span>: The bool component of the returned pair is true if and only if the insertion took place. The returned iterator points to the map element whose key is equivalent to k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus linear insertion time in case the key is not present. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm8467-bb"></a><span class="identifier">try_emplace</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">key_type</span> <span class="special">&amp;&amp;</span> k<span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value_type shall be EmplaceConstructible into map from piecewise_construct, forward_as_tuple(move(k)), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Effects</strong></span>: If the map already contains an element whose key is equivalent to k, there is no effect. Otherwise inserts an object of type value_type constructed with piecewise_construct, forward_as_tuple(move(k)), forward_as_tuple(forward&lt;Args&gt;(args)...).</p>
<p><span class="bold"><strong>Returns</strong></span>: The returned iterator points to the map element whose key is equivalent to k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but amortized constant if value is inserted right before p. Linear insertion time in case no equivalent key is present. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8487-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x if and only if there is no element in the container with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Returns</strong></span>: The bool component of the returned pair is true if and only if the insertion takes place, and the iterator component of the pair points to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus linear insertion to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8500-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">value_type</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a new value_type move constructed from the pair if and only if there is no element in the container with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Returns</strong></span>: The bool component of the returned pair is true if and only if the insertion takes place, and the iterator component of the pair points to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus linear insertion to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pair<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span> <span class="identifier">BOOST_MOVE_I</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm8513-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">Pair</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a new value_type constructed from the pair if and only if there is no element in the container with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Returns</strong></span>: The bool component of the returned pair is true if and only if the insertion takes place, and the iterator component of the pair points to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus linear insertion to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8528-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a copy of x in the container if and only if there is no element in the container with key equivalent to the key of x. p is a hint pointing to where the insert should start to search.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time (constant if x is inserted right before p) plus insertion linear to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8543-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">value_type</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an element move constructed from x in the container. p is a hint pointing to where the insert should start to search.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time (constant if x is inserted right before p) plus insertion linear to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pair<span class="special">&gt;</span> <span class="identifier">iterator</span> <a name="idm8558-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">Pair</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an element constructed from x in the container. p is a hint pointing to where the insert should start to search.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the element with key equivalent to the key of x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time (constant if x is inserted right before p) plus insertion linear to the elements with bigger keys than x.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm8575-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: first, last are not iterators into *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: inserts each element from the range [first,last) if and only if there is no element with key equivalent to the key of that element.</p>
<p><span class="bold"><strong>Complexity</strong></span>: N log(size()+N).</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm8592-bb"></a><span class="identifier">insert</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: first, last are not iterators into *this.</p>
<p><span class="bold"><strong>Requires</strong></span>: [first ,last) must be ordered according to the predicate and must be unique values.</p>
<p><span class="bold"><strong>Effects</strong></span>: inserts each element from the range [first,last) if and only if there is no element with key equivalent to the key of that element. This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm8616-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: inserts each element from the range [il.begin(), il.end()) if and only if there is no element with key equivalent to the key of that element.</p>
<p><span class="bold"><strong>Complexity</strong></span>: N log(N).</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm8627-bb"></a><span class="identifier">insert</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: [il.begin(), il.end()) must be ordered according to the predicate and must be unique values.</p>
<p><span class="bold"><strong>Effects</strong></span>: inserts each element from the range [il.begin(), il.end()) if and only if there is no element with key equivalent to the key of that element. This function is more efficient than the normal range creation for ordered ranges.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear.</p>
<p><span class="bold"><strong>Note</strong></span>: If an element is inserted it might invalidate elements.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm8645-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;</span> source<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: this-&gt;get_allocator() == source.get_allocator().</p>
<p><span class="bold"><strong>Effects</strong></span>: Move-inserts each element from source into *this a using the comparison object of *this. If there is an element in a with key equivalent to the key of an element from source, then that element is not moved from source.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in this-&gt;size() + source.size().</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates all iterators and references. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm8661-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> source<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: this-&gt;get_allocator() == source.get_allocator().</p>
<p><span class="bold"><strong>Effects</strong></span>: Move-inserts each element from source into *this a using the comparison object of *this. If there is an element in a with key equivalent to the key of an element from source, then that element is not moved from source.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in this-&gt;size() + source.size().</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates all iterators and references. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm8677-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="flat_multimap.html" title="Class template flat_multimap">flat_multimap</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;</span> source<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: this-&gt;get_allocator() == source.get_allocator().</p>
<p><span class="bold"><strong>Effects</strong></span>: Move-inserts each element from source into *this a using the comparison object of *this. If there is an element in a with key equivalent to the key of an element from source, then that element is not moved from source.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in this-&gt;size() + source.size().</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates all iterators and references. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> C2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm8693-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="flat_multimap.html" title="Class template flat_multimap">flat_multimap</a><span class="special">&lt;</span> <span class="identifier">Key</span><span class="special">,</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">C2</span><span class="special">,</span> <span class="identifier">AllocatorOrContainer</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> source<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: this-&gt;get_allocator() == source.get_allocator().</p>
<p><span class="bold"><strong>Effects</strong></span>: Move-inserts each element from source into *this a using the comparison object of *this. If there is an element in a with key equivalent to the key of an element from source, then that element is not moved from source.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in this-&gt;size() + source.size().</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates all iterators and references. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8709-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases the element pointed to by p.</p>
<p><span class="bold"><strong>Returns</strong></span>: Returns an iterator pointing to the element immediately following q prior to the element being erased. If no such element exists, returns end().</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the elements with keys bigger than p</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates elements with keys not less than the erased element. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8722-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: If present, erases the element in the container with key equivalent to x.</p>
<p><span class="bold"><strong>Returns</strong></span>: Returns the number of erased elements (0/1).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus erasure time linear to the elements with bigger keys. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8733-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> first<span class="special">,</span> <span class="identifier">const_iterator</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements in the range [first, last).</p>
<p><span class="bold"><strong>Returns</strong></span>: Returns last.</p>
<p><span class="bold"><strong>Complexity</strong></span>: size()*N where N is the distance from first to last.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic search time plus erasure time linear to the elements with bigger keys. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm8748-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span> <span class="special">&amp;&amp;</span><span class="identifier">boost</span><span class="special">::</span><span class="identifier">container</span><span class="special">::</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_swappable</span><span class="special">&lt;</span> <span class="identifier">Compare</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Swaps the contents of *this and x.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm8760-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: erase(begin(),end()).</p>
<p><span class="bold"><strong>Postcondition</strong></span>: size() == 0.</p>
<p><span class="bold"><strong>Complexity</strong></span>: linear in size(). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">key_compare</span> <a name="idm8769-bb"></a><span class="identifier">key_comp</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the comparison object out of which a was constructed.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">value_compare</span> <a name="idm8776-bb"></a><span class="identifier">value_comp</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an object of value_compare constructed out of the comparison object.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8783-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to an element with the key equivalent to x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8792-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: A const_iterator pointing to an element with the key equivalent to x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">iterator</span> <a name="idm8801-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to an element with the key equivalent to x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">const_iterator</span> <a name="idm8814-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: A const_iterator pointing to an element with the key equivalent to x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm8827-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: The number of elements with key equivalent to x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: log(size())+count(k) </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">size_type</span> <a name="idm8836-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of elements with key equivalent to x.</p>
<p><span class="bold"><strong>Complexity</strong></span>: log(size())+count(k) </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm8849-bb"></a><span class="identifier">contains</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: Returns true if there is an element with key equivalent to key in the container, otherwise false.</p>
<p><span class="bold"><strong>Complexity</strong></span>: log(size()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="keyword">bool</span> <a name="idm8858-bb"></a><span class="identifier">contains</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: Returns true if there is an element with key equivalent to key in the container, otherwise false.</p>
<p><span class="bold"><strong>Complexity</strong></span>: log(size()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8871-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the first element with key not less than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8880-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: A const iterator pointing to the first element with key not less than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">iterator</span> <a name="idm8889-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the first element with key not less than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">const_iterator</span> <a name="idm8902-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: A const iterator pointing to the first element with key not less than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm8915-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the first element with key greater than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm8924-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: A const iterator pointing to the first element with key greater than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">iterator</span> <a name="idm8933-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator pointing to the first element with key greater than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">const_iterator</span> <a name="idm8946-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Returns</strong></span>: A const iterator pointing to the first element with key greater than x, or end() if such an element is not found.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a name="idm8959-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to std::make_pair(this-&gt;lower_bound(k), this-&gt;upper_bound(k)).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
<a name="idm8968-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to std::make_pair(this-&gt;lower_bound(k), this-&gt;upper_bound(k)).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a name="idm8977-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to std::make_pair(this-&gt;lower_bound(k), this-&gt;upper_bound(k)).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> K<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> <a name="idm8990-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">K</span> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: This overload is available only if key_compare::is_transparent exists.</p>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to std::make_pair(this-&gt;lower_bound(k), this-&gt;upper_bound(k)).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">sequence_type</span> <a name="idm9003-bb"></a><span class="identifier">extract_sequence</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Extracts the internal sequence container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Same as the move constructor of sequence_type, usually constant.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: this-&gt;empty()</p>
<p><span class="bold"><strong>Throws</strong></span>: If secuence_type's move constructor throws </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm9014-bb"></a><span class="identifier">adopt_sequence</span><span class="special">(</span><span class="identifier">sequence_type</span> <span class="special">&amp;&amp;</span> seq<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Discards the internally hold sequence container and adopts the one passed externally using the move assignment. Erases non-unique elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Assuming O(1) move assignment, O(NlogN) with N = seq.size()</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison or the move constructor throws </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm9025-bb"></a><span class="identifier">adopt_sequence</span><span class="special">(</span><a class="link" href="ordered_unique_range_t.html" title="Struct ordered_unique_range_t">ordered_unique_range_t</a><span class="special">,</span> <span class="identifier">sequence_type</span> <span class="special">&amp;&amp;</span> seq<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: seq shall be ordered according to this-&gt;compare() and shall contain unique elements.</p>
<p><span class="bold"><strong>Effects</strong></span>: Discards the internally hold sequence container and adopts the one passed externally using the move assignment.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Assuming O(1) move assignment, O(1)</p>
<p><span class="bold"><strong>Throws</strong></span>: If the move assignment throws </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">sequence_type</span> <span class="special">&amp;</span> <a name="idm9041-bb"></a><span class="identifier">sequence</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const view of the underlying sequence.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.10.3.4.11"></a><h3>
<a name="idm9410-bb"></a><code class="computeroutput">flat_map</code> friend functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm9411-bb"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x and y are equal</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm9424-bb"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x and y are unequal</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm9437-bb"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is less than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm9450-bb"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is greater than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm9463-bb"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is equal or less than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm9476-bb"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is equal or greater than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">void</span> <a name="idm9489-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="flat_map.html" title="Class template flat_map">flat_map</a> <span class="special">&amp;</span> y<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">swap</span><span class="special">(</span><span class="identifier">y</span><span class="special">)</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: x.swap(y)</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2018 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pmr/devector_of.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_container_header_reference.html#header.boost.container.flat_map_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="flat_multimap.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
