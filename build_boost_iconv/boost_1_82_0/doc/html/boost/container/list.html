<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template list</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../boost_container_header_reference.html#header.boost.container.list_hpp" title="Header &lt;boost/container/list.hpp&gt;">
<link rel="prev" href="pmr/flat_set_of.html" title="Struct template flat_set_of">
<link rel="next" href="pmr/list_of.html" title="Struct template list_of">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pmr/flat_set_of.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_container_header_reference.html#header.boost.container.list_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="pmr/list_of.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.container.list"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template list</span></h2>
<p>boost::container::list</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../boost_container_header_reference.html#header.boost.container.list_hpp" title="Header &lt;boost/container/list.hpp&gt;">boost/container/list.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">:</span> <span class="keyword">protected</span> dtl::node_alloc_holder&lt; real_allocator&lt; T, Allocator &gt;::type, dtl::intrusive_list_type&lt; real_allocator&lt; T, Allocator &gt;::type &gt;::type &gt;
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">T</span>                                                                       <a name="boost.container.list.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>            
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">pointer</span>         <a name="boost.container.list.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>               
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_pointer</span>   <a name="boost.container.list.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">reference</span>       <a name="boost.container.list.reference"></a><span class="identifier">reference</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_reference</span> <a name="boost.container.list.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">size_type</span>       <a name="boost.container.list.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="special">::</span><a class="link" href="allocator_traits.html" title="Struct template allocator_traits">boost::container::allocator_traits</a><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">difference_type</span> <a name="boost.container.list.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">ValueAllocator</span>                                                          <a name="boost.container.list.allocator_type"></a><span class="identifier">allocator_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.list.stored_allocator_type"></a><span class="identifier">stored_allocator_type</span><span class="special">;</span> 
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.list.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.list.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.list.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                                                  <a name="boost.container.list.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="list.html#boost.container.listconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="list.html#idm14692-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="list.html#idm14700-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="list.html#idm14710-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14720-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14732-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">ValueAllocator</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">ValueAllocator</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14747-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14760-bb"><span class="identifier">list</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="list.html#idm14771-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14786-bb"><span class="identifier">list</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span> 
    <a class="link" href="list.html#idm14799-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="identifier">InpIt</span><span class="special">,</span> <span class="identifier">InpIt</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">ValueAllocator</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">ValueAllocator</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14816-bb"><span class="identifier">list</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">,</span> 
       <span class="keyword">const</span> <span class="identifier">ValueAllocator</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">ValueAllocator</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> <a class="link" href="list.html#idm14837-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> <a class="link" href="list.html#idm14852-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> <a class="link" href="list.html#idm14867-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="list.html#idm14829-bb"><span class="special">~</span><span class="identifier">list</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="list.html#idm13837-bb">public member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm13838-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="list.html#idm13851-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">InpIt</span><span class="special">,</span> <span class="identifier">InpIt</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm13866-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">allocator_type</span> <a class="link" href="list.html#idm13877-bb"><span class="identifier">get_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a class="link" href="list.html#idm13886-bb"><span class="identifier">get_stored_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a class="link" href="list.html#idm13897-bb"><span class="identifier">get_stored_allocator</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm13908-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="list.html#idm13917-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm13926-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="list.html#idm13935-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="list.html#idm13944-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="list.html#idm13953-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="list.html#idm13962-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="list.html#idm13971-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="list.html#idm13980-bb"><span class="identifier">cbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="list.html#idm13989-bb"><span class="identifier">cend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="list.html#idm13998-bb"><span class="identifier">crbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="list.html#idm14007-bb"><span class="identifier">crend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="list.html#idm14016-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="list.html#idm14025-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="list.html#idm14034-bb"><span class="identifier">max_size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14043-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14054-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="list.html#idm14067-bb"><span class="identifier">front</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="list.html#idm14078-bb"><span class="identifier">front</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reference</span> <a class="link" href="list.html#idm14089-bb"><span class="identifier">back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reference</span> <a class="link" href="list.html#idm14100-bb"><span class="identifier">back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">reference</span> <a class="link" href="list.html#idm14111-bb"><span class="identifier">emplace_back</span></a><span class="special">(</span><span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">reference</span> <a class="link" href="list.html#idm14127-bb"><span class="identifier">emplace_front</span></a><span class="special">(</span><span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="list.html#idm14143-bb"><span class="identifier">emplace</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14159-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14170-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">T</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14181-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14192-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">T</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm14203-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm14220-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">T</span> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm14237-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span> <span class="identifier">iterator</span> <a class="link" href="list.html#idm14256-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">InpIt</span><span class="special">,</span> <span class="identifier">InpIt</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm14277-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14294-bb"><span class="identifier">pop_front</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14303-bb"><span class="identifier">pop_back</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm14312-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="list.html#idm14325-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14340-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_swap</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14352-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14361-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14379-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14397-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14417-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14437-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14459-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14481-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> 
              <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14507-bb"><span class="identifier">splice</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> 
              <span class="identifier">size_type</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14533-bb"><span class="identifier">remove</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pred<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="list.html#idm14546-bb"><span class="identifier">remove_if</span></a><span class="special">(</span><span class="identifier">Pred</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14561-bb"><span class="identifier">unique</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BinaryPredicate<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="list.html#idm14572-bb"><span class="identifier">unique</span></a><span class="special">(</span><span class="identifier">BinaryPredicate</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14587-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14601-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StrictWeakOrdering<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="list.html#idm14615-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StrictWeakOrdering</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StrictWeakOrdering<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="list.html#idm14635-bb"><span class="identifier">merge</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">StrictWeakOrdering</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14655-bb"><span class="identifier">sort</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StrictWeakOrdering<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="list.html#idm14666-bb"><span class="identifier">sort</span></a><span class="special">(</span><span class="identifier">StrictWeakOrdering</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="list.html#idm14681-bb"><span class="identifier">reverse</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="list.html#idm14881-bb">friend functions</a></span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="list.html#idm14882-bb"><span class="keyword">operator</span><span class="special">==</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="list.html#idm14895-bb"><span class="keyword">operator</span><span class="special">!=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="list.html#idm14908-bb"><span class="keyword">operator</span><span class="special">&lt;</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="list.html#idm14921-bb"><span class="keyword">operator</span><span class="special">&gt;</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="list.html#idm14934-bb"><span class="keyword">operator</span><span class="special">&lt;=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">bool</span> <a class="link" href="list.html#idm14947-bb"><span class="keyword">operator</span><span class="special">&gt;=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">friend</span> <span class="keyword">void</span> <a class="link" href="list.html#idm14960-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">swap</span><span class="special">(</span><span class="identifier">y</span><span class="special">)</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.14.3.4"></a><h2>Description</h2>
<p>A list is a doubly linked list. That is, it is a Sequence that supports both forward and backward traversal, and (amortized) constant time insertion and removal of elements at the beginning or the end, or in the middle. Lists have the important property that insertion and splicing do not invalidate iterators to list elements, and that even removal invalidates only the iterators that point to the elements that are removed. The ordering of iterators may be changed (that is, list&lt;T&gt;::iterator might have a different predecessor or successor after a list operation than it did before), but the iterators themselves will not be invalidated or made to point to different elements unless that invalidation or mutation is explicit.</p>
<p>
</p>
<div class="refsect2">
<a name="id-********.********"></a><h3>Template Parameters</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> T</pre>
<p>The type of object that is stored in the list </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">typename</span> Allocator <span class="special">=</span> <span class="keyword">void</span></pre>
<p>The allocator used for all internal memory management, use void for the default allocator </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.14.3.4.5"></a><h3>
<a name="boost.container.listconstruct-copy-destruct"></a><code class="computeroutput">list</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm14692-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">dtl</span><span class="special">::</span><span class="identifier">is_nothrow_default_constructible</span><span class="special">&lt;</span> <span class="identifier">ValueAllocator</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Default constructs a list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm14700-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a list taking the allocator as parameter.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm14710-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a list and inserts n value-initialized value_types.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws or T's default or copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14720-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a list that will use a copy of allocator a and inserts n copies of value.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws or T's default or copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14732-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> value<span class="special">,</span> 
     <span class="keyword">const</span> <span class="identifier">ValueAllocator</span> <span class="special">&amp;</span> a <span class="special">=</span> <span class="identifier">ValueAllocator</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a list that will use a copy of allocator a and inserts n copies of value.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws or T's default or copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14747-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a list.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x == *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the elements x contains. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14760-bb"></a><span class="identifier">list</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructor. Moves x's resources to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14771-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Copy constructs a list using the specified allocator.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x == *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor or copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the elements x contains. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14786-bb"></a><span class="identifier">list</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> a<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move constructor sing the specified allocator. Moves x's resources to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocation or value_type's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant if a == x.get_allocator(), linear otherwise. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span> 
  <a name="idm14799-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">InpIt</span> first<span class="special">,</span> <span class="identifier">InpIt</span> last<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">ValueAllocator</span> <span class="special">&amp;</span> a <span class="special">=</span> <span class="identifier">ValueAllocator</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a list that will use a copy of allocator a and inserts a copy of the range [first, last) in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws or T's constructor taking a dereferenced InIt throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the range [first, last). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14816-bb"></a><span class="identifier">list</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">,</span> 
     <span class="keyword">const</span> <span class="identifier">ValueAllocator</span> <span class="special">&amp;</span> a <span class="special">=</span> <span class="identifier">ValueAllocator</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a list that will use a copy of allocator a and inserts a copy of the range [il.begin(), il.end()) in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_type's default constructor throws or T's constructor taking a dereferenced std::initializer_list iterator throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the range [il.begin(), il.end()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> <a name="idm14837-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Makes *this contain the same elements as x.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: this-&gt;size() == x.size(). *this contains a copy of each of x's elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in x. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> <a name="idm14852-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_move_assignment</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Move assignment. All x's values are transferred to *this.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: x.empty(). *this contains a the elements x had before the function.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator_traits_type::propagate_on_container_move_assignment is false and (allocation throws or value_type's move constructor throws)</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant if allocator_traits_type:: propagate_on_container_move_assignment is true or this-&gt;get&gt;allocator() == x.get_allocator(). Linear otherwise. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> <a name="idm14867-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Makes *this contain the same elements as il.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: this-&gt;size() == il.size(). *this contains a copy of each of x's elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in x. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm14829-bb"></a><span class="special">~</span><span class="identifier">list</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Destroys the list. All stored values are destroyed and used memory is deallocated.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.14.3.4.6"></a><h3>
<a name="idm13837-bb"></a><code class="computeroutput">list</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm13838-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> val<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Assigns the n copies of val to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm13851-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">InpIt</span> first<span class="special">,</span> <span class="identifier">InpIt</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Assigns the range [first, last) to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's constructor from dereferencing InpIt throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm13866-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Assigns the range [il.begin(), il.end()) to *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's constructor from dereferencing std::initializer_list iterator throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">allocator_type</span> <a name="idm13877-bb"></a><span class="identifier">get_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a copy of the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: If allocator's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a name="idm13886-bb"></a><span class="identifier">get_stored_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">stored_allocator_type</span> <span class="special">&amp;</span> <a name="idm13897-bb"></a><span class="identifier">get_stored_allocator</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the internal allocator.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm13908-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element contained in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm13917-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the first element contained in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm13926-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the end of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm13935-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the end of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm13944-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the beginning of the reversed list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm13953-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm13962-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the end of the reversed list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm13971-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm13980-bb"></a><span class="identifier">cbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the first element contained in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm13989-bb"></a><span class="identifier">cend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator to the end of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm13998-bb"></a><span class="identifier">crbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm14007-bb"></a><span class="identifier">crend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm14016-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if the list contains no elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm14025-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of the elements contained in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm14034-bb"></a><span class="identifier">max_size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the largest possible size of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14043-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> new_size<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts or erases elements at the end such that the size becomes n. New elements are value initialized.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the difference between size() and new_size. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14054-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> new_size<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts or erases elements at the end such that the size becomes n. New elements are copy constructed from x.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the difference between size() and new_size. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm14067-bb"></a><span class="identifier">front</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the first element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm14078-bb"></a><span class="identifier">front</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the first element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reference</span> <a name="idm14089-bb"></a><span class="identifier">back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reference to the first element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reference</span> <a name="idm14100-bb"></a><span class="identifier">back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: !empty()</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the first element from the beginning of the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">reference</span> <a name="idm14111-bb"></a><span class="identifier">emplace_back</span><span class="special">(</span><span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an object of type T constructed with std::forward&lt;Args&gt;(args)... in the end of the list.</p>
<p><span class="bold"><strong>Returns</strong></span>: A reference to the created object.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's in-place constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> <span class="identifier">reference</span> <a name="idm14127-bb"></a><span class="identifier">emplace_front</span><span class="special">(</span><span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an object of type T constructed with std::forward&lt;Args&gt;(args)... in the beginning of the list.</p>
<p><span class="bold"><strong>Returns</strong></span>: A reference to the created object.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's in-place constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">class</span><span class="special">...</span> Args<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm14143-bb"></a><span class="identifier">emplace</span><span class="special">(</span><span class="identifier">const_iterator</span> position<span class="special">,</span> <span class="identifier">Args</span> <span class="special">&amp;&amp;</span><span class="special">...</span> args<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts an object of type T constructed with std::forward&lt;Args&gt;(args)... before p.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's in-place constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14159-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a copy of x at the beginning of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14170-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">T</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a new element in the beginning of the list and moves the resources of x to this new element.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14181-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a copy of x at the end of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14192-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">T</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a new element in the end of the list and moves the resources of x to this new element.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm14203-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a valid iterator of *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Insert a copy of x before p.</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the inserted element.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or x's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm14220-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">T</span> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a valid iterator of *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Insert a new element before p with x's resources.</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the inserted element.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm14237-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> position<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a valid iterator of *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts n copies of x before p.</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the first inserted element or p if n is 0.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws or T's copy constructor throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to n. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InpIt<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm14256-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">InpIt</span> first<span class="special">,</span> <span class="identifier">InpIt</span> last<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a valid iterator of *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Insert a copy of the [first, last) range before p.</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the first inserted element or p if first == last.</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, T's constructor from a dereferenced InpIt throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to distance [first, last). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm14277-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <span class="identifier">std</span><span class="special">::</span><span class="identifier">initializer_list</span><span class="special">&lt;</span> <span class="identifier">value_type</span> <span class="special">&gt;</span> il<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a valid iterator of *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Insert a copy of the [il.begin(), il.end()) range before p.</p>
<p><span class="bold"><strong>Returns</strong></span>: an iterator to the first inserted element or p if if.begin() == il.end().</p>
<p><span class="bold"><strong>Throws</strong></span>: If memory allocation throws, T's constructor from a dereferenced std::initializer_list iterator throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to distance [il.begin(), il.end()). </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14294-bb"></a><span class="identifier">pop_front</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes the first element from the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14303-bb"></a><span class="identifier">pop_back</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes the last element from the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm14312-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a valid iterator of *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases the element at p.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Amortized constant time. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm14325-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> first<span class="special">,</span> <span class="identifier">const_iterator</span> last<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: first and last must be valid iterator to elements in *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases the elements pointed by [first, last).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the distance between first and last. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14340-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">propagate_on_container_swap</span><span class="special">::</span><span class="identifier">value</span><span class="special">||</span><span class="identifier">allocator_traits_type</span><span class="special">::</span><span class="identifier">is_always_equal</span><span class="special">::</span><span class="identifier">value</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Swaps the contents of *this and x.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14352-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements of the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the list. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14361-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by the list. x != *this. this' allocator and x's allocator shall compare equal</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers all the elements of list x to this list, before the the element pointed by p. No destructors or copy constructors are called.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14379-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by the list. x != *this. this' allocator and x's allocator shall compare equal</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers all the elements of list x to this list, before the the element pointed by p. No destructors or copy constructors are called.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14397-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="identifier">const_iterator</span> i<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by this list. i must point to an element contained in list x. this' allocator and x's allocator shall compare equal</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers the value pointed by i, from list x to this list, before the element pointed by p. No destructors or copy constructors are called. If p == i or p == ++i, this function is a null operation.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14417-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">,</span> <span class="identifier">const_iterator</span> i<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by this list. i must point to an element contained in list x. this' allocator and x's allocator shall compare equal.</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers the value pointed by i, from list x to this list, before the element pointed by p. No destructors or copy constructors are called. If p == i or p == ++i, this function is a null operation.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14437-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="identifier">const_iterator</span> first<span class="special">,</span> 
            <span class="identifier">const_iterator</span> last<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by this list. first and last must point to elements contained in list x. this' allocator and x's allocator shall compare equal</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers the range pointed by first and last from list x to this list, before the element pointed by p. No destructors or copy constructors are called.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements transferred.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14459-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">,</span> <span class="identifier">const_iterator</span> first<span class="special">,</span> 
            <span class="identifier">const_iterator</span> last<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by this list. first and last must point to elements contained in list x. this' allocator and x's allocator shall compare equal.</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers the range pointed by first and last from list x to this list, before the element pointed by p. No destructors or copy constructors are called.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements transferred.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14481-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="identifier">const_iterator</span> first<span class="special">,</span> 
            <span class="identifier">const_iterator</span> last<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by this list. first and last must point to elements contained in list x. n == distance(first, last). this' allocator and x's allocator shall compare equal</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers the range pointed by first and last from list x to this list, before the element pointed by p. No destructors or copy constructors are called.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14507-bb"></a><span class="identifier">splice</span><span class="special">(</span><span class="identifier">const_iterator</span> p<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">,</span> <span class="identifier">const_iterator</span> first<span class="special">,</span> 
            <span class="identifier">const_iterator</span> last<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must point to an element contained by this list. first and last must point to elements contained in list x. n == distance(first, last). this' allocator and x's allocator shall compare equal</p>
<p><span class="bold"><strong>Effects</strong></span>: Transfers the range pointed by first and last from list x to this list, before the element pointed by p. No destructors or copy constructors are called.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators of values obtained from list x now point to elements of this list. Iterators of this list and all the references are not invalidated.</p>
<p><span class="bold"><strong>Note</strong></span>: Non-standard extension </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14533-bb"></a><span class="identifier">remove</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">T</span> <span class="special">&amp;</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes all the elements that compare equal to value.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comparison throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear time. It performs exactly size() comparisons for equality.</p>
<p><span class="bold"><strong>Note</strong></span>: The relative order of elements that are not removed is unchanged, and iterators to elements that are not removed remain valid. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Pred<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm14546-bb"></a><span class="identifier">remove_if</span><span class="special">(</span><span class="identifier">Pred</span> pred<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes all the elements for which a specified predicate is satisfied.</p>
<p><span class="bold"><strong>Throws</strong></span>: If pred throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear time. It performs exactly size() calls to the predicate.</p>
<p><span class="bold"><strong>Note</strong></span>: The relative order of elements that are not removed is unchanged, and iterators to elements that are not removed remain valid. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14561-bb"></a><span class="identifier">unique</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes adjacent duplicate elements or adjacent elements that are equal from the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comparison throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear time (size()-1 comparisons equality comparisons).</p>
<p><span class="bold"><strong>Note</strong></span>: The relative order of elements that are not removed is unchanged, and iterators to elements that are not removed remain valid. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> BinaryPredicate<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm14572-bb"></a><span class="identifier">unique</span><span class="special">(</span><span class="identifier">BinaryPredicate</span> binary_pred<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Removes adjacent duplicate elements or adjacent elements that satisfy some binary predicate from the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: If pred throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear time (size()-1 comparisons calls to pred()).</p>
<p><span class="bold"><strong>Note</strong></span>: The relative order of elements that are not removed is unchanged, and iterators to elements that are not removed remain valid. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14587-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: The lists x and *this must be distinct.</p>
<p><span class="bold"><strong>Effects</strong></span>: This function removes all of x's elements and inserts them in order into *this according to std::less&lt;value_type&gt;. The merge is stable; that is, if an element from *this is equivalent to one from x, then the element from *this will precede the one from x.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comparison throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: This function is linear time: it performs at most size() + x.size() - 1 comparisons. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14601-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: The lists x and *this must be distinct.</p>
<p><span class="bold"><strong>Effects</strong></span>: This function removes all of x's elements and inserts them in order into *this according to std::less&lt;value_type&gt;. The merge is stable; that is, if an element from *this is equivalent to one from x, then the element from *this will precede the one from x.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comparison throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: This function is linear time: it performs at most size() + x.size() - 1 comparisons. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StrictWeakOrdering<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm14615-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">StrictWeakOrdering</span> <span class="special">&amp;</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a comparison function that induces a strict weak ordering and both *this and x must be sorted according to that ordering The lists x and *this must be distinct.</p>
<p><span class="bold"><strong>Effects</strong></span>: This function removes all of x's elements and inserts them in order into *this. The merge is stable; that is, if an element from *this is equivalent to one from x, then the element from *this will precede the one from x.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comp throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: This function is linear time: it performs at most size() + x.size() - 1 comparisons.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators and references to *this are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StrictWeakOrdering<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm14635-bb"></a><span class="identifier">merge</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;&amp;</span> x<span class="special">,</span> <span class="identifier">StrictWeakOrdering</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: p must be a comparison function that induces a strict weak ordering and both *this and x must be sorted according to that ordering The lists x and *this must be distinct.</p>
<p><span class="bold"><strong>Effects</strong></span>: This function removes all of x's elements and inserts them in order into *this. The merge is stable; that is, if an element from *this is equivalent to one from x, then the element from *this will precede the one from x.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comp throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: This function is linear time: it performs at most size() + x.size() - 1 comparisons.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators and references to *this are not invalidated. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14655-bb"></a><span class="identifier">sort</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: This function sorts the list *this according to std::less&lt;value_type&gt;. The sort is stable, that is, the relative order of equivalent elements is preserved.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comparison throws.</p>
<p><span class="bold"><strong>Notes</strong></span>: Iterators and references are not invalidated.</p>
<p><span class="bold"><strong>Complexity</strong></span>: The number of comparisons is approximately N log N, where N is the list's size. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> StrictWeakOrdering<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm14666-bb"></a><span class="identifier">sort</span><span class="special">(</span><span class="identifier">StrictWeakOrdering</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: This function sorts the list *this according to std::less&lt;value_type&gt;. The sort is stable, that is, the relative order of equivalent elements is preserved.</p>
<p><span class="bold"><strong>Throws</strong></span>: If comp throws.</p>
<p><span class="bold"><strong>Notes</strong></span>: Iterators and references are not invalidated.</p>
<p><span class="bold"><strong>Complexity</strong></span>: The number of comparisons is approximately N log N, where N is the list's size. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm14681-bb"></a><span class="identifier">reverse</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Reverses the order of elements in the list.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: This function is linear time.</p>
<p><span class="bold"><strong>Note</strong></span>: Iterators and references are not invalidated </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.14.3.4.7"></a><h3>
<a name="idm14881-bb"></a><code class="computeroutput">list</code> friend functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm14882-bb"></a><span class="keyword">operator</span><span class="special">==</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x and y are equal</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm14895-bb"></a><span class="keyword">operator</span><span class="special">!=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x and y are unequal</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm14908-bb"></a><span class="keyword">operator</span><span class="special">&lt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is less than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm14921-bb"></a><span class="keyword">operator</span><span class="special">&gt;</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is greater than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm14934-bb"></a><span class="keyword">operator</span><span class="special">&lt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is equal or less than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">bool</span> <a name="idm14947-bb"></a><span class="keyword">operator</span><span class="special">&gt;=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <span class="keyword">const</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if x is equal or greater than y</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements in the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">friend</span> <span class="keyword">void</span> <a name="idm14960-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> x<span class="special">,</span> <a class="link" href="list.html" title="Class template list">list</a> <span class="special">&amp;</span> y<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">(</span><span class="keyword">noexcept</span><span class="special">(</span><span class="identifier">x</span><span class="special">.</span><span class="identifier">swap</span><span class="special">(</span><span class="identifier">y</span><span class="special">)</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: x.swap(y)</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2009-2018 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="pmr/flat_set_of.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../boost_container_header_reference.html#header.boost.container.list_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="pmr/list_of.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
