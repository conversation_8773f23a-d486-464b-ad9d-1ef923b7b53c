<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template rbtree</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../intrusive/reference.html#header.boost.intrusive.rbtree_hpp" title="Header &lt;boost/intrusive/rbtree.hpp&gt;">
<link rel="prev" href="make_rbtree.html" title="Struct template make_rbtree">
<link rel="next" href="rbtree_algorithms.html" title="Class template rbtree_algorithms">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="make_rbtree.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../intrusive/reference.html#header.boost.intrusive.rbtree_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="rbtree_algorithms.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.intrusive.rbtree"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template rbtree</span></h2>
<p>boost::intrusive::rbtree</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../intrusive/reference.html#header.boost.intrusive.rbtree_hpp" title="Header &lt;boost/intrusive/rbtree.hpp&gt;">boost/intrusive/rbtree.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">ValueTraits</span>                                    <a name="boost.intrusive.rbtree.value_traits"></a><span class="identifier">value_traits</span><span class="special">;</span>          
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">pointer</span>                <a name="boost.intrusive.rbtree.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>               
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_pointer</span>          <a name="boost.intrusive.rbtree.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">value_type</span>             <a name="boost.intrusive.rbtree.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>            
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">key_type</span>               <a name="boost.intrusive.rbtree.key_type"></a><span class="identifier">key_type</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">key_of_value</span>           <a name="boost.intrusive.rbtree.key_of_value"></a><span class="identifier">key_of_value</span><span class="special">;</span>          
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">reference</span>              <a name="boost.intrusive.rbtree.reference"></a><span class="identifier">reference</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_reference</span>        <a name="boost.intrusive.rbtree.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">difference_type</span>        <a name="boost.intrusive.rbtree.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">size_type</span>              <a name="boost.intrusive.rbtree.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">value_compare</span>          <a name="boost.intrusive.rbtree.value_compare"></a><span class="identifier">value_compare</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">key_compare</span>            <a name="boost.intrusive.rbtree.key_compare"></a><span class="identifier">key_compare</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">iterator</span>               <a name="boost.intrusive.rbtree.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_iterator</span>         <a name="boost.intrusive.rbtree.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">reverse_iterator</span>       <a name="boost.intrusive.rbtree.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_reverse_iterator</span> <a name="boost.intrusive.rbtree.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node_traits</span>            <a name="boost.intrusive.rbtree.node_traits"></a><span class="identifier">node_traits</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node</span>                   <a name="boost.intrusive.rbtree.node"></a><span class="identifier">node</span><span class="special">;</span>                  
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node_ptr</span>               <a name="boost.intrusive.rbtree.node_ptr"></a><span class="identifier">node_ptr</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_node_ptr</span>         <a name="boost.intrusive.rbtree.const_node_ptr"></a><span class="identifier">const_node_ptr</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node_algorithms</span>        <a name="boost.intrusive.rbtree.node_algorithms"></a><span class="identifier">node_algorithms</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">insert_commit_data</span>     <a name="boost.intrusive.rbtree.insert_commit_data"></a><span class="identifier">insert_commit_data</span><span class="special">;</span>    

  <span class="comment">// <a class="link" href="rbtree.html#boost.intrusive.rbtreeconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="rbtree.html#idm38502-bb"><span class="identifier">rbtree</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="rbtree.html#idm38510-bb"><span class="identifier">rbtree</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm38523-bb"><span class="identifier">rbtree</span></a><span class="special">(</span><span class="keyword">bool</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">key_compare</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="rbtree.html#idm38547-bb"><span class="identifier">rbtree</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a class="link" href="rbtree.html#idm38558-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="rbtree.html#idm38567-bb"><span class="special">~</span><span class="identifier">rbtree</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="rbtree.html#idm37269-bb">public member functions</a></span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37270-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm37279-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm37288-bb"><span class="identifier">cbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37297-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm37306-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm37315-bb"><span class="identifier">cend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="rbtree.html#idm37324-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="rbtree.html#idm37333-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="rbtree.html#idm37342-bb"><span class="identifier">crbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="rbtree.html#idm37351-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="rbtree.html#idm37360-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="rbtree.html#idm37369-bb"><span class="identifier">crend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37378-bb"><span class="identifier">root</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm37387-bb"><span class="identifier">root</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm37396-bb"><span class="identifier">croot</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">key_compare</span> <a class="link" href="rbtree.html#idm37405-bb"><span class="identifier">key_comp</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">value_compare</span> <a class="link" href="rbtree.html#idm37414-bb"><span class="identifier">value_comp</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="rbtree.html#idm37423-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37432-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37441-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37453-bb"><span class="identifier">clone_from</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Cloner</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37475-bb"><span class="identifier">clone_from</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Cloner</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37499-bb"><span class="identifier">clone_from</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Cloner</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37523-bb"><span class="identifier">insert_equal</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37538-bb"><span class="identifier">insert_equal</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37555-bb"><span class="identifier">insert_equal</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="rbtree.html#idm37574-bb"><span class="identifier">insert_unique</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37589-bb"><span class="identifier">insert_unique</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm37606-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> 
                        <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm37632-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> 
                        <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a class="link" href="rbtree.html#idm37660-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a class="link" href="rbtree.html#idm37675-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37692-bb"><span class="identifier">insert_unique_commit</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37711-bb"><span class="identifier">insert_unique</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37730-bb"><span class="identifier">insert_before</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37747-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37762-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37777-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37790-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37805-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37820-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37844-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37863-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37884-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37905-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37934-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="rbtree.html#idm37945-bb"><span class="identifier">clear_and_dispose</span></a><span class="special">(</span><span class="identifier">Disposer</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37959-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="rbtree.html#idm37971-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm37992-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38004-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38021-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38033-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38050-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38062-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38083-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38095-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38116-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38128-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38149-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38161-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a class="link" href="rbtree.html#idm38182-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm38194-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="rbtree.html#idm38215-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm38227-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="rbtree.html#idm38248-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm38279-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> 
                  <span class="keyword">bool</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="rbtree.html#idm38321-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="rbtree.html#idm38352-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> 
                  <span class="keyword">bool</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38394-bb"><span class="identifier">iterator_to</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38407-bb"><span class="identifier">iterator_to</span></a><span class="special">(</span><span class="identifier">const_reference</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">pointer</span> <a class="link" href="rbtree.html#idm38420-bb"><span class="identifier">unlink_leftmost_without_rebalance</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="rbtree.html#idm38431-bb"><span class="identifier">replace_node</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="rbtree.html#idm38448-bb"><span class="identifier">remove_node</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="rbtree.html#idm38462-bb"><span class="identifier">merge_unique</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="rbtree.html#idm38482-bb"><span class="identifier">merge_equal</span></a><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="rbtree.html#idm38576-bb">public static functions</a></span>
  <span class="keyword">static</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a class="link" href="rbtree.html#idm38577-bb"><span class="identifier">container_from_end_iterator</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a class="link" href="rbtree.html#idm38591-bb"><span class="identifier">container_from_end_iterator</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a class="link" href="rbtree.html#idm38605-bb"><span class="identifier">container_from_iterator</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a class="link" href="rbtree.html#idm38619-bb"><span class="identifier">container_from_iterator</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="identifier">iterator</span> <a class="link" href="rbtree.html#idm38633-bb"><span class="identifier">s_iterator_to</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="identifier">const_iterator</span> <a class="link" href="rbtree.html#idm38649-bb"><span class="identifier">s_iterator_to</span></a><span class="special">(</span><span class="identifier">const_reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="rbtree.html#idm38665-bb"><span class="identifier">init_node</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">bool</span> <span class="identifier">constant_time_size</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.28.4.4"></a><h2>Description</h2>
<p>The class template rbtree is an intrusive red-black tree container, that is used to construct intrusive set and multiset containers. The no-throw guarantee holds only, if the key_compare object doesn't throw.</p>
<p>The template parameter <code class="computeroutput">T</code> is the type to be managed by the container. The user can specify additional options and if no options are provided default options are used.</p>
<p>The container supports the following options: <code class="computeroutput">base_hook&lt;&gt;/member_hook&lt;&gt;/value_traits&lt;&gt;</code>, <code class="computeroutput">constant_time_size&lt;&gt;</code>, <code class="computeroutput">size_type&lt;&gt;</code> and <code class="computeroutput">compare&lt;&gt;</code>. </p>
<div class="refsect2">
<a name="id-*********.********"></a><h3>
<a name="boost.intrusive.rbtreeconstruct-copy-destruct"></a><code class="computeroutput">rbtree</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm38502-bb"></a><span class="identifier">rbtree</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the copy constructor of the key_compare object throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm38510-bb"></a><span class="identifier">rbtree</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span> cmp<span class="special">,</span> 
                <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> v_traits <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty container with given comparison and traits.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the copy constructor of the key_compare object throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
  <a name="idm38523-bb"></a><span class="identifier">rbtree</span><span class="special">(</span><span class="keyword">bool</span> unique<span class="special">,</span> <span class="identifier">Iterator</span> b<span class="special">,</span> <span class="identifier">Iterator</span> e<span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span> cmp <span class="special">=</span> <span class="identifier">key_compare</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> v_traits <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Dereferencing iterator must yield an lvalue of type value_type. cmp must be a comparison function that induces a strict weak ordering.</p>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty container and inserts elements from [b, e).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if [b, e) is already sorted using comp and otherwise N * log N, where N is the distance between first and last.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the copy constructor/operator() of the key_compare object throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm38547-bb"></a><span class="identifier">rbtree</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a container moving resources from another container. Internal comparison object and value traits are move constructed and nodes belonging to x (except the node representing the "end") are linked to *this.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node's move constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the move constructor of the comparison objet throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a name="idm38558-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to swap </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm38567-bb"></a><span class="special">~</span><span class="identifier">rbtree</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Detaches all elements from this. The objects in the set are not deleted (i.e. no destructors are called), but the nodes according to the <code class="computeroutput"><a class="link" href="value_traits.html" title="Struct template value_traits">value_traits</a></code> template parameter are reinitialized and thus can be reused.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to elements contained in *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-*********.28.4.4.6"></a><h3>
<a name="idm37269-bb"></a><code class="computeroutput">rbtree</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37270-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator pointing to the beginning of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm37279-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the beginning of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm37288-bb"></a><span class="identifier">cbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the beginning of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37297-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator pointing to the end of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm37306-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the end of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm37315-bb"></a><span class="identifier">cend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the end of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm37324-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm37333-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm37342-bb"></a><span class="identifier">crbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm37351-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm37360-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm37369-bb"></a><span class="identifier">crend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37378-bb"></a><span class="identifier">root</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a iterator pointing to the root node of the container or end() if not present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm37387-bb"></a><span class="identifier">root</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the root node of the container or cend() if not present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm37396-bb"></a><span class="identifier">croot</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the root node of the container or cend() if not present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">key_compare</span> <a name="idm37405-bb"></a><span class="identifier">key_comp</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the key_compare object used by the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If key_compare copy-constructor throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">value_compare</span> <a name="idm37414-bb"></a><span class="identifier">value_comp</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the value_compare object used by the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_compare copy-constructor throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm37423-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if the container is empty.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm37432-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of elements stored in the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to elements contained in *this if constant-time size option is disabled. Constant time otherwise.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm37441-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Swaps the contents of two containers.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor's swap call throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm37453-bb"></a><span class="identifier">clone_from</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> src<span class="special">,</span> <span class="identifier">Cloner</span> cloner<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw. Cloner should yield to nodes equivalent to the original nodes.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements from *this calling Disposer::operator()(pointer), clones all the elements from src calling Cloner::operator()(const_reference ) and inserts them on *this. Copies the predicate from the source container.</p>
<p>If cloner throws, all cloned elements are unlinked and disposed calling Disposer::operator()(pointer).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to erased plus inserted elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If cloner throws or predicate copy assignment throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm37475-bb"></a><span class="identifier">clone_from</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span> src<span class="special">,</span> <span class="identifier">Cloner</span> cloner<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw. Cloner should yield to nodes equivalent to the original nodes.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements from *this calling Disposer::operator()(pointer), clones all the elements from src calling Cloner::operator()(reference) and inserts them on *this. Copies the predicate from the source container.</p>
<p>If cloner throws, all cloned elements are unlinked and disposed calling Disposer::operator()(pointer).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to erased plus inserted elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If cloner throws or predicate copy assignment throws. Basic guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: This version can modify the source container, useful to implement move semantics. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm37499-bb"></a><span class="identifier">clone_from</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;&amp;</span> src<span class="special">,</span> <span class="identifier">Cloner</span> cloner<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw. Cloner should yield to nodes equivalent to the original nodes.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements from *this calling Disposer::operator()(pointer), clones all the elements from src calling Cloner::operator()(reference) and inserts them on *this. Copies the predicate from the source container.</p>
<p>If cloner throws, all cloned elements are unlinked and disposed calling Disposer::operator()(pointer).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to erased plus inserted elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If cloner throws or predicate copy assignment throws. Basic guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: This version can modify the source container, useful to implement move semantics. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37523-bb"></a><span class="identifier">insert_equal</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts value into the container before the upper bound.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for insert element is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the internal key_compare ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37538-bb"></a><span class="identifier">insert_equal</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and "hint" must be a valid iterator.</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container, using "hint" as a hint to where it will be inserted. If "hint" is the upper_bound the insertion takes constant time (two comparisons in the worst case)</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it is amortized constant time if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the internal key_compare ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm37555-bb"></a><span class="identifier">insert_equal</span><span class="special">(</span><span class="identifier">Iterator</span> b<span class="special">,</span> <span class="identifier">Iterator</span> e<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Dereferencing iterator must yield an lvalue of type value_type.</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a each element of a range into the container before the upper bound of the key of each element.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Insert range is in general O(N * log(N)), where N is the size of the range. However, it is linear in N if the range is already sorted by value_comp().</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm37574-bb"></a><span class="identifier">insert_unique</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts value into the container if the value is not already present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for insert element is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37589-bb"></a><span class="identifier">insert_unique</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and "hint" must be a valid iterator</p>
<p><span class="bold"><strong>Effects</strong></span>: Tries to insert x into the container, using "hint" as a hint to where it will be inserted.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it is amortized constant time (two comparisons in the worst case) if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a name="idm37606-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> 
                      <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: comp must be a comparison function that induces the same strict weak ordering as key_compare. The difference is that comp compares an arbitrary key with the contained values.</p>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function is used to improve performance when constructing a value_type is expensive: if there is an equivalent value the constructed object must be discarded. Many times, the part of the node that is used to impose the order is much cheaper to construct than the value_type and this function offers the possibility to use that part to check if the insertion will be successful.</p>
<p>If the check is successful, the user can construct the value_type and use "insert_commit" to insert the object in constant-time. This gives a total logarithmic complexity to the insertion: check(O(log(N)) + commit(O(1)).</p>
<p>"commit_data" remains valid for a subsequent "insert_commit" only if no more objects are inserted or erased from the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a name="idm37632-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> 
                      <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> 
                      <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: comp must be a comparison function that induces the same strict weak ordering as key_compare. The difference is that comp compares an arbitrary key with the contained values.</p>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself, using "hint" as a hint to where it will be inserted.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it's amortized constant time if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function is used to improve performance when constructing a value_type is expensive: if there is an equivalent value the constructed object must be discarded. Many times, the part of the constructing that is used to impose the order is much cheaper to construct than the value_type and this function offers the possibility to use that key to check if the insertion will be successful.</p>
<p>If the check is successful, the user can construct the value_type and use "insert_commit" to insert the object in constant-time. This can give a total constant-time complexity to the insertion: check(O(1)) + commit(O(1)).</p>
<p>"commit_data" remains valid for a subsequent "insert_commit" only if no more objects are inserted or erased from the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
<a name="idm37660-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
<a name="idm37675-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">,</span> 
                    <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself, using "hint" as a hint to where it will be inserted.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it's amortized constant time if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37692-bb"></a><span class="identifier">insert_unique_commit</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">,</span> 
                              <span class="keyword">const</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue of type value_type. commit_data must have been obtained from a previous call to "insert_check". No objects should have been inserted or erased from the container between the "insert_check" that filled "commit_data" and the call to "insert_commit".</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts the value in the container using the information obtained from the "commit_data" that a previous "insert_check" filled.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator to the newly inserted object.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function has only sense if a "insert_check" has been previously executed to fill "commit_data". No value should be inserted or erased between the "insert_check" and "insert_commit" calls. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm37711-bb"></a><span class="identifier">insert_unique</span><span class="special">(</span><span class="identifier">Iterator</span> b<span class="special">,</span> <span class="identifier">Iterator</span> e<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Dereferencing iterator must yield an lvalue of type value_type.</p>
<p><span class="bold"><strong>Effects</strong></span>: Tries to insert each element of a range into the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Insert range is in general O(N * log(N)), where N is the size of the range. However, it is linear in N if the range is already sorted by value_comp().</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37730-bb"></a><span class="identifier">insert_before</span><span class="special">(</span><span class="identifier">const_iterator</span> pos<span class="special">,</span> <span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, "pos" must be a valid iterator (or end) and must be the succesor of value once inserted according to the predicate</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container before "pos".</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function does not check preconditions so if "pos" is not the successor of "value" container ordering invariant will be broken. This is a low-level function to be used only for performance reasons by advanced users. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm37747-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and it must be no less than the greatest inserted key</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container in the last position.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function does not check preconditions so if value is less than the greatest inserted key container ordering invariant will be broken. This function is slightly more efficient than using "insert_before". This is a low-level function to be used only for performance reasons by advanced users. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm37762-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and it must be no greater than the minimum inserted key</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container in the first position.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function does not check preconditions so if value is greater than the minimum inserted key container ordering invariant will be broken. This function is slightly more efficient than using "insert_before". This is a low-level function to be used only for performance reasons by advanced users. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37777-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> i<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases the element pointed to by i.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase element is constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37790-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> b<span class="special">,</span> <span class="identifier">const_iterator</span> e<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases the range pointed to by b end e.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase range is at most O(log(size() + N)), where N is the number of elements in the range.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm37805-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given value.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm37820-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given key. according to the comparison functor "comp".</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm37844-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="identifier">const_iterator</span> i<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases the element pointed to by i. Disposer::operator()(pointer) is called for the removed element.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase element is constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators to the erased elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm37863-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="identifier">const_iterator</span> b<span class="special">,</span> <span class="identifier">const_iterator</span> e<span class="special">,</span> 
                             <span class="identifier">Disposer</span> disposer<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases the range pointed to by b end e. Disposer::operator()(pointer) is called for the removed elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase range is at most O(log(size() + N)), where N is the number of elements in the range.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators to the erased elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm37884-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given value. Disposer::operator()(pointer) is called for the removed elements.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm37905-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> 
                              <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk) and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given key. according to the comparison functor "comp". Disposer::operator()(pointer) is called for the removed elements.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators to the erased elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm37934-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all of the elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements on the container. if it's a safe-mode or auto-unlink value_type. Constant time otherwise.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm37945-bb"></a><span class="identifier">clear_and_dispose</span><span class="special">(</span><span class="identifier">Disposer</span> disposer<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all of the elements calling disposer(p) for each node to be erased. <span class="bold"><strong>Complexity</strong></span>: Average complexity for is at most O(log(size() + N)), where N is the number of elements in the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. Calls N times to disposer functor. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm37959-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of contained elements with the given value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic to the number of elements contained plus lineal to number of objects with the given value.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm37971-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of contained elements with the given key</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic to the number of elements contained plus lineal to number of objects with the given key.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm37992-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm38004-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm38021-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">const_iterator</span> 
  <a name="idm38033-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm38050-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm38062-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k according to comp or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm38083-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">const_iterator</span> 
  <a name="idm38095-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k according to comp or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm38116-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm38128-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm38149-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">const_iterator</span> <a name="idm38161-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a name="idm38182-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
  <a name="idm38194-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
<a name="idm38215-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a name="idm38227-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
<a name="idm38248-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> lower<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
              <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to key_compare. [key_comp()(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!key_comp()(upper_key, lower_key) &amp;&amp; !key_comp()(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key) if left_closed, upper_bound(lower_key) otherwise</p>
<p>second = upper_bound(upper_key) if right_closed, lower_bound(upper_key) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_value and upper_value.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
  <a name="idm38279-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
                <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">lower_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, lower_key) if left_closed is true, with respect to !comp(lower_key, nk) otherwise.</p>
<p><code class="computeroutput">upper_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(upper_key, nk) if right_closed is true, with respect to comp(nk, upper_key) otherwise.</p>
<p><code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to comp [comp(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!comp(upper_key, lower_key) &amp;&amp; !comp(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key, comp) if left_closed, upper_bound(lower_key, comp) otherwise</p>
<p>second = upper_bound(upper_key, comp) if right_closed, lower_bound(upper_key, comp) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_key and upper_key.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
<a name="idm38321-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
              <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to key_compare. [key_comp()(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!key_comp()(upper_key, lower_key) &amp;&amp; !key_comp()(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key) if left_closed, upper_bound(lower_key) otherwise</p>
<p>second = upper_bound(upper_key) if right_closed, lower_bound(upper_key) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_value and upper_value.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a name="idm38352-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
                <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">lower_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, lower_key) if left_closed is true, with respect to !comp(lower_key, nk) otherwise.</p>
<p><code class="computeroutput">upper_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(upper_key, nk) if right_closed is true, with respect to comp(nk, upper_key) otherwise.</p>
<p><code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to comp [comp(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!comp(upper_key, lower_key) &amp;&amp; !comp(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key, comp) if left_closed, upper_bound(lower_key, comp) otherwise</p>
<p>second = upper_bound(upper_key, comp) if right_closed, lower_bound(upper_key, comp) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_key and upper_key.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm38394-bb"></a><span class="identifier">iterator_to</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm38407-bb"></a><span class="identifier">iterator_to</span><span class="special">(</span><span class="identifier">const_reference</span> value<span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid const_iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">pointer</span> <a name="idm38420-bb"></a><span class="identifier">unlink_leftmost_without_rebalance</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Unlinks the leftmost node from the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity is constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function breaks the container and the container can only be used for more unlink_leftmost_without_rebalance calls. This function is normally used to achieve a step by step controlled destruction of the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm38431-bb"></a><span class="identifier">replace_node</span><span class="special">(</span><span class="identifier">iterator</span> replace_this<span class="special">,</span> <span class="identifier">reference</span> with_this<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: replace_this must be a valid iterator of *this and with_this must not be inserted in any container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Replaces replace_this in its position in the container with with_this. The container does not need to be rebalanced.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function will break container ordering invariants if with_this is not equivalent to *replace_this according to the ordering rules. This function is faster than erasing and inserting the node, since no rebalancing or comparison is needed. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm38448-bb"></a><span class="identifier">remove_node</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: removes "value" from the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic time.</p>
<p><span class="bold"><strong>Note</strong></span>: This static function is only usable with non-constant time size containers that have stateless comparison functors.</p>
<p>If the user calls this function with a constant time size container or stateful comparison functor a compilation error will be issued. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm38462-bb"></a><span class="identifier">merge_unique</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: "source" container's Options can only can differ in the comparison function from *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Attempts to extract each element in source and insert it into a using the comparison object of *this. If there is an element in a with key equivalent to the key of an element from source, then that element is not extracted from source.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: Pointers and references to the transferred elements of source refer to those same elements but as members of *this. Iterators referring to the transferred elements will continue to refer to their elements, but they now behave as iterators into *this, not into source.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing unless the comparison object throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: N log(a.size() + N) (N has the value source.size()) </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm38482-bb"></a><span class="identifier">merge_equal</span><span class="special">(</span><a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: "source" container's Options can only can differ in the comparison function from *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Extracts each element in source and insert it into a using the comparison object of *this.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: Pointers and references to the transferred elements of source refer to those same elements but as members of *this. Iterators referring to the transferred elements will continue to refer to their elements, but they now behave as iterators into *this, not into source.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing unless the comparison object throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: N log(a.size() + N) (N has the value source.size()) </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-*********.28.4.4.7"></a><h3>
<a name="idm38576-bb"></a><code class="computeroutput">rbtree</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a name="idm38577-bb"></a><span class="identifier">container_from_end_iterator</span><span class="special">(</span><span class="identifier">iterator</span> end_iterator<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: end_iterator must be a valid end iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the end iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> 
<a name="idm38591-bb"></a><span class="identifier">container_from_end_iterator</span><span class="special">(</span><span class="identifier">const_iterator</span> end_iterator<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: end_iterator must be a valid end iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the end iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a name="idm38605-bb"></a><span class="identifier">container_from_iterator</span><span class="special">(</span><span class="identifier">iterator</span> it<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: it must be a valid iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="rbtree.html" title="Class template rbtree">rbtree</a> <span class="special">&amp;</span> <a name="idm38619-bb"></a><span class="identifier">container_from_iterator</span><span class="special">(</span><span class="identifier">const_iterator</span> it<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: it must be a valid iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="identifier">iterator</span> <a name="idm38633-bb"></a><span class="identifier">s_iterator_to</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This static function is available only if the <span class="emphasis"><em>value traits</em></span> is stateless. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="identifier">const_iterator</span> <a name="idm38649-bb"></a><span class="identifier">s_iterator_to</span><span class="special">(</span><span class="identifier">const_reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This static function is available only if the <span class="emphasis"><em>value traits</em></span> is stateless. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">void</span> <a name="idm38665-bb"></a><span class="identifier">init_node</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value shall not be in a container.</p>
<p><span class="bold"><strong>Effects</strong></span>: init_node puts the hook of a value in a well-known default state.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Note</strong></span>: This function puts the hook in the well-known default state used by auto_unlink and safe hooks. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2005 Olaf Krzikalla<br>Copyright © 2006-2015 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="make_rbtree.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../intrusive/reference.html#header.boost.intrusive.rbtree_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="rbtree_algorithms.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
