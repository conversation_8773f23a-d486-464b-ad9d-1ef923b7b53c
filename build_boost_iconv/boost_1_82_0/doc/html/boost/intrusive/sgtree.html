<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template sgtree</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../intrusive/reference.html#header.boost.intrusive.sgtree_hpp" title="Header &lt;boost/intrusive/sgtree.hpp&gt;">
<link rel="prev" href="make_sgtree.html" title="Struct template make_sgtree">
<link rel="next" href="sgtree_algorithms.html" title="Class template sgtree_algorithms">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="make_sgtree.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../intrusive/reference.html#header.boost.intrusive.sgtree_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="sgtree_algorithms.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.intrusive.sgtree"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template sgtree</span></h2>
<p>boost::intrusive::sgtree</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../intrusive/reference.html#header.boost.intrusive.sgtree_hpp" title="Header &lt;boost/intrusive/sgtree.hpp&gt;">boost/intrusive/sgtree.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// types</span>
  <span class="keyword">typedef</span> <span class="identifier">ValueTraits</span>                                    <a name="boost.intrusive.sgtree.value_traits"></a><span class="identifier">value_traits</span><span class="special">;</span>          
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">pointer</span>                <a name="boost.intrusive.sgtree.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>               
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_pointer</span>          <a name="boost.intrusive.sgtree.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">value_type</span>             <a name="boost.intrusive.sgtree.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>            
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">key_type</span>               <a name="boost.intrusive.sgtree.key_type"></a><span class="identifier">key_type</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">key_of_value</span>           <a name="boost.intrusive.sgtree.key_of_value"></a><span class="identifier">key_of_value</span><span class="special">;</span>          
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">reference</span>              <a name="boost.intrusive.sgtree.reference"></a><span class="identifier">reference</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_reference</span>        <a name="boost.intrusive.sgtree.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">difference_type</span>        <a name="boost.intrusive.sgtree.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">size_type</span>              <a name="boost.intrusive.sgtree.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">value_compare</span>          <a name="boost.intrusive.sgtree.value_compare"></a><span class="identifier">value_compare</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">key_compare</span>            <a name="boost.intrusive.sgtree.key_compare"></a><span class="identifier">key_compare</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">iterator</span>               <a name="boost.intrusive.sgtree.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_iterator</span>         <a name="boost.intrusive.sgtree.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">reverse_iterator</span>       <a name="boost.intrusive.sgtree.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_reverse_iterator</span> <a name="boost.intrusive.sgtree.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node_traits</span>            <a name="boost.intrusive.sgtree.node_traits"></a><span class="identifier">node_traits</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node</span>                   <a name="boost.intrusive.sgtree.node"></a><span class="identifier">node</span><span class="special">;</span>                  
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">node_ptr</span>               <a name="boost.intrusive.sgtree.node_ptr"></a><span class="identifier">node_ptr</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span><span class="special">::</span><span class="identifier">const_node_ptr</span>         <a name="boost.intrusive.sgtree.const_node_ptr"></a><span class="identifier">const_node_ptr</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                         <a name="boost.intrusive.sgtree.node_algorithms"></a><span class="identifier">node_algorithms</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <span class="identifier">implementation_defined</span>                         <a name="boost.intrusive.sgtree.insert_commit_data"></a><span class="identifier">insert_commit_data</span><span class="special">;</span>    

  <span class="comment">// <a class="link" href="sgtree.html#boost.intrusive.sgtreeconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="sgtree.html#idm46706-bb"><span class="identifier">sgtree</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="sgtree.html#idm46714-bb"><span class="identifier">sgtree</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm46727-bb"><span class="identifier">sgtree</span></a><span class="special">(</span><span class="keyword">bool</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">key_compare</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
           <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="sgtree.html#idm46751-bb"><span class="identifier">sgtree</span></a><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a class="link" href="sgtree.html#idm46762-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="sgtree.html#idm46771-bb"><span class="special">~</span><span class="identifier">sgtree</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="sgtree.html#idm45441-bb">public member functions</a></span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45442-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm45451-bb"><span class="identifier">begin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm45460-bb"><span class="identifier">cbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45469-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm45478-bb"><span class="identifier">end</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm45487-bb"><span class="identifier">cend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="sgtree.html#idm45496-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="sgtree.html#idm45505-bb"><span class="identifier">rbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="sgtree.html#idm45514-bb"><span class="identifier">crbegin</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">reverse_iterator</span> <a class="link" href="sgtree.html#idm45523-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="sgtree.html#idm45532-bb"><span class="identifier">rend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_reverse_iterator</span> <a class="link" href="sgtree.html#idm45541-bb"><span class="identifier">crend</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45550-bb"><span class="identifier">root</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm45559-bb"><span class="identifier">root</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm45568-bb"><span class="identifier">croot</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">key_compare</span> <a class="link" href="sgtree.html#idm45577-bb"><span class="identifier">key_comp</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">value_compare</span> <a class="link" href="sgtree.html#idm45586-bb"><span class="identifier">value_comp</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="sgtree.html#idm45595-bb"><span class="identifier">empty</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm45604-bb"><span class="identifier">size</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45613-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45625-bb"><span class="identifier">clone_from</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Cloner</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45647-bb"><span class="identifier">clone_from</span></a><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;&amp;</span><span class="special">,</span> <span class="identifier">Cloner</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45671-bb"><span class="identifier">insert_equal</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45686-bb"><span class="identifier">insert_equal</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45703-bb"><span class="identifier">insert_equal</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a class="link" href="sgtree.html#idm45722-bb"><span class="identifier">insert_unique</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45737-bb"><span class="identifier">insert_unique</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span> <span class="identifier">BOOST_INTRUSIVE_I</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm45754-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> 
                        <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm45780-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> 
                        <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a class="link" href="sgtree.html#idm45808-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a class="link" href="sgtree.html#idm45823-bb"><span class="identifier">insert_unique_check</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45840-bb"><span class="identifier">insert_unique_commit</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45859-bb"><span class="identifier">insert_unique</span></a><span class="special">(</span><span class="identifier">Iterator</span><span class="special">,</span> <span class="identifier">Iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45878-bb"><span class="identifier">insert_before</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45895-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm45910-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45925-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45938-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm45953-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm45968-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm45992-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46011-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm46032-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm46053-bb"><span class="identifier">erase_and_dispose</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> <span class="identifier">Disposer</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46082-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46093-bb"><span class="identifier">clear_and_dispose</span></a><span class="special">(</span><span class="identifier">Disposer</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46107-bb"><span class="identifier">merge_unique</span></a><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
   <a class="link" href="sgtree.html#idm46127-bb"><span class="keyword">while</span></a><span class="special">(</span><span class="identifier">it</span> <span class="special">!</span> <span class="special">=</span> <span class="identifier">itend</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46132-bb"><span class="identifier">merge_equal</span></a><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
   <a class="link" href="sgtree.html#idm46152-bb"><span class="keyword">while</span></a><span class="special">(</span><span class="identifier">it</span> <span class="special">!</span> <span class="special">=</span> <span class="identifier">itend</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm46157-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">size_type</span> <a class="link" href="sgtree.html#idm46169-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46190-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46202-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46219-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46231-bb"><span class="identifier">lower_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46248-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46260-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46281-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46293-bb"><span class="identifier">upper_bound</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46314-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46326-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46347-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46359-bb"><span class="identifier">find</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a class="link" href="sgtree.html#idm46380-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm46392-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="sgtree.html#idm46413-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm46425-bb"><span class="identifier">equal_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="sgtree.html#idm46446-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm46477-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> 
                  <span class="keyword">bool</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a class="link" href="sgtree.html#idm46519-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
    <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
    <a class="link" href="sgtree.html#idm46550-bb"><span class="identifier">bounded_range</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span><span class="special">,</span> <span class="keyword">bool</span><span class="special">,</span> 
                  <span class="keyword">bool</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46592-bb"><span class="identifier">iterator_to</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46605-bb"><span class="identifier">iterator_to</span></a><span class="special">(</span><span class="identifier">const_reference</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">pointer</span> <a class="link" href="sgtree.html#idm46618-bb"><span class="identifier">unlink_leftmost_without_rebalance</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46629-bb"><span class="identifier">replace_node</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46646-bb"><span class="identifier">remove_node</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46660-bb"><span class="identifier">rebalance</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46669-bb"><span class="identifier">rebalance_subtree</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">float</span> <a class="link" href="sgtree.html#idm46684-bb"><span class="identifier">balance_factor</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46693-bb"><span class="identifier">balance_factor</span></a><span class="special">(</span><span class="keyword">float</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="sgtree.html#idm46780-bb">public static functions</a></span>
  <span class="keyword">static</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a class="link" href="sgtree.html#idm46781-bb"><span class="identifier">container_from_end_iterator</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a class="link" href="sgtree.html#idm46795-bb"><span class="identifier">container_from_end_iterator</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a class="link" href="sgtree.html#idm46809-bb"><span class="identifier">container_from_iterator</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a class="link" href="sgtree.html#idm46823-bb"><span class="identifier">container_from_iterator</span></a><span class="special">(</span><span class="identifier">const_iterator</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="identifier">iterator</span> <a class="link" href="sgtree.html#idm46837-bb"><span class="identifier">s_iterator_to</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="identifier">const_iterator</span> <a class="link" href="sgtree.html#idm46853-bb"><span class="identifier">s_iterator_to</span></a><span class="special">(</span><span class="identifier">const_reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">void</span> <a class="link" href="sgtree.html#idm46869-bb"><span class="identifier">init_node</span></a><span class="special">(</span><span class="identifier">reference</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">bool</span> <span class="identifier">constant_time_size</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">bool</span> <span class="identifier">floating_point</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">bool</span> <span class="identifier">stateful_value_traits</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.33.4.4"></a><h2>Description</h2>
<p>The class template sgtree is an intrusive scapegoat tree container, that is used to construct intrusive <a class="link" href="sg_set.html" title="Class template sg_set">sg_set</a> and <a class="link" href="sg_multiset.html" title="Class template sg_multiset">sg_multiset</a> containers. The no-throw guarantee holds only, if the value_compare object doesn't throw.</p>
<p>The template parameter <code class="computeroutput">T</code> is the type to be managed by the container. The user can specify additional options and if no options are provided default options are used.</p>
<p>The container supports the following options: <code class="computeroutput">base_hook&lt;&gt;/member_hook&lt;&gt;/value_traits&lt;&gt;</code>, <code class="computeroutput">floating_point&lt;&gt;</code>, <code class="computeroutput">size_type&lt;&gt;</code> and <code class="computeroutput">compare&lt;&gt;</code>. </p>
<div class="refsect2">
<a name="id-*********.********"></a><h3>
<a name="boost.intrusive.sgtreeconstruct-copy-destruct"></a><code class="computeroutput">sgtree</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm46706-bb"></a><span class="identifier">sgtree</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the copy constructor of the key_compare object throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm46714-bb"></a><span class="identifier">sgtree</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span> cmp<span class="special">,</span> 
                <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> v_traits <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty container with given comparison and traits.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the copy constructor of the key_compare object throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> 
  <a name="idm46727-bb"></a><span class="identifier">sgtree</span><span class="special">(</span><span class="keyword">bool</span> unique<span class="special">,</span> <span class="identifier">Iterator</span> b<span class="special">,</span> <span class="identifier">Iterator</span> e<span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">key_compare</span> <span class="special">&amp;</span> cmp <span class="special">=</span> <span class="identifier">key_compare</span><span class="special">(</span><span class="special">)</span><span class="special">,</span> 
         <span class="keyword">const</span> <span class="identifier">value_traits</span> <span class="special">&amp;</span> v_traits <span class="special">=</span> <span class="identifier">value_traits</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Dereferencing iterator must yield an lvalue of type value_type. cmp must be a comparison function that induces a strict weak ordering.</p>
<p><span class="bold"><strong>Effects</strong></span>: Constructs an empty container and inserts elements from [b, e).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear in N if [b, e) is already sorted using comp and otherwise N * log N, where N is the distance between first and last.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the copy constructor/operator() of the key_compare object throws. Basic guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm46751-bb"></a><span class="identifier">sgtree</span><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Constructs a container moving resources from another container. Internal comparison object and value traits are move constructed and nodes belonging to x (except the node representing the "end") are linked to *this.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_traits::node_traits::node's move constructor throws (this does not happen with predefined Boost.Intrusive hooks) or the move constructor of the comparison objet throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a name="idm46762-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;&amp;</span> x<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Equivalent to swap </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm46771-bb"></a><span class="special">~</span><span class="identifier">sgtree</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Detaches all elements from this. The objects in the set are not deleted (i.e. no destructors are called), but the nodes according to the <code class="computeroutput"><a class="link" href="value_traits.html" title="Struct template value_traits">value_traits</a></code> template parameter are reinitialized and thus can be reused.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to elements contained in *this.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-*********.33.4.4.6"></a><h3>
<a name="idm45441-bb"></a><code class="computeroutput">sgtree</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45442-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator pointing to the beginning of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm45451-bb"></a><span class="identifier">begin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the beginning of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm45460-bb"></a><span class="identifier">cbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the beginning of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45469-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator pointing to the end of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm45478-bb"></a><span class="identifier">end</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the end of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm45487-bb"></a><span class="identifier">cend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the end of the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm45496-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm45505-bb"></a><span class="identifier">rbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm45514-bb"></a><span class="identifier">crbegin</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the beginning of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">reverse_iterator</span> <a name="idm45523-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm45532-bb"></a><span class="identifier">rend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_reverse_iterator</span> <a name="idm45541-bb"></a><span class="identifier">crend</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_reverse_iterator pointing to the end of the reversed container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45550-bb"></a><span class="identifier">root</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a iterator pointing to the root node of the container or end() if not present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm45559-bb"></a><span class="identifier">root</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the root node of the container or cend() if not present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm45568-bb"></a><span class="identifier">croot</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const_iterator pointing to the root node of the container or cend() if not present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">key_compare</span> <a name="idm45577-bb"></a><span class="identifier">key_comp</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the key_compare object used by the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If key_compare copy-constructor throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">value_compare</span> <a name="idm45586-bb"></a><span class="identifier">value_comp</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the value_compare object used by the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If value_compare copy-constructor throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm45595-bb"></a><span class="identifier">empty</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns true if the container is empty.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm45604-bb"></a><span class="identifier">size</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of elements stored in the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to elements contained in *this if constant-time size option is disabled. Constant time otherwise.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm45613-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> other<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Swaps the contents of two containers.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor's swap call throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm45625-bb"></a><span class="identifier">clone_from</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> src<span class="special">,</span> <span class="identifier">Cloner</span> cloner<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw. Cloner should yield to nodes equivalent to the original nodes.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements from *this calling Disposer::operator()(pointer), clones all the elements from src calling Cloner::operator()(const_reference ) and inserts them on *this. Copies the predicate from the source container.</p>
<p>If cloner throws, all cloned elements are unlinked and disposed calling Disposer::operator()(pointer).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to erased plus inserted elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If cloner throws or predicate copy assignment throws. Basic guarantee. Additional notes: it also copies the alpha factor from the source container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Cloner<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm45647-bb"></a><span class="identifier">clone_from</span><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;&amp;</span> src<span class="special">,</span> <span class="identifier">Cloner</span> cloner<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw. Cloner should yield to nodes equivalent to the original nodes.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements from *this calling Disposer::operator()(pointer), clones all the elements from src calling Cloner::operator()(reference) and inserts them on *this. Copies the predicate from the source container.</p>
<p>If cloner throws, all cloned elements are unlinked and disposed calling Disposer::operator()(pointer).</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to erased plus inserted elements.</p>
<p><span class="bold"><strong>Throws</strong></span>: If cloner throws or predicate copy assignment throws. Basic guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: This version can modify the source container, useful to implement move semantics. Additional notes: it also copies the alpha factor from the source container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45671-bb"></a><span class="identifier">insert_equal</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts value into the container before the upper bound.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for insert element is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the internal key_compare ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45686-bb"></a><span class="identifier">insert_equal</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and "hint" must be a valid iterator.</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container, using "hint" as a hint to where it will be inserted. If "hint" is the upper_bound the insertion takes constant time (two comparisons in the worst case)</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it is amortized constant time if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the internal key_compare ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm45703-bb"></a><span class="identifier">insert_equal</span><span class="special">(</span><span class="identifier">Iterator</span> b<span class="special">,</span> <span class="identifier">Iterator</span> e<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Dereferencing iterator must yield an lvalue of type value_type.</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts a each element of a range into the container before the upper bound of the key of each element.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Insert range is in general O(N * log(N)), where N is the size of the range. However, it is linear in N if the range is already sorted by value_comp().</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> <a name="idm45722-bb"></a><span class="identifier">insert_unique</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts value into the container if the value is not already present.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for insert element is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45737-bb"></a><span class="identifier">insert_unique</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="identifier">reference</span> value<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and "hint" must be a valid iterator</p>
<p><span class="bold"><strong>Effects</strong></span>: Tries to insert x into the container, using "hint" as a hint to where it will be inserted.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it is amortized constant time (two comparisons in the worst case) if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span> <span class="identifier">BOOST_INTRUSIVE_I</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a name="idm45754-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> 
                      <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: comp must be a comparison function that induces the same strict weak ordering as key_compare. The difference is that comp compares an arbitrary key with the contained values.</p>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function is used to improve performance when constructing a value_type is expensive: if there is an equivalent value the constructed object must be discarded. Many times, the part of the node that is used to impose the order is much cheaper to construct than the value_type and this function offers the possibility to use that part to check if the insertion will be successful.</p>
<p>If the check is successful, the user can construct the value_type and use "insert_commit" to insert the object in constant-time. This gives a total logarithmic complexity to the insertion: check(O(log(N)) + commit(O(1)).</p>
<p>"commit_data" remains valid for a subsequent "insert_commit" only if no more objects are inserted or erased from the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
  <a name="idm45780-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> 
                      <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> 
                      <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: comp must be a comparison function that induces the same strict weak ordering as key_compare. The difference is that comp compares an arbitrary key with the contained values.</p>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself, using "hint" as a hint to where it will be inserted.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it's amortized constant time if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function is used to improve performance when constructing a value_type is expensive: if there is an equivalent value the constructed object must be discarded. Many times, the part of the constructing that is used to impose the order is much cheaper to construct than the value_type and this function offers the possibility to use that key to check if the insertion will be successful.</p>
<p>If the check is successful, the user can construct the value_type and use "insert_commit" to insert the object in constant-time. This can give a total constant-time complexity to the insertion: check(O(1)) + commit(O(1)).</p>
<p>"commit_data" remains valid for a subsequent "insert_commit" only if no more objects are inserted or erased from the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
<a name="idm45808-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity is at most logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="keyword">bool</span> <span class="special">&gt;</span> 
<a name="idm45823-bb"></a><span class="identifier">insert_unique_check</span><span class="special">(</span><span class="identifier">const_iterator</span> hint<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">,</span> 
                    <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Checks if a value can be inserted in the container, using a user provided key instead of the value itself, using "hint" as a hint to where it will be inserted.</p>
<p><span class="bold"><strong>Returns</strong></span>: If there is an equivalent value returns a pair containing an iterator to the already present value and false. If the value can be inserted returns true in the returned pair boolean and fills "commit_data" that is meant to be used with the "insert_commit" function.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic in general, but it's amortized constant time if t is inserted immediately before hint.</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comp ordering function throws. Strong guarantee. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45840-bb"></a><span class="identifier">insert_unique_commit</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">,</span> 
                              <span class="keyword">const</span> <span class="identifier">insert_commit_data</span> <span class="special">&amp;</span> commit_data<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue of type value_type. commit_data must have been obtained from a previous call to "insert_check". No objects should have been inserted or erased from the container between the "insert_check" that filled "commit_data" and the call to "insert_commit".</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts the value in the container using the information obtained from the "commit_data" that a previous "insert_check" filled.</p>
<p><span class="bold"><strong>Returns</strong></span>: An iterator to the newly inserted object.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function has only sense if a "insert_check" has been previously executed to fill "commit_data". No value should be inserted or erased between the "insert_check" and "insert_commit" calls. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Iterator<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm45859-bb"></a><span class="identifier">insert_unique</span><span class="special">(</span><span class="identifier">Iterator</span> b<span class="special">,</span> <span class="identifier">Iterator</span> e<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Dereferencing iterator must yield an lvalue of type value_type.</p>
<p><span class="bold"><strong>Effects</strong></span>: Tries to insert each element of a range into the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Insert range is in general O(N * log(N)), where N is the size of the range. However, it is linear in N if the range is already sorted by value_comp().</p>
<p><span class="bold"><strong>Throws</strong></span>: If the comparison functor call throws.</p>
<p><span class="bold"><strong>Note</strong></span>: Does not affect the validity of iterators and references. No copy-constructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45878-bb"></a><span class="identifier">insert_before</span><span class="special">(</span><span class="identifier">const_iterator</span> pos<span class="special">,</span> <span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, "pos" must be a valid iterator (or end) and must be the succesor of value once inserted according to the predicate</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container before "pos".</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function does not check preconditions so if "pos" is not the successor of "value" container ordering invariant will be broken. This is a low-level function to be used only for performance reasons by advanced users. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm45895-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and it must be no less than the greatest inserted key</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container in the last position.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function does not check preconditions so if value is less than the greatest inserted key container ordering invariant will be broken. This function is slightly more efficient than using "insert_before". This is a low-level function to be used only for performance reasons by advanced users. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm45910-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue, and it must be no greater than the minimum inserted key</p>
<p><span class="bold"><strong>Effects</strong></span>: Inserts x into the container in the first position.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function does not check preconditions so if value is greater than the minimum inserted key container ordering invariant will be broken. This function is slightly more efficient than using "insert_before". This is a low-level function to be used only for performance reasons by advanced users. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45925-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> i<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases the element pointed to by i.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase element is constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm45938-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">const_iterator</span> b<span class="special">,</span> <span class="identifier">const_iterator</span> e<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases the range pointed to by b end e.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase range is at most O(log(size() + N)), where N is the number of elements in the range.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm45953-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given value.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm45968-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given key. according to the comparison functor "comp".</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm45992-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="identifier">const_iterator</span> i<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases the element pointed to by i. Disposer::operator()(pointer) is called for the removed element.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase element is constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators to the erased elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm46011-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="identifier">const_iterator</span> b<span class="special">,</span> <span class="identifier">const_iterator</span> e<span class="special">,</span> 
                             <span class="identifier">Disposer</span> disposer<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases the range pointed to by b end e. Disposer::operator()(pointer) is called for the removed elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity for erase range is at most O(log(size() + N)), where N is the number of elements in the range.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators to the erased elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm46032-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given value. Disposer::operator()(pointer) is called for the removed elements.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">,</span> <span class="keyword">typename</span> Disposer<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm46053-bb"></a><span class="identifier">erase_and_dispose</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> 
                              <span class="identifier">Disposer</span> disposer<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk) and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Requires</strong></span>: Disposer::operator()(pointer) shouldn't throw.</p>
<p><span class="bold"><strong>Effects</strong></span>: Erases all the elements with the given key. according to the comparison functor "comp". Disposer::operator()(pointer) is called for the removed elements.</p>
<p><span class="bold"><strong>Returns</strong></span>: The number of erased elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: O(log(size() + N).</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators to the erased elements. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm46082-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all of the elements.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the number of elements on the container. if it's a safe-mode or auto-unlink value_type. Constant time otherwise.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. No destructors are called. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Disposer<span class="special">&gt;</span> <span class="keyword">void</span> <a name="idm46093-bb"></a><span class="identifier">clear_and_dispose</span><span class="special">(</span><span class="identifier">Disposer</span> disposer<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Erases all of the elements calling disposer(p) for each node to be erased. <span class="bold"><strong>Complexity</strong></span>: Average complexity for is at most O(log(size() + N)), where N is the number of elements in the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: Invalidates the iterators (but not the references) to the erased elements. Calls N times to disposer functor. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm46107-bb"></a><span class="identifier">merge_unique</span><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: "source" container's Options can only can differ in the comparison function from *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Attempts to extract each element in source and insert it into a using the comparison object of *this. If there is an element in a with key equivalent to the key of an element from source, then that element is not extracted from source.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: Pointers and references to the transferred elements of source refer to those same elements but as members of *this. Iterators referring to the transferred elements will continue to refer to their elements, but they now behave as iterators into *this, not into source.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing unless the comparison object throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: N log(a.size() + N) (N has the value source.size()) </p>
</li>
<li class="listitem"><pre class="literallayout"> <a name="idm46127-bb"></a><span class="keyword">while</span><span class="special">(</span><span class="identifier">it</span> <span class="special">!</span> <span class="special">=</span> <span class="identifier">itend</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">class</span> <span class="special">...</span> Options2<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm46132-bb"></a><span class="identifier">merge_equal</span><span class="special">(</span><a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Options2</span><span class="special">...</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: "source" container's Options can only can differ in the comparison function from *this.</p>
<p><span class="bold"><strong>Effects</strong></span>: Extracts each element in source and insert it into a using the comparison object of *this.</p>
<p><span class="bold"><strong>Postcondition</strong></span>: Pointers and references to the transferred elements of source refer to those same elements but as members of *this. Iterators referring to the transferred elements will continue to refer to their elements, but they now behave as iterators into *this, not into source.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing unless the comparison object throws.</p>
<p><span class="bold"><strong>Complexity</strong></span>: N log(a.size() + N) (N has the value source.size()) </p>
</li>
<li class="listitem"><pre class="literallayout"> <a name="idm46152-bb"></a><span class="keyword">while</span><span class="special">(</span><span class="identifier">it</span> <span class="special">!</span> <span class="special">=</span> <span class="identifier">itend</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm46157-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of contained elements with the given value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic to the number of elements contained plus lineal to number of objects with the given value.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">size_type</span> <a name="idm46169-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns the number of contained elements with the given key</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic to the number of elements contained plus lineal to number of objects with the given key.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm46190-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm46202-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm46219-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">const_iterator</span> 
  <a name="idm46231-bb"></a><span class="identifier">lower_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is not less than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm46248-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm46260-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k according to comp or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm46281-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">const_iterator</span> 
  <a name="idm46293-bb"></a><span class="identifier">upper_bound</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an iterator to the first element whose key is greater than k according to comp or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm46314-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">iterator</span> <a name="idm46326-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm46347-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">const_iterator</span> <a name="idm46359-bb"></a><span class="identifier">find</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), and nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds an iterator to the first element whose key is k or end() if that element does not exist.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> <a name="idm46380-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
  <a name="idm46392-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
<a name="idm46413-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> key<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a name="idm46425-bb"></a><span class="identifier">equal_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> key<span class="special">,</span> <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: key is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, key) and !comp(key, nk), with comp(nk, key) implying !comp(key, nk), with nk the key_type of a value_type inserted into <code class="computeroutput">*this</code>.</p>
<p><span class="bold"><strong>Effects</strong></span>: Finds a range containing all elements whose key is k or an empty range that indicates the position where those elements would be if they there is no elements with key k.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
<a name="idm46446-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
              <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to key_compare. [key_comp()(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!key_comp()(upper_key, lower_key) &amp;&amp; !key_comp()(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key) if left_closed, upper_bound(lower_key) otherwise</p>
<p>second = upper_bound(upper_key) if right_closed, lower_bound(upper_key) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_value and upper_value.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span> <span class="special">&gt;</span> 
  <a name="idm46477-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
                <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">lower_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, lower_key) if left_closed is true, with respect to !comp(lower_key, nk) otherwise.</p>
<p><code class="computeroutput">upper_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(upper_key, nk) if right_closed is true, with respect to comp(nk, upper_key) otherwise.</p>
<p><code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to comp [comp(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!comp(upper_key, lower_key) &amp;&amp; !comp(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key, comp) if left_closed, upper_bound(lower_key, comp) otherwise</p>
<p>second = upper_bound(upper_key, comp) if right_closed, lower_bound(upper_key, comp) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_key and upper_key.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
<a name="idm46519-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">key_type</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
              <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to key_compare. [key_comp()(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!key_comp()(upper_key, lower_key) &amp;&amp; !key_comp()(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key) if left_closed, upper_bound(lower_key) otherwise</p>
<p>second = upper_bound(upper_key) if right_closed, lower_bound(upper_key) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">key_compare</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_value and upper_value.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> KeyType<span class="special">,</span> <span class="keyword">typename</span> KeyTypeKeyCompare<span class="special">&gt;</span> 
  <span class="identifier">std</span><span class="special">::</span><span class="identifier">pair</span><span class="special">&lt;</span> <span class="identifier">const_iterator</span><span class="special">,</span> <span class="identifier">const_iterator</span> <span class="special">&gt;</span> 
  <a name="idm46550-bb"></a><span class="identifier">bounded_range</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> lower_key<span class="special">,</span> <span class="keyword">const</span> <span class="identifier">KeyType</span> <span class="special">&amp;</span> upper_key<span class="special">,</span> 
                <span class="identifier">KeyTypeKeyCompare</span> comp<span class="special">,</span> <span class="keyword">bool</span> left_closed<span class="special">,</span> <span class="keyword">bool</span> right_closed<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: <code class="computeroutput">lower_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to comp(nk, lower_key) if left_closed is true, with respect to !comp(lower_key, nk) otherwise.</p>
<p><code class="computeroutput">upper_key</code> is a value such that <code class="computeroutput">*this</code> is partitioned with respect to !comp(upper_key, nk) if right_closed is true, with respect to comp(nk, upper_key) otherwise.</p>
<p><code class="computeroutput">upper_key</code> shall not precede <code class="computeroutput">lower_key</code> according to comp [comp(upper_key, lower_key) shall be false]</p>
<p>If <code class="computeroutput">lower_key</code> is equivalent to <code class="computeroutput">upper_key</code> [!comp(upper_key, lower_key) &amp;&amp; !comp(lower_key, upper_key)] then ('left_closed' || 'right_closed') must be false.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns an a pair with the following criteria:</p>
<p>first = lower_bound(lower_key, comp) if left_closed, upper_bound(lower_key, comp) otherwise</p>
<p>second = upper_bound(upper_key, comp) if right_closed, lower_bound(upper_key, comp) otherwise</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic.</p>
<p><span class="bold"><strong>Throws</strong></span>: If <code class="computeroutput">comp</code> throws.</p>
<p><span class="bold"><strong>Note</strong></span>: This function can be more efficient than calling upper_bound and lower_bound for lower_key and upper_key.</p>
<p><span class="bold"><strong>Note</strong></span>: Experimental function, the interface might change in future releases. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm46592-bb"></a><span class="identifier">iterator_to</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">const_iterator</span> <a name="idm46605-bb"></a><span class="identifier">iterator_to</span><span class="special">(</span><span class="identifier">const_reference</span> value<span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid const_iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">pointer</span> <a name="idm46618-bb"></a><span class="identifier">unlink_leftmost_without_rebalance</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Unlinks the leftmost node from the container.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Average complexity is constant time.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Notes</strong></span>: This function breaks the container and the container can only be used for more unlink_leftmost_without_rebalance calls. This function is normally used to achieve a step by step controlled destruction of the container. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm46629-bb"></a><span class="identifier">replace_node</span><span class="special">(</span><span class="identifier">iterator</span> replace_this<span class="special">,</span> <span class="identifier">reference</span> with_this<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: replace_this must be a valid iterator of *this and with_this must not be inserted in any container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Replaces replace_this in its position in the container with with_this. The container does not need to be rebalanced.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This function will break container ordering invariants if with_this is not equivalent to *replace_this according to the ordering rules. This function is faster than erasing and inserting the node, since no rebalancing or comparison is needed. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm46646-bb"></a><span class="identifier">remove_node</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: removes "value" from the container.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic time.</p>
<p><span class="bold"><strong>Note</strong></span>: This static function is only usable with non-constant time size containers that have stateless comparison functors.</p>
<p>If the user calls this function with a constant time size container or stateful comparison functor a compilation error will be issued. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm46660-bb"></a><span class="identifier">rebalance</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Effects</strong></span>: Rebalances the tree.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm46669-bb"></a><span class="identifier">rebalance_subtree</span><span class="special">(</span><span class="identifier">iterator</span> root<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: old_root is a node of a tree.</p>
<p><span class="bold"><strong>Effects</strong></span>: Rebalances the subtree rooted at old_root.</p>
<p><span class="bold"><strong>Returns</strong></span>: The new root of the subtree.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the elements in the subtree. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">float</span> <a name="idm46684-bb"></a><span class="identifier">balance_factor</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Returns</strong></span>: The balance factor (alpha) used in this tree</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm46693-bb"></a><span class="identifier">balance_factor</span><span class="special">(</span><span class="keyword">float</span> new_alpha<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: new_alpha must be a value between 0.5 and 1.0</p>
<p><span class="bold"><strong>Effects</strong></span>: Establishes a new balance factor (alpha) and rebalances the tree if the new balance factor is stricter (less) than the old factor.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Linear to the elements in the subtree. </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-*********.33.4.4.7"></a><h3>
<a name="idm46780-bb"></a><code class="computeroutput">sgtree</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a name="idm46781-bb"></a><span class="identifier">container_from_end_iterator</span><span class="special">(</span><span class="identifier">iterator</span> end_iterator<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: end_iterator must be a valid end iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the end iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> 
<a name="idm46795-bb"></a><span class="identifier">container_from_end_iterator</span><span class="special">(</span><span class="identifier">const_iterator</span> end_iterator<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: end_iterator must be a valid end iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the end iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a name="idm46809-bb"></a><span class="identifier">container_from_iterator</span><span class="special">(</span><span class="identifier">iterator</span> it<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: it must be a valid iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">const</span> <a class="link" href="sgtree.html" title="Class template sgtree">sgtree</a> <span class="special">&amp;</span> <a name="idm46823-bb"></a><span class="identifier">container_from_iterator</span><span class="special">(</span><span class="identifier">const_iterator</span> it<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Precondition</strong></span>: it must be a valid iterator of the container.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns a const reference to the container associated to the iterator</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Logarithmic. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="identifier">iterator</span> <a name="idm46837-bb"></a><span class="identifier">s_iterator_to</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This static function is available only if the <span class="emphasis"><em>value traits</em></span> is stateless. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="identifier">const_iterator</span> <a name="idm46853-bb"></a><span class="identifier">s_iterator_to</span><span class="special">(</span><span class="identifier">const_reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value must be an lvalue and shall be in a set of appropriate type. Otherwise the behavior is undefined.</p>
<p><span class="bold"><strong>Effects</strong></span>: Returns: a valid iterator i belonging to the set that points to the value</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Note</strong></span>: This static function is available only if the <span class="emphasis"><em>value traits</em></span> is stateless. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">void</span> <a name="idm46869-bb"></a><span class="identifier">init_node</span><span class="special">(</span><span class="identifier">reference</span> value<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p><span class="bold"><strong>Requires</strong></span>: value shall not be in a container.</p>
<p><span class="bold"><strong>Effects</strong></span>: init_node puts the hook of a value in a well-known default state.</p>
<p><span class="bold"><strong>Throws</strong></span>: Nothing.</p>
<p><span class="bold"><strong>Complexity</strong></span>: Constant time.</p>
<p><span class="bold"><strong>Note</strong></span>: This function puts the hook in the well-known default state used by auto_unlink and safe hooks. </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2005 Olaf Krzikalla<br>Copyright © 2006-2015 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="make_sgtree.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../intrusive/reference.html#header.boost.intrusive.sgtree_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="sgtree_algorithms.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
