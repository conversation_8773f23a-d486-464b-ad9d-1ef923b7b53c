<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class status</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../mpi/reference.html#header.boost.mpi.status_hpp" title="Header &lt;boost/mpi/status.hpp&gt;">
<link rel="prev" href="skeleton_proxy.html" title="Struct template skeleton_proxy">
<link rel="next" href="timer.html" title="Class timer">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="skeleton_proxy.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../mpi/reference.html#header.boost.mpi.status_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="timer.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.mpi.status"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class status</span></h2>
<p>boost::mpi::status — Contains information about a message that has been or can be received. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../mpi/reference.html#header.boost.mpi.status_hpp" title="Header &lt;boost/mpi/status.hpp&gt;">boost/mpi/status.hpp</a>&gt;

</span>
<span class="keyword">class</span> <a class="link" href="status.html" title="Class status">status</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="status.html#boost.mpi.statusconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_5-bb"><span class="identifier">status</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_6-bb"><span class="identifier">status</span></a><span class="special">(</span><span class="identifier">MPI_Status</span> <span class="keyword">const</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4-bb">public member functions</a></span>
  <span class="keyword">int</span> <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_1-bb"><span class="identifier">source</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_2-bb"><span class="identifier">tag</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">int</span> <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_3-bb"><span class="identifier">error</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_4-bb"><span class="identifier">cancelled</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="identifier">optional</span><span class="special">&lt;</span> <span class="keyword">int</span> <span class="special">&gt;</span> <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_5-bb"><span class="identifier">count</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
  <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_6-bb"><span class="keyword">operator</span> <span class="identifier">MPI_Status</span> <span class="special">&amp;</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="status.html#id-1_3_25_7_24_2_1_1_4_7-bb"><span class="keyword">operator</span> <span class="keyword">const</span> <span class="identifier">MPI_Status</span> <span class="special">&amp;</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>

  <span class="comment">// public data members</span>
  <span class="keyword">mutable</span> <span class="keyword">int</span> <span class="identifier">m_count</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.25.4.4"></a><h2>Description</h2>
<p>This structure contains status information about messages that have been received (with <code class="computeroutput">communicator::recv</code>) or can be received (returned from <code class="computeroutput">communicator::probe</code> or <code class="computeroutput">communicator::iprobe</code>). It permits access to the source of the message, message tag, error code (rarely used), or the number of elements that have been transmitted. </p>
<div class="refsect2">
<a name="id-********.********"></a><h3>
<a name="boost.mpi.statusconstruct-copy-destruct"></a><code class="computeroutput">status</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><a name="id-1_3_25_7_24_2_1_1_5-bb"></a><span class="identifier">status</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><a name="id-1_3_25_7_24_2_1_1_6-bb"></a><span class="identifier">status</span><span class="special">(</span><span class="identifier">MPI_Status</span> <span class="keyword">const</span> <span class="special">&amp;</span> s<span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.25.4.4.4"></a><h3>
<a name="id-1_3_25_7_24_2_1_1_4-bb"></a><code class="computeroutput">status</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="id-1_3_25_7_24_2_1_1_4_1-bb"></a><span class="identifier">source</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Retrieve the source of the message. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="id-1_3_25_7_24_2_1_1_4_2-bb"></a><span class="identifier">tag</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Retrieve the message tag. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">int</span> <a name="id-1_3_25_7_24_2_1_1_4_3-bb"></a><span class="identifier">error</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Retrieve the error code. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="id-1_3_25_7_24_2_1_1_4_4-bb"></a><span class="identifier">cancelled</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Determine whether the communication associated with this object has been successfully cancelled. </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> <span class="identifier">optional</span><span class="special">&lt;</span> <span class="keyword">int</span> <span class="special">&gt;</span> <a name="id-1_3_25_7_24_2_1_1_4_5-bb"></a><span class="identifier">count</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>Determines the number of elements of type <code class="computeroutput">T</code> contained in the message. The type <code class="computeroutput">T</code> must have an associated data type, i.e., <code class="computeroutput">is_mpi_datatype&lt;T&gt;</code> must derive <code class="computeroutput">mpl::true_</code>. In cases where the type <code class="computeroutput">T</code> does not match the transmitted type, this routine will return an empty <code class="computeroutput">optional&lt;int&gt;</code>.</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>the number of <code class="computeroutput">T</code> elements in the message, if it can be determined. </p></td>
</tr></tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="id-1_3_25_7_24_2_1_1_4_6-bb"></a><span class="keyword">operator</span> <span class="identifier">MPI_Status</span> <span class="special">&amp;</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>References the underlying <code class="computeroutput">MPI_Status</code> </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="id-1_3_25_7_24_2_1_1_4_7-bb"></a><span class="keyword">operator</span> <span class="keyword">const</span> <span class="identifier">MPI_Status</span> <span class="special">&amp;</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>References the underlying <code class="computeroutput">MPI_Status</code> </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2005-2007 Douglas Gregor,
      Matthias Troyer, Trustees of Indiana University<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at &lt;ulink url="http://www.boost.org/LICENSE_1_0.txt"&gt;
        http://www.boost.org/LICENSE_1_0.txt &lt;/ulink&gt;)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="skeleton_proxy.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../mpi/reference.html#header.boost.mpi.status_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="timer.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
