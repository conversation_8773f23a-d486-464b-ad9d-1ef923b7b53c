<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template assign_base</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../../accumulators/reference.html#header.boost.accumulators.numeric.functional_hpp" title="Header &lt;boost/accumulators/numeric/functional.hpp&gt;">
<link rel="prev" href="assign.html" title="Struct template assign">
<link rel="next" href="complement.html" title="Struct template complement">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="assign.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../accumulators/reference.html#header.boost.accumulators.numeric.functional_hpp"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="complement.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.numeric.functional.assign_base"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template assign_base</span></h2>
<p>boost::numeric::functional::assign_base</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../accumulators/reference.html#header.boost.accumulators.numeric.functional_hpp" title="Header &lt;boost/accumulators/numeric/functional.hpp&gt;">boost/accumulators/numeric/functional.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> Left<span class="special">,</span> <span class="keyword">typename</span> Right<span class="special">,</span> <span class="keyword">typename</span> EnableIf <span class="special">=</span> <span class="keyword">void</span><span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="assign_base.html" title="Struct template assign_base">assign_base</a> <span class="special">:</span> <span class="keyword">public</span> std::binary_function&lt; Left, Right, typeof(lvalue&lt; Left &gt;()=lvalue&lt; Right &gt;())&gt;
<span class="special">{</span>

  <span class="comment">// <a class="link" href="assign_base.html#id-1_3_2_6_4_2_1_1_6_11_3-bb">public member functions</a></span>
  <span class="identifier">result_type</span> <a class="link" href="assign_base.html#id-1_3_2_6_4_2_1_1_6_11_3_1-bb"><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span></a><span class="special">(</span><span class="identifier">Left</span> <span class="special">&amp;</span><span class="special">,</span> <span class="identifier">Right</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*******.********"></a><h2>Description</h2>
<div class="refsect2">
<a name="id-*******.********.2"></a><h3>
<a name="id-1_3_2_6_4_2_1_1_6_11_3-bb"></a><code class="computeroutput">assign_base</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<pre class="literallayout"><span class="identifier">result_type</span> <a name="id-1_3_2_6_4_2_1_1_6_11_3_1-bb"></a><span class="keyword">operator</span><span class="special">(</span><span class="special">)</span><span class="special">(</span><span class="identifier">Left</span> <span class="special">&amp;</span> left<span class="special">,</span> <span class="identifier">Right</span> <span class="special">&amp;</span> right<span class="special">)</span> <span class="keyword">const</span><span class="special">;</span></pre>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>left = right </p></td>
</tr></tbody>
</table></div>
</li></ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2005, 2006 Eric Niebler<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="assign.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../accumulators/reference.html#header.boost.accumulators.numeric.functional_hpp"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="complement.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
