<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct template is_expr</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../proto/reference.html#header.boost.proto.traits_hpp" title="Header &lt;boost/proto/traits.hpp&gt;">
<link rel="prev" href="nary_expr/impl.html" title="Struct template impl">
<link rel="next" href="tag_of.html" title="Struct template tag_of">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="nary_expr/impl.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../proto/reference.html#header.boost.proto.traits_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="tag_of.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.proto.is_expr"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct template is_expr</span></h2>
<p>boost::proto::is_expr — A Boolean metafunction that indicates whether a given type <code class="computeroutput">T</code>
          is a Proto expression type.</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../proto/reference.html#header.boost.proto.traits_hpp" title="Header &lt;boost/proto/traits.hpp&gt;">boost/proto/traits.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">&gt;</span> 
<span class="keyword">struct</span> <a class="link" href="is_expr.html" title="Struct template is_expr">is_expr</a> <span class="special">:</span> <span class="keyword"></span> mpl::bool_&lt;<em class="replaceable"><code>true-or-false</code></em>&gt; <span class="special">{</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.41.70.4"></a><h2>Description</h2>
<p>
            If <code class="computeroutput">T</code> is an instantiation of 
            <code class="computeroutput"><a class="link" href="expr.html" title="Struct template expr">proto::expr&lt;&gt;</a></code> or
            <code class="computeroutput"><a class="link" href="basic_expr.html" title="Struct template basic_expr">proto::basic_expr&lt;&gt;</a></code> or is an extension
            (via <a class="link" href="extends.html" title="Struct template extends">proto::extends&lt;&gt;</a> or
            <a class="link" href="../../BOOST_PROTO_EXTENDS.html" title="Macro BOOST_PROTO_EXTENDS">BOOST_PROTO_EXTENDS</a>()) of such an instantiation, 
            <code class="computeroutput"><a class="link" href="is_expr.html" title="Struct template is_expr">proto::is_expr</a>&lt;T&gt;::value</code>
            is <code class="computeroutput">true</code>. 
            Otherwise, <code class="computeroutput"><a class="link" href="is_expr.html" title="Struct template is_expr">proto::is_expr</a>&lt;T&gt;::value</code>
            is <code class="computeroutput">false</code>.
          </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2008 Eric Niebler<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="nary_expr/impl.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../proto/reference.html#header.boost.proto.traits_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="tag_of.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
