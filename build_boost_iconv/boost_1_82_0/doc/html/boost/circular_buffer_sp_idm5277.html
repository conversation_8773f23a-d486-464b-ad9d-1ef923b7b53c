<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class template circular_buffer_space_optimized</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.space_optimized_hpp" title="Header &lt;boost/circular_buffer/space_optimized.hpp&gt;">
<link rel="prev" href="swap_idm5227.html" title="Function template swap">
<link rel="next" href="../circular_buffer/s14.html" title="Index">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="swap_idm5227.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.space_optimized_hpp"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../circular_buffer/s14.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.circular_buffer_sp_idm5277"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class template circular_buffer_space_optimized</span></h2>
<p>boost::circular_buffer_space_optimized — Space optimized circular buffer container adaptor. <code class="computeroutput">T</code> must be a copyable class or must have an noexcept move constructor and move assignment operator. </p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.space_optimized_hpp" title="Header &lt;boost/circular_buffer/space_optimized.hpp&gt;">boost/circular_buffer/space_optimized.hpp</a>&gt;

</span><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> T<span class="special">,</span> <span class="keyword">typename</span> Alloc<span class="special">&gt;</span> 
<span class="keyword">class</span> <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a> <span class="special">:</span>
  <span class="keyword">private</span> <span class="identifier">boost</span><span class="special">::</span><span class="identifier">circular_buffer</span><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span>
<span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="circular_buffer_sp_idm5277.html#boost.circular_buffer_sp_idm5277types">types</a></span>
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">value_type</span>             <a name="boost.circular_buffer_sp_idm5277.value_type"></a><span class="identifier">value_type</span><span class="special">;</span>            
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">pointer</span>                <a name="boost.circular_buffer_sp_idm5277.pointer"></a><span class="identifier">pointer</span><span class="special">;</span>               
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_pointer</span>          <a name="boost.circular_buffer_sp_idm5277.const_pointer"></a><span class="identifier">const_pointer</span><span class="special">;</span>         
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">reference</span>              <a name="boost.circular_buffer_sp_idm5277.reference"></a><span class="identifier">reference</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_reference</span>        <a name="boost.circular_buffer_sp_idm5277.const_reference"></a><span class="identifier">const_reference</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">size_type</span>              <a name="boost.circular_buffer_sp_idm5277.size_type"></a><span class="identifier">size_type</span><span class="special">;</span>             
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">difference_type</span>        <a name="boost.circular_buffer_sp_idm5277.difference_type"></a><span class="identifier">difference_type</span><span class="special">;</span>       
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">allocator_type</span>         <a name="boost.circular_buffer_sp_idm5277.allocator_type"></a><span class="identifier">allocator_type</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_iterator</span>         <a name="boost.circular_buffer_sp_idm5277.const_iterator"></a><span class="identifier">const_iterator</span><span class="special">;</span>        
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">iterator</span>               <a name="boost.circular_buffer_sp_idm5277.iterator"></a><span class="identifier">iterator</span><span class="special">;</span>              
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_reverse_iterator</span> <a name="boost.circular_buffer_sp_idm5277.const_reverse_iterator"></a><span class="identifier">const_reverse_iterator</span><span class="special">;</span>
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">reverse_iterator</span>       <a name="boost.circular_buffer_sp_idm5277.reverse_iterator"></a><span class="identifier">reverse_iterator</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">array_range</span>            <a name="boost.circular_buffer_sp_idm5277.array_range"></a><span class="identifier">array_range</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">const_array_range</span>      <a name="boost.circular_buffer_sp_idm5277.const_array_range"></a><span class="identifier">const_array_range</span><span class="special">;</span>     
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">param_value_type</span>       <a name="boost.circular_buffer_sp_idm5277.param_value_type"></a><span class="identifier">param_value_type</span><span class="special">;</span>      
  <span class="keyword">typedef</span> <a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span><span class="special">::</span><span class="identifier">rvalue_type</span>            <a name="boost.circular_buffer_sp_idm5277.rvalue_type"></a><span class="identifier">rvalue_type</span><span class="special">;</span>           
  <span class="keyword">typedef</span> <span class="identifier">cb_details</span><span class="special">::</span><span class="identifier">capacity_control</span><span class="special">&lt;</span> <span class="identifier">size_type</span> <span class="special">&gt;</span>           <a class="link" href="circular_buffer_sp_idm5277.html#boost.circular_buffer_sp_idm5277.capacity_type"><span class="identifier">capacity_type</span></a><span class="special">;</span>         

  <span class="comment">// <a class="link" href="circular_buffer_sp_idm5277.html#boost.circular_buffer_sp_idm5277construct-copy-destruct">construct/copy/destruct</a></span>
  <span class="keyword">explicit</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7357-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">explicit</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7377-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> 
                                           <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer_sp_idm5277.html#idm7405-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">,</span> 
                                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer_sp_idm5277.html#idm7443-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">,</span> 
                                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer_sp_idm5277.html#idm7490-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer_sp_idm5277.html#idm7518-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="circular_buffer_sp_idm5277.html#idm7544-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                                    <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <a class="link" href="circular_buffer_sp_idm5277.html#idm7587-bb"><span class="identifier">circular_buffer_space_optimized</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> 
                                    <span class="identifier">InputIterator</span><span class="special">,</span> 
                                    <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a class="link" href="circular_buffer_sp_idm5277.html#idm7642-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
  <a class="link" href="circular_buffer_sp_idm5277.html#idm7690-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="circular_buffer_sp_idm5277.html#idm5372-bb">public member functions</a></span>
  <span class="keyword">bool</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5373-bb"><span class="identifier">full</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">size_type</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5406-bb"><span class="identifier">reserve</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="identifier">capacity_type</span> <span class="special">&amp;</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5436-bb"><span class="identifier">capacity</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5468-bb"><span class="identifier">set_capacity</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">capacity_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5529-bb"><span class="identifier">resize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span> <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5590-bb"><span class="identifier">rset_capacity</span></a><span class="special">(</span><span class="keyword">const</span> <span class="identifier">capacity_type</span> <span class="special">&amp;</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5640-bb"><span class="identifier">rresize</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span> <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5701-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5754-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5816-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5873-bb"><span class="identifier">assign</span></a><span class="special">(</span><span class="identifier">capacity_type</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5944-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm5983-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6027-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6070-bb"><span class="identifier">push_back</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6112-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6156-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6201-bb"><span class="identifier">push_front</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6243-bb"><span class="identifier">pop_back</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6281-bb"><span class="identifier">pop_front</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6319-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6386-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6453-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6517-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6608-bb"><span class="identifier">insert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6712-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6779-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">rvalue_type</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6846-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm6910-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">size_type</span><span class="special">,</span> <span class="identifier">param_value_type</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
    <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7001-bb"><span class="identifier">rinsert</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">,</span> <span class="identifier">InputIterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7105-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7155-bb"><span class="identifier">erase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7208-bb"><span class="identifier">rerase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="identifier">iterator</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7263-bb"><span class="identifier">rerase</span></a><span class="special">(</span><span class="identifier">iterator</span><span class="special">,</span> <span class="identifier">iterator</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="circular_buffer_sp_idm5277.html#idm7321-bb"><span class="identifier">clear</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-********.4.3.4"></a><h2>Description</h2>
<div class="refsect2">
<a name="id-********.*******"></a><h3>
<a name="boost.circular_buffer_sp_idm5277types"></a><code class="computeroutput">circular_buffer_space_optimized</code> 
        public
       types</h3>
<div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem">
<p>
<span class="keyword">typedef</span> <span class="identifier">cb_details</span><span class="special">::</span><span class="identifier">capacity_control</span><span class="special">&lt;</span> <span class="identifier">size_type</span> <span class="special">&gt;</span> <a name="boost.circular_buffer_sp_idm5277.capacity_type"></a><span class="identifier">capacity_type</span><span class="special">;</span></p>
<p>Capacity controller of the space optimized circular buffer.</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p>capacity_control in details.hpp. </p>
<p>
</p>
<p>
<code class="computeroutput"> class capacity_control<br>
 {<br>
 size_type m_capacity; // Available capacity.<br>
 size_type m_min_capacity; // Minimum capacity.<br>
 public:<br>
 capacity_control(size_type capacity, size_type min_capacity = 0)<br>
 : m_capacity(capacity), m_min_capacity(min_capacity)<br>
 {};<br>
 size_type capacity() const { return m_capacity; }<br>
 size_type min_capacity() const { return m_min_capacity; }<br>
 operator size_type() const { return m_capacity; }<br>
 };<br>
 </code> </p>
<p>Always <code class="computeroutput">capacity &gt;= min_capacity</code>. </p>
<p>The <code class="computeroutput">capacity()</code> represents the capacity of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> and the <code class="computeroutput">min_capacity()</code> determines the minimal allocated size of its internal buffer. </p>
<p>The converting constructor of the <code class="computeroutput">capacity_control</code> allows implicit conversion from <code class="computeroutput">size_type</code>-like types which ensures compatibility of creating an instance of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> with other STL containers.</p>
<p>On the other hand the operator <code class="computeroutput">size_type()</code> provides implicit conversion to the <code class="computeroutput">size_type</code> which allows to treat the capacity of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> the same way as in the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>. </p>
</li></ol></div>
</div>
<div class="refsect2">
<a name="id-********.4.3.4.3"></a><h3>
<a name="boost.circular_buffer_sp_idm5277construct-copy-destruct"></a><code class="computeroutput">circular_buffer_space_optimized</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm7357-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Create an empty space optimized circular buffer with zero capacity. <p>


</p>
<p><b>Complexity. </b>Constant. </p>
<p>
</p>
<div class="warning"><table border="0" summary="Warning">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Warning]" src="../../../doc/src/images/warning.png"></td>
<th align="left">Warning</th>
</tr>
<tr><td align="left" valign="top"><p>Since Boost version 1.36 the behaviour of this constructor has changed. Now it creates a space optimized circular buffer with zero capacity. </p></td></tr>
</table></div>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity().capacity() == 0 &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; size() == 0</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">explicit</span> <a name="idm7377-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="identifier">capacity_type</span> capacity_ctrl<span class="special">,</span> 
                                         <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create an empty space optimized circular buffer with the specified capacity. <p>


</p>
<p><b>Complexity. </b>Constant. </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The capacity controller representing the maximum number of elements which can be stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> and the minimal allocated size of the internal buffer. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() == 0</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">capacity_ctrl.min_capacity()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm7405-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="identifier">capacity_type</span> capacity_ctrl<span class="special">,</span> 
                                <span class="identifier">param_value_type</span> item<span class="special">,</span> 
                                <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a full space optimized circular buffer with the specified capacity filled with <code class="computeroutput">capacity_ctrl.capacity()</code> copies of <code class="computeroutput">item</code>. <p>


</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">capacity_ctrl.capacity()</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The capacity controller representing the maximum number of elements which can be stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> and the minimal allocated size of the internal buffer. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; full() &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [capacity_ctrl.capacity() - 1] == item </code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">capacity_ctrl.capacity()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm7443-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="identifier">capacity_type</span> capacity_ctrl<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> 
                                <span class="identifier">param_value_type</span> item<span class="special">,</span> 
                                <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a space optimized circular buffer with the specified capacity filled with <code class="computeroutput">n</code> copies of <code class="computeroutput">item</code>. <p>



</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The capacity controller representing the maximum number of elements which can be stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> and the minimal allocated size of the internal buffer. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the created <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">capacity_ctrl.capacity() &gt;= n</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this)[n - 1] == item</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">max[n, capacity_ctrl.min_capacity()]</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm7490-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> cb<span class="special">)</span><span class="special">;</span></pre>The copy constructor. <p>Creates a copy of the specified <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. 


</p>
<p><b>Complexity. </b>Linear (in the size of <code class="computeroutput">cb</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p>The <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> to be copied. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">*this == cb</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">cb.size()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm7518-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> cb<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>The move constructor. <p>Move constructs a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> from <code class="computeroutput">cb</code>, leaving <code class="computeroutput">cb</code> empty. 



</p>
<p><b>Constant. </b></p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p><code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to 'steal' value from. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>C++ compiler with rvalue references support. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">cb.empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm7544-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
                                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a full space optimized circular buffer filled with a copy of the range. <p>



</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">std::distance(first, last)</code>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity().capacity() == std::distance(first, last) &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; full() &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">std::distance(first, last)</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept and <code class="computeroutput">InputIterator</code> is a move iterator. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <a name="idm7587-bb"></a><span class="identifier">circular_buffer_space_optimized</span><span class="special">(</span><span class="identifier">capacity_type</span> capacity_ctrl<span class="special">,</span> 
                                  <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">,</span> 
                                  <span class="keyword">const</span> <span class="identifier">allocator_type</span> <span class="special">&amp;</span> alloc <span class="special">=</span> <span class="identifier">allocator_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Create a space optimized circular buffer with the specified capacity (and the minimal guaranteed amount of allocated memory) filled with a copy of the range. <p>



</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(first, last)</code>; in <code class="computeroutput">min[capacity_ctrl.capacity(), std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">alloc</code></span></p></td>
<td><p>The allocator. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The capacity controller representing the maximum number of elements which can be stored in the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> and the minimal allocated size of the internal buffer. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== (last - capacity_ctrl.capacity()) &amp;&amp; (*this)[1] == *(last - capacity_ctrl.capacity() + 1) &amp;&amp; ... &amp;&amp; (*this)[capacity_ctrl.capacity() - 1] == *(last - 1)</code><br>
<br>
 If the number of items to be copied from the range <code class="computeroutput">[first, last)</code> is greater than the specified <code class="computeroutput">capacity_ctrl.capacity()</code> then only elements from the range <code class="computeroutput">[last - capacity_ctrl.capacity(), last)</code> will be copied.<br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">max[capacity_ctrl.min_capacity(), min[capacity_ctrl.capacity(), std::distance(first, last)]]</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
<a name="idm7642-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><span class="keyword">const</span> <a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> cb<span class="special">)</span><span class="special">;</span></pre>The assign operator. <p>Makes this <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> to become a copy of the specified <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. 


</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to this <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of <code class="computeroutput">cb</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p>The <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> to be copied. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">*this == cb</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">cb.size()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> 
<a name="idm7690-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;&amp;</span> cb<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Move assigns content of <code class="computeroutput">cb</code> to <code class="computeroutput">*this</code>, leaving <code class="computeroutput">cb</code> empty. <p>



</p>
<p><b>Complexity. </b>Constant. </p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p><code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code> to 'steal' value from. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>C++ compiler with rvalue references support. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">cb.empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-********.4.3.4.4"></a><h3>
<a name="idm5372-bb"></a><code class="computeroutput">circular_buffer_space_optimized</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm5373-bb"></a><span class="identifier">full</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Is the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> full? <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">empty()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">true</code> if the number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> equals the capacity of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code>; <code class="computeroutput">false</code> otherwise. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">size_type</span> <a name="idm5406-bb"></a><span class="identifier">reserve</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the maximum number of elements which can be inserted into the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> without overwriting any of already stored elements. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">capacity()</code>, <code class="computeroutput">size()</code>, <code class="computeroutput">max_size()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p><code class="computeroutput">capacity().capacity() - size()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="identifier">capacity_type</span> <span class="special">&amp;</span> <a name="idm5436-bb"></a><span class="identifier">capacity</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Get the capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. <p>

</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Does not invalidate any iterators. </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">reserve()</code>, <code class="computeroutput">size()</code>, <code class="computeroutput">max_size()</code>, <code class="computeroutput">set_capacity(const capacity_type&amp;)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>The capacity controller representing the maximum number of elements which can be stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> and the minimal allocated size of the internal buffer. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5468-bb"></a><span class="identifier">set_capacity</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">capacity_type</span> <span class="special">&amp;</span> capacity_ctrl<span class="special">)</span><span class="special">;</span></pre>Change the capacity (and the minimal guaranteed amount of allocated memory) of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[size(), capacity_ctrl.capacity()]</code>). </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>To explicitly clear the extra allocated memory use the <span class="bold"><strong>shrink-to-fit</strong></span> technique:<br>
<br>
 <code class="computeroutput">boost::circular_buffer_space_optimized&lt;int&gt; cb(1000);<br>
 ...<br>
 boost::circular_buffer_space_optimized&lt;int&gt;(cb).swap(cb);</code><br>
<br>
 For more information about the shrink-to-fit technique in STL see <a href="http://www.gotw.ca/gotw/054.htm" target="_top">http://www.gotw.ca/gotw/054.htm</a>. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rset_capacity(const capacity_type&amp;)</code>, <code class="computeroutput">resize(size_type, const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The new capacity controller. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() &lt;= capacity_ctrl.capacity()</code><br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is greater than the desired new capacity then number of <code class="computeroutput">[size() - capacity_ctrl.capacity()]</code> <span class="bold"><strong>last</strong></span> elements will be removed and the new size will be equal to <code class="computeroutput">capacity_ctrl.capacity()</code>.<br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is lower than the new capacity then the amount of allocated memory in the internal buffer may be accommodated as necessary but it will never drop below <code class="computeroutput">capacity_ctrl.min_capacity()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted, (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5529-bb"></a><span class="identifier">resize</span><span class="special">(</span><span class="identifier">size_type</span> new_size<span class="special">,</span> <span class="identifier">param_value_type</span> item <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Change the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the new size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rresize(size_type, const_reference)</code>, <code class="computeroutput">set_capacity(const capacity_type&amp;)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with in order to gain the requested size. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">new_size</code></span></p></td>
<td><p>The new size. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">size() == new_size &amp;&amp; capacity().capacity() &gt;= new_size</code><br>
<br>
 If the new size is greater than the current size, copies of <code class="computeroutput">item</code> will be inserted at the <span class="bold"><strong>back</strong></span> of the of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <code class="computeroutput">new_size</code>.<br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is greater than the desired new size then number of <code class="computeroutput">[size() - new_size]</code> <span class="bold"><strong>last</strong></span> elements will be removed. (The capacity will remain unchanged.)<br>
<br>
 The amount of allocated memory in the internal buffer may be accommodated as necessary. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5590-bb"></a><span class="identifier">rset_capacity</span><span class="special">(</span><span class="keyword">const</span> <span class="identifier">capacity_type</span> <span class="special">&amp;</span> capacity_ctrl<span class="special">)</span><span class="special">;</span></pre>Change the capacity (and the minimal guaranteed amount of allocated memory) of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Strong. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[size(), capacity_ctrl.capacity()]</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">set_capacity(const capacity_type&amp;)</code>, <code class="computeroutput">rresize(size_type, const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The new capacity controller. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() &lt;= capacity_ctrl</code><br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is greater than the desired new capacity then number of <code class="computeroutput">[size() - capacity_ctrl.capacity()]</code> <span class="bold"><strong>first</strong></span> elements will be removed and the new size will be equal to <code class="computeroutput">capacity_ctrl.capacity()</code>.<br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is lower than the new capacity then the amount of allocated memory in the internal buffer may be accommodated as necessary but it will never drop below <code class="computeroutput">capacity_ctrl.min_capacity()</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5640-bb"></a><span class="identifier">rresize</span><span class="special">(</span><span class="identifier">size_type</span> new_size<span class="special">,</span> <span class="identifier">param_value_type</span> item <span class="special">=</span> <span class="identifier">value_type</span><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>Change the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the new size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">resize(size_type, const_reference)</code>, <code class="computeroutput">rset_capacity(const capacity_type&amp;)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with in order to gain the requested size. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">new_size</code></span></p></td>
<td><p>The new size. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">size() == new_size &amp;&amp; capacity().capacity() &gt;= new_size</code><br>
<br>
 If the new size is greater than the current size, copies of <code class="computeroutput">item</code> will be inserted at the <span class="bold"><strong>front</strong></span> of the of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> in order to achieve the desired size. In the case the resulting size exceeds the current capacity the capacity will be set to <code class="computeroutput">new_size</code>.<br>
<br>
 If the current number of elements stored in the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is greater than the desired new size then number of <code class="computeroutput">[size() - new_size]</code> <span class="bold"><strong>first</strong></span> elements will be removed. (The capacity will remain unchanged.)<br>
<br>
 The amount of allocated memory in the internal buffer may be accommodated as necessary. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5701-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Assign <code class="computeroutput">n</code> items into the space optimized circular buffer. <p>The content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be removed and replaced with <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code>. 


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity().capacity() == n &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">n</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5754-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">capacity_type</span> capacity_ctrl<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Assign <code class="computeroutput">n</code> items into the space optimized circular buffer specifying the capacity. <p>The capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be set to the specified value and the content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be removed and replaced with <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code>. 



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">n</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The new capacity controller. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of elements the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be filled with. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">capacity_ctrl.capacity() &gt;= n</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() == n &amp;&amp; (*this)[0] == item &amp;&amp; (*this)[1] == item &amp;&amp; ... &amp;&amp; (*this) [n - 1] == item </code><br>
<br>
 The amount of allocated memory will be <code class="computeroutput">max[n, capacity_ctrl.min_capacity()]</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm5816-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Assign a copy of the range into the space optimized circular buffer. <p>The content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be removed and replaced with copies of elements from the specified range. 



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the <code class="computeroutput">std::distance(first, last)</code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity().capacity() == std::distance(first, last) &amp;&amp; capacity().min_capacity() == 0 &amp;&amp; size() == std::distance(first, last) &amp;&amp; (*this)[0]== *first &amp;&amp; (*this)[1] == *(first + 1) &amp;&amp; ... &amp;&amp; (*this)[std::distance(first, last) - 1] == *(last - 1)</code><br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">std::distance(first, last)</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept and <code class="computeroutput">InputIterator</code> is a move iterator. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm5873-bb"></a><span class="identifier">assign</span><span class="special">(</span><span class="identifier">capacity_type</span> capacity_ctrl<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> 
              <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Assign a copy of the range into the space optimized circular buffer specifying the capacity. <p>The capacity of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be set to the specified value and the content of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> will be removed and replaced with copies of elements from the specified range. 



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">std::distance(first, last)</code>; in <code class="computeroutput">min[capacity_ctrl.capacity(), std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">operator=</code>, <code class="computeroutput">assign(size_type, const_reference)</code>, <code class="computeroutput">assign(capacity_type, size_type, const_reference)</code>, <code class="computeroutput">assign(InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">capacity_ctrl</code></span></p></td>
<td><p>The new capacity controller. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be copied. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be copied. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>.<br>
 <code class="computeroutput">first</code> and <code class="computeroutput">last</code> have to meet the requirements of <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">capacity() == capacity_ctrl &amp;&amp; size() &lt;= std::distance(first, last) &amp;&amp; (*this)[0]== *(last - capacity) &amp;&amp; (*this)[1] == *(last - capacity + 1) &amp;&amp; ... &amp;&amp; (*this)[capacity - 1] == *(last - 1)</code><br>
<br>
 If the number of items to be copied from the range <code class="computeroutput">[first, last)</code> is greater than the specified <code class="computeroutput">capacity</code> then only elements from the range <code class="computeroutput">[last - capacity, last)</code> will be copied.<br>
<br>
 The amount of allocated memory in the internal buffer is <code class="computeroutput">max[std::distance(first, last), capacity_ctrl.min_capacity()]</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept and <code class="computeroutput">InputIterator</code> is a move iterator. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5944-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a><span class="special">&lt;</span> <span class="identifier">T</span><span class="special">,</span> <span class="identifier">Alloc</span> <span class="special">&gt;</span> <span class="special">&amp;</span> cb<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Swap the contents of two space-optimized circular-buffers. <p>


</p>
<p><b>Exception Safety. </b>No-throw. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators of both <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> containers. (On the other hand the iterators still point to the same elements but within another container. If you want to rely on this feature you have to turn the __debug_support off, otherwise an assertion will report an error if such invalidated iterator is used.) </p>
<p>
</p>
<p><b>Complexity. </b>Constant (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">swap(circular_buffer&lt;T, Alloc&gt;&amp;, circular_buffer&lt;T, Alloc&gt;&amp;)</code>, <code class="computeroutput">swap(circular_buffer_space_optimized&lt;T, Alloc&gt;&amp;, circular_buffer_space_optimized&lt;T, Alloc&gt;&amp;)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">cb</code></span></p></td>
<td><p>The <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> whose content will be swapped. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">this</code> contains elements of <code class="computeroutput">cb</code> and vice versa; the capacity and the amount of allocated memory in the internal buffer of <code class="computeroutput">this</code> equal to the capacity and the amount of allocated memory of <code class="computeroutput">cb</code> and vice versa. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>Nothing. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm5983-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the end of the space optimized circular buffer. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_front(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity().capacity() &gt; 0</code> then <code class="computeroutput">back() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the first element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6027-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the end of the space optimized circular buffer. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_front(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity().capacity() &gt; 0</code> then <code class="computeroutput">back() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the first element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6070-bb"></a><span class="identifier">push_back</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Insert a new element at the end of the space optimized circular buffer. <p>

</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_front(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity().capacity() &gt; 0</code> then <code class="computeroutput">back() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the first element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6112-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the beginning of the space optimized circular buffer. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity().capacity() &gt; 0</code> then <code class="computeroutput">front() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the last element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6156-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert a new element at the beginning of the space optimized circular buffer. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity().capacity() &gt; 0</code> then <code class="computeroutput">front() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the last element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6201-bb"></a><span class="identifier">push_front</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Insert a new element at the beginning of the space optimized circular buffer. <p>

</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">pop_back()</code>, <code class="computeroutput">pop_front()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>if <code class="computeroutput">capacity().capacity() &gt; 0</code> then <code class="computeroutput">front() == item</code><br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the last element will be removed. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6243-bb"></a><span class="identifier">pop_back</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Remove the last element from the space optimized circular buffer. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">pop_front()</code>, <code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">push_front(const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The last element is removed from the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code>.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6281-bb"></a><span class="identifier">pop_front</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Remove the first element from the space optimized circular buffer. <p>


</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">pop_back()</code>, <code class="computeroutput">push_back(const_reference)</code>, <code class="computeroutput">push_front(const_reference)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">!empty()</code> </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The first element is removed from the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code>.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm6319-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the first element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">begin()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">begin()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm6386-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the first element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">begin()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">begin()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm6453-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Insert an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the first element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">begin()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">begin()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6517-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code> at the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[capacity().capacity(), size() + n]</code>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting 5 elements at the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">insert(p, (size_t)5, 0);</code><br>
<br>
actually only 4 elements get inserted and elements <code class="computeroutput">1</code> and <code class="computeroutput">2</code> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|0|0|0|0|3|4|</code><br>
 <br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|0|0|0|0|0|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element whose copies will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of <code class="computeroutput">item</code>s the to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code>s will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The number of <code class="computeroutput">min[n, (pos - begin()) + reserve()]</code> elements will be inserted at the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[pos - begin(), max[0, n - reserve()]]</code> elements will be overwritten at the beginning of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.)<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm6608-bb"></a><span class="identifier">insert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Insert the range <code class="computeroutput">[first, last)</code> at the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">[size() + std::distance(first, last)]</code>; in <code class="computeroutput">min[capacity().capacity(), size() + std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting a range of elements at the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">int array[] = { 5, 6, 7, 8, 9 };</code><br>
<code class="computeroutput">insert(p, array, array + 5);</code><br>
<br>
 actually only elements <code class="computeroutput">6</code>, <code class="computeroutput">7</code>, <code class="computeroutput">8</code> and <code class="computeroutput">9</code> from the specified range get inserted and elements <code class="computeroutput">1</code> and <code class="computeroutput">2</code> are overwritten. This is due to the fact the insert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|6|7|8|9|3|4|</code><br>
<br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|5|6|7|8|9|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the range will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end.<br>
Valid range <code class="computeroutput">[first, last)</code> where <code class="computeroutput">first</code> and <code class="computeroutput">last</code> meet the requirements of an <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>Elements from the range <code class="computeroutput">[first + max[0, distance(first, last) - (pos - begin()) - reserve()], last)</code> will be inserted at the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[pos - begin(), max[0, distance(first, last) - reserve()]]</code> elements will be overwritten at the beginning of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.)<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm6712-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element before the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position before which the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the last element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">end()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">end()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm6779-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">rvalue_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert an element before the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position before which the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the last element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">end()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">end()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm6846-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Insert an element before the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position before which the <code class="computeroutput">item</code> will be inserted. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The <code class="computeroutput">item</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
 If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full, the last element will be overwritten. If the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> is full and the <code class="computeroutput">pos</code> points to <code class="computeroutput">end()</code>, then the <code class="computeroutput">item</code> will not be inserted. If the capacity is <code class="computeroutput">0</code>, nothing will be inserted.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the inserted element or <code class="computeroutput">end()</code> if the <code class="computeroutput">item</code> is not inserted. (See the <span class="emphasis"><em>Effect</em></span>.) </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T()</code> throws. Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws or nothing if <code class="computeroutput">T::T(T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm6910-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">size_type</span> n<span class="special">,</span> <span class="identifier">param_value_type</span> item<span class="special">)</span><span class="special">;</span></pre>Insert <code class="computeroutput">n</code> copies of the <code class="computeroutput">item</code> before the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">min[capacity().capacity(), size() + n]</code>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting 5 elements before the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">rinsert(p, (size_t)5, 0);</code><br>
<br>
actually only 4 elements get inserted and elements <code class="computeroutput">3</code> and <code class="computeroutput">4</code> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|1|2|0|0|0|0|</code><br>
 <br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|0|0|0|0|0|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, InputIterator, InputIterator)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">item</code></span></p></td>
<td><p>The element whose copies will be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">n</code></span></p></td>
<td><p>The number of <code class="computeroutput">item</code>s the to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the <code class="computeroutput">item</code>s will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The number of <code class="computeroutput">min[n, (end() - pos) + reserve()]</code> elements will be inserted before the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[end() - pos, max[0, n - reserve()]]</code> elements will be overwritten at the end of the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.)<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">template</span><span class="special">&lt;</span><span class="keyword">typename</span> InputIterator<span class="special">&gt;</span> 
  <span class="keyword">void</span> <a name="idm7001-bb"></a><span class="identifier">rinsert</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">,</span> <span class="identifier">InputIterator</span> first<span class="special">,</span> <span class="identifier">InputIterator</span> last<span class="special">)</span><span class="special">;</span></pre>Insert the range <code class="computeroutput">[first, last)</code> before the specified position. <p>



</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in <code class="computeroutput">[size() + std::distance(first, last)]</code>; in <code class="computeroutput">min[capacity().capacity(), size() + std::distance(first, last)]</code> if the <code class="computeroutput">InputIterator</code> is a <a href="https://www.boost.org/sgi/stl/RandomAccessIterator.html" target="_top">RandomAccessIterator</a>). </p>
<p>
</p>
<p><b>Example. </b>Consider a <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> with the capacity of 6 and the size of 4. Its internal buffer may look like the one below.<br>
<br>
 <code class="computeroutput">|1|2|3|4| | |</code><br>
 <code class="computeroutput">p ___^</code><br>
<br>
After inserting a range of elements before the position <code class="computeroutput">p</code>:<br>
<br>
 <code class="computeroutput">int array[] = { 5, 6, 7, 8, 9 };</code><br>
<code class="computeroutput">insert(p, array, array + 5);</code><br>
<br>
 actually only elements <code class="computeroutput">5</code>, <code class="computeroutput">6</code>, <code class="computeroutput">7</code> and <code class="computeroutput">8</code> from the specified range get inserted and elements <code class="computeroutput">3</code> and <code class="computeroutput">4</code> are overwritten. This is due to the fact the rinsert operation preserves the capacity. After insertion the internal buffer looks like this:<br>
<br>
<code class="computeroutput">|1|2|5|6|7|8|</code><br>
<br>
For comparison if the capacity would not be preserved the internal buffer would then result in <code class="computeroutput">|1|2|5|6|7|8|9|3|4|</code>. </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">rinsert(iterator, value_type)</code>, <code class="computeroutput">rinsert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, value_type)</code>, <code class="computeroutput">insert(iterator, size_type, value_type)</code>, <code class="computeroutput">insert(iterator, InputIterator, InputIterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be inserted. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator specifying the position where the range will be inserted. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> or its end.<br>
 Valid range <code class="computeroutput">[first, last)</code> where <code class="computeroutput">first</code> and <code class="computeroutput">last</code> meet the requirements of an <a href="https://www.boost.org/sgi/stl/InputIterator.html" target="_top">InputIterator</a>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>Elements from the range <code class="computeroutput">[first, last - max[0, distance(first, last) - (end() - pos) - reserve()])</code> will be inserted before the position <code class="computeroutput">pos</code>.<br>
The number of <code class="computeroutput">min[end() - pos, max[0, distance(first, last) - reserve()]]</code> elements will be overwritten at the end of the <code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code>.<br>
(See <span class="emphasis"><em>Example</em></span> for the explanation.)<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively increased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::T(const T&amp;)</code> throws. Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm7105-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Remove an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator pointing at the element to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> (but not an <code class="computeroutput">end()</code>). </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The element at the position <code class="computeroutput">pos</code> is removed.<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining beyond the removed element or <code class="computeroutput">end()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws or nothing if <code class="computeroutput">T::operator = (T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm7155-bb"></a><span class="identifier">erase</span><span class="special">(</span><span class="identifier">iterator</span> first<span class="special">,</span> <span class="identifier">iterator</span> last<span class="special">)</span><span class="special">;</span></pre>Erase the range <code class="computeroutput">[first, last)</code>. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be removed. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be removed. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The elements from the range <code class="computeroutput">[first, last)</code> are removed. (If <code class="computeroutput">first == last</code> nothing is removed.)<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining beyond the removed elements or <code class="computeroutput">end()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws or nothing if <code class="computeroutput">T::operator = (T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm7208-bb"></a><span class="identifier">rerase</span><span class="special">(</span><span class="identifier">iterator</span> pos<span class="special">)</span><span class="special">;</span></pre>Remove an element at the specified position. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>Basically there is no difference between <code class="computeroutput">erase(iterator)</code> and this method. It is implemented only for consistency with the base <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody><tr>
<td><p><span class="term"><code class="computeroutput">pos</code></span></p></td>
<td><p>An iterator pointing at the element to be removed. </p></td>
</tr></tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p><code class="computeroutput">pos</code> is a valid iterator pointing to the <code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code> (but not an <code class="computeroutput">end()</code>).<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The element at the position <code class="computeroutput">pos</code> is removed. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining in front of the removed element or <code class="computeroutput">begin()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws or nothing if <code class="computeroutput">T::operator = (T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">iterator</span> <a name="idm7263-bb"></a><span class="identifier">rerase</span><span class="special">(</span><span class="identifier">iterator</span> first<span class="special">,</span> <span class="identifier">iterator</span> last<span class="special">)</span><span class="special">;</span></pre>Erase the range <code class="computeroutput">[first, last)</code>. <p>




</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<div class="note"><table border="0" summary="Note">
<tr>
<td rowspan="2" align="center" valign="top" width="25"><img alt="[Note]" src="../../../doc/src/images/note.png"></td>
<th align="left">Note</th>
</tr>
<tr><td align="left" valign="top"><p>Basically there is no difference between <code class="computeroutput">erase(iterator, iterator)</code> and this method. It is implemented only for consistency with the base <code class="computeroutput">&lt;<code class="computeroutput"><a class="link" href="circular_buffer.html" title="Class template circular_buffer">circular_buffer</a></code></code>. </p></td></tr>
</table></div>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">clear()</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Parameters:</span></p></td>
<td><div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term"><code class="computeroutput">first</code></span></p></td>
<td><p>The beginning of the range to be removed. </p></td>
</tr>
<tr>
<td><p><span class="term"><code class="computeroutput">last</code></span></p></td>
<td><p>The end of the range to be removed. </p></td>
</tr>
</tbody>
</table></div></td>
</tr>
<tr>
<td><p><span class="term">Requires:</span></p></td>
<td><p>Valid range <code class="computeroutput">[first, last)</code>. </p></td>
</tr>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p>The elements from the range <code class="computeroutput">[first, last)</code> are removed. (If <code class="computeroutput">first == last</code> nothing is removed.)<br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Returns:</span></p></td>
<td><p>Iterator to the first element remaining in front of the removed elements or <code class="computeroutput">begin()</code> if no such element exists. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). Whatever <code class="computeroutput">T::operator = (const T&amp;)</code> throws or nothing if <code class="computeroutput">T::operator = (T&amp;&amp;)</code> is noexcept. </td>
</tr>
</tbody>
</table></div>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm7321-bb"></a><span class="identifier">clear</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>Remove all stored elements from the space optimized circular buffer. <p>

</p>
<p><b>Exception Safety. </b>Basic. </p>
<p>
</p>
<p><b>Iterator Invalidation. </b>Invalidates all iterators pointing to the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code> (except iterators equal to <code class="computeroutput">end()</code>). </p>
<p>
</p>
<p><b>Complexity. </b>Linear (in the size of the <code class="computeroutput"><code class="computeroutput"><a class="link" href="circular_buffer_sp_idm5277.html" title="Class template circular_buffer_space_optimized">circular_buffer_space_optimized</a></code></code>). </p>
<p>
</p>
<p><span class="bold"><strong>See Also:</strong></span></p>
<p><code class="computeroutput">~circular_buffer_space_optimized()</code>, <code class="computeroutput">erase(iterator)</code>, <code class="computeroutput">erase(iterator, iterator)</code>, <code class="computeroutput">rerase(iterator)</code>, <code class="computeroutput">rerase(iterator, iterator)</code> </p>
<p>
</p>
<p>
</p>
<div class="variablelist"><table border="0" class="variablelist compact">
<colgroup>
<col align="left" valign="top">
<col>
</colgroup>
<tbody>
<tr>
<td><p><span class="term">Postconditions:</span></p></td>
<td><p><code class="computeroutput">size() == 0</code><br>
<br>
 The amount of allocated memory in the internal buffer may be predictively decreased. </p></td>
</tr>
<tr>
<td><p><span class="term">Throws:</span></p></td>
<td>An allocation error if memory is exhausted (<code class="computeroutput">std::bad_alloc</code> if the standard allocator is used). </td>
</tr>
</tbody>
</table></div>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2013 Jan Gaspar<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="swap_idm5227.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../boost_circular_buffer_c___reference.html#header.boost.circular_buffer.space_optimized_hpp"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../circular_buffer/s14.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
