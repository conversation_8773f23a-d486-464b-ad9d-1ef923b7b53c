<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Class shared_memory_object</title>
<link rel="stylesheet" href="../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../interprocess/indexes_reference.html#header.boost.interprocess.shared_memory_object_hpp" title="Header &lt;boost/interprocess/shared_memory_object.hpp&gt;">
<link rel="prev" href="segment_manager_base.html" title="Class template segment_manager_base">
<link rel="next" href="deleter.html" title="Class template deleter">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../boost.png"></td>
<td align="center"><a href="../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="segment_manager_base.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../interprocess/indexes_reference.html#header.boost.interprocess.shared_memory_object_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="deleter.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.interprocess.shared_memory_object"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Class shared_memory_object</span></h2>
<p>boost::interprocess::shared_memory_object</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../interprocess/indexes_reference.html#header.boost.interprocess.shared_memory_object_hpp" title="Header &lt;boost/interprocess/shared_memory_object.hpp&gt;">boost/interprocess/shared_memory_object.hpp</a>&gt;

</span>
<span class="keyword">class</span> <a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">{</span>
<span class="keyword">public</span><span class="special">:</span>
  <span class="comment">// <a class="link" href="shared_memory_object.html#boost.interprocess.shared_memory_objectconstruct-copy-destruct">construct/copy/destruct</a></span>
  <a class="link" href="shared_memory_object.html#idm28822-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28825-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="create_only_t.html" title="Struct create_only_t">create_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">mode_t</span><span class="special">,</span> 
                       <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28840-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="open_or_create_t.html" title="Struct open_or_create_t">open_or_create_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">mode_t</span><span class="special">,</span> 
                       <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28855-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="open_only_t.html" title="Struct open_only_t">open_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">mode_t</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28865-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="create_only_t.html" title="Struct create_only_t">create_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">mode_t</span><span class="special">,</span> 
                       <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28881-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="open_or_create_t.html" title="Struct open_or_create_t">open_or_create_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">mode_t</span><span class="special">,</span> 
                       <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28897-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="open_only_t.html" title="Struct open_only_t">open_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span><span class="special">,</span> <span class="identifier">mode_t</span><span class="special">)</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28908-bb"><span class="identifier">shared_memory_object</span></a><span class="special">(</span><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;</span> <a class="link" href="shared_memory_object.html#idm28914-bb"><span class="keyword">operator</span><span class="special">=</span></a><span class="special">(</span><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <a class="link" href="shared_memory_object.html#idm28922-bb"><span class="special">~</span><span class="identifier">shared_memory_object</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="shared_memory_object.html#idm28795-bb">public member functions</a></span>
  <span class="keyword">void</span> <a class="link" href="shared_memory_object.html#idm28796-bb"><span class="identifier">swap</span></a><span class="special">(</span><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">void</span> <a class="link" href="shared_memory_object.html#idm28802-bb"><span class="identifier">truncate</span></a><span class="special">(</span><span class="identifier">offset_t</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a class="link" href="../container/basic_string.html#idm28807-bb"><span class="identifier">get_name</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="keyword">bool</span> <a class="link" href="shared_memory_object.html#idm28810-bb"><span class="identifier">get_size</span></a><span class="special">(</span><span class="identifier">offset_t</span> <span class="special">&amp;</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">mode_t</span> <a class="link" href="shared_memory_object.html#idm28816-bb"><span class="identifier">get_mode</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>
  <span class="identifier">mapping_handle_t</span> <a class="link" href="shared_memory_object.html#idm28819-bb"><span class="identifier">get_mapping_handle</span></a><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span>

  <span class="comment">// <a class="link" href="shared_memory_object.html#idm28925-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="keyword">bool</span> <a class="link" href="shared_memory_object.html#idm28926-bb"><span class="identifier">remove</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">bool</span> <a class="link" href="shared_memory_object.html#idm28932-bb"><span class="identifier">remove</span></a><span class="special">(</span><span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.********"></a><h2>Description</h2>
<p>A class that wraps a shared memory mapping that can be used to create mapped regions from the mapped files </p>
<div class="refsect2">
<a name="id-*********.********.3"></a><h3>
<a name="boost.interprocess.shared_memory_objectconstruct-copy-destruct"></a><code class="computeroutput">shared_memory_object</code> 
        public
       construct/copy/destruct</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><a name="idm28822-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Default constructor. Represents an empty <code class="computeroutput"><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a></code>. </li>
<li class="listitem">
<pre class="literallayout"><a name="idm28825-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="create_only_t.html" title="Struct create_only_t">create_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> name<span class="special">,</span> <span class="identifier">mode_t</span> mode<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> perm <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Creates a shared memory object with name "name" and mode "mode", with the access mode "mode" If the file previously exists, throws an error. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28840-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="open_or_create_t.html" title="Struct open_or_create_t">open_or_create_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> name<span class="special">,</span> <span class="identifier">mode_t</span> mode<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> perm <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Tries to create a shared memory object with name "name" and mode "mode", with the access mode "mode". If the file previously exists, it tries to open it with mode "mode". Otherwise throws an error. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28855-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="open_only_t.html" title="Struct open_only_t">open_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> name<span class="special">,</span> <span class="identifier">mode_t</span> mode<span class="special">)</span><span class="special">;</span></pre>
<p>Tries to open a shared memory object with name "name", with the access mode "mode". If the file does not previously exist, it throws an error. </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28865-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="create_only_t.html" title="Struct create_only_t">create_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span> name<span class="special">,</span> <span class="identifier">mode_t</span> mode<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> perm <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Creates a shared memory object with name "name" and mode "mode", with the access mode "mode" If the file previously exists, throws an error.</p>
<p>Note: This function is only available on operating systems with native wchar_t APIs (e.g. Windows). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28881-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="open_or_create_t.html" title="Struct open_or_create_t">open_or_create_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span> name<span class="special">,</span> <span class="identifier">mode_t</span> mode<span class="special">,</span> 
                     <span class="keyword">const</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a> <span class="special">&amp;</span> perm <span class="special">=</span> <a class="link" href="permissions.html" title="Class permissions">permissions</a><span class="special">(</span><span class="special">)</span><span class="special">)</span><span class="special">;</span></pre>
<p>Tries to create a shared memory object with name "name" and mode "mode", with the access mode "mode". If the file previously exists, it tries to open it with mode "mode". Otherwise throws an error.</p>
<p>Note: This function is only available on operating systems with native wchar_t APIs (e.g. Windows). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28897-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="open_only_t.html" title="Struct open_only_t">open_only_t</a><span class="special">,</span> <span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span> name<span class="special">,</span> <span class="identifier">mode_t</span> mode<span class="special">)</span><span class="special">;</span></pre>
<p>Tries to open a shared memory object with name "name", with the access mode "mode". If the file does not previously exist, it throws an error.</p>
<p>Note: This function is only available on operating systems with native wchar_t APIs (e.g. Windows). </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28908-bb"></a><span class="identifier">shared_memory_object</span><span class="special">(</span><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;&amp;</span> moved<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Moves the ownership of "moved"'s shared memory object to *this. After the call, "moved" does not represent any shared memory object. Does not throw </p>
</li>
<li class="listitem">
<pre class="literallayout"><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;</span> <a name="idm28914-bb"></a><span class="keyword">operator</span><span class="special">=</span><span class="special">(</span><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;&amp;</span> moved<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Moves the ownership of "moved"'s shared memory to *this. After the call, "moved" does not represent any shared memory. Does not throw </p>
</li>
<li class="listitem">
<pre class="literallayout"><a name="idm28922-bb"></a><span class="special">~</span><span class="identifier">shared_memory_object</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre>
<p>Destroys *this and indicates that the calling process is finished using the resource. All mapped regions are still valid after destruction. The destructor function will deallocate any system resources allocated by the system for use by this process for this resource. The resource can still be opened again calling the open constructor overload. To erase the resource from the system use remove(). </p>
</li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-*********.********.4"></a><h3>
<a name="idm28795-bb"></a><code class="computeroutput">shared_memory_object</code> public member functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28796-bb"></a><span class="identifier">swap</span><span class="special">(</span><a class="link" href="shared_memory_object.html" title="Class shared_memory_object">shared_memory_object</a> <span class="special">&amp;</span> moved<span class="special">)</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Swaps the shared_memory_objects. Does not throw. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">void</span> <a name="idm28802-bb"></a><span class="identifier">truncate</span><span class="special">(</span><span class="identifier">offset_t</span> length<span class="special">)</span><span class="special">;</span></pre>Sets the size of the shared memory mapping. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a name="idm28807-bb"></a><span class="identifier">get_name</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Returns the name of the shared memory object. </li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">bool</span> <a name="idm28810-bb"></a><span class="identifier">get_size</span><span class="special">(</span><span class="identifier">offset_t</span> <span class="special">&amp;</span> size<span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>
<p>Returns true if the size of the shared memory object can be obtained and writes the size in the passed reference </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">mode_t</span> <a name="idm28816-bb"></a><span class="identifier">get_mode</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Returns access mode. </li>
<li class="listitem">
<pre class="literallayout"><span class="identifier">mapping_handle_t</span> <a name="idm28819-bb"></a><span class="identifier">get_mapping_handle</span><span class="special">(</span><span class="special">)</span> <span class="keyword">const</span> <span class="keyword">noexcept</span><span class="special">;</span></pre>Returns mapping handle. Never throws. </li>
</ol></div>
</div>
<div class="refsect2">
<a name="id-*********.********.5"></a><h3>
<a name="idm28925-bb"></a><code class="computeroutput">shared_memory_object</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">bool</span> <a name="idm28926-bb"></a><span class="identifier">remove</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> name<span class="special">)</span><span class="special">;</span></pre>
<p>Erases a shared memory object from the system. Returns false on error. Never throws </p>
</li>
<li class="listitem">
<pre class="literallayout"><span class="keyword">static</span> <span class="keyword">bool</span> <a name="idm28932-bb"></a><span class="identifier">remove</span><span class="special">(</span><span class="keyword">const</span> <span class="keyword">wchar_t</span> <span class="special">*</span> name<span class="special">)</span><span class="special">;</span></pre>
<p>Erases a shared memory object from the system. Returns false on error. Never throws</p>
<p>Note: This function is only available on operating systems with native wchar_t APIs (e.g. Windows). </p>
</li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2005-2015 Ion Gaztanaga<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="segment_manager_base.html"><img src="../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../interprocess/indexes_reference.html#header.boost.interprocess.shared_memory_object_hpp"><img src="../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../index.html"><img src="../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="deleter.html"><img src="../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
