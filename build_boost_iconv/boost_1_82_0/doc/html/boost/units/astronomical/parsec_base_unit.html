<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Struct parsec_base_unit</title>
<link rel="stylesheet" href="../../../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../../../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../../../boost_units/Reference.html#header.boost.units.base_units.astronomical.parsec_hpp" title="Header &lt;boost/units/base_units/astronomical/parsec.hpp&gt;">
<link rel="prev" href="../bas_1_3_43_10_10_3_7_1_1_1.html" title="Struct base_unit_info&lt;astronomical::light_year_base_unit&gt;">
<link rel="next" href="../cgs/gram_base_unit.html" title="Struct gram_base_unit">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../../../boost.png"></td>
<td align="center"><a href="../../../../../index.html">Home</a></td>
<td align="center"><a href="../../../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../bas_1_3_43_10_10_3_7_1_1_1.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../boost_units/Reference.html#header.boost.units.base_units.astronomical.parsec_hpp"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../cgs/gram_base_unit.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.units.astronomical.parsec_base_unit"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Struct parsec_base_unit</span></h2>
<p>boost::units::astronomical::parsec_base_unit</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../../../boost_units/Reference.html#header.boost.units.base_units.astronomical.parsec_hpp" title="Header &lt;boost/units/base_units/astronomical/parsec.hpp&gt;">boost/units/base_units/astronomical/parsec.hpp</a>&gt;

</span>
<span class="keyword">struct</span> <a class="link" href="parsec_base_unit.html" title="Struct parsec_base_unit">parsec_base_unit</a> <span class="special">:</span> <span class="keyword">public</span> boost::units::base_unit&lt; parsec_base_unit, boost::units::si::meter_base_unit ::dimension_type, -206 &gt;
<span class="special">{</span>

  <span class="comment">// <a class="link" href="parsec_base_unit.html#id-1_3_43_10_10_3_8_1_1_1_1_2-bb">public static functions</a></span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a class="link" href="parsec_base_unit.html#id-1_3_43_10_10_3_8_1_1_1_1_2_1-bb"><span class="identifier">name</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
  <span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a class="link" href="parsec_base_unit.html#id-1_3_43_10_10_3_8_1_1_1_1_2_2-bb"><span class="identifier">symbol</span></a><span class="special">(</span><span class="special">)</span><span class="special">;</span>
<span class="special">}</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.********.4"></a><h2>Description</h2>
<div class="refsect2">
<a name="id-*********.********.4.2"></a><h3>
<a name="id-1_3_43_10_10_3_8_1_1_1_1_2-bb"></a><code class="computeroutput">parsec_base_unit</code> public static functions</h3>
<div class="orderedlist"><ol class="orderedlist" type="1">
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a name="id-1_3_43_10_10_3_8_1_1_1_1_2_1-bb"></a><span class="identifier">name</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
<li class="listitem"><pre class="literallayout"><span class="keyword">static</span> <span class="keyword">const</span> <span class="keyword">char</span> <span class="special">*</span> <a name="id-1_3_43_10_10_3_8_1_1_1_1_2_2-bb"></a><span class="identifier">symbol</span><span class="special">(</span><span class="special">)</span><span class="special">;</span></pre></li>
</ol></div>
</div>
</div>
</div>
<div class="copyright-footer">Copyright © 2003-2008 Matthias Christian Schabel<br>Copyright © 2007-2010 Steven
      Watanabe<p>
        Distributed under the Boost Software License, Version 1.0. (See accompanying
        file LICENSE_1_0.txt or copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="../bas_1_3_43_10_10_3_7_1_1_1.html"><img src="../../../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../../../boost_units/Reference.html#header.boost.units.base_units.astronomical.parsec_hpp"><img src="../../../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../../../index.html"><img src="../../../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="../cgs/gram_base_unit.html"><img src="../../../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
