<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Type definition crc_ccitt_true_t</title>
<link rel="stylesheet" href="../../../doc/src/boostbook.css" type="text/css">
<meta name="generator" content="DocBook XSL Stylesheets V1.79.1">
<link rel="home" href="../index.html" title="The Boost C++ Libraries BoostBook Documentation Subset">
<link rel="up" href="../crc/reference.html#header.boost.crc_hpp" title="Header &lt;boost/crc.hpp&gt;">
<link rel="prev" href="crc_optimal.html" title="Class template crc_optimal">
<link rel="next" href="crc_xmodem_type.html" title="Type definition crc_xmodem_type">
<meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
<table cellpadding="2" width="100%"><tr>
<td valign="top"><img alt="Boost C++ Libraries" width="277" height="86" src="../../../boost.png"></td>
<td align="center"><a href="../../../index.html">Home</a></td>
<td align="center"><a href="../../../libs/libraries.htm">Libraries</a></td>
<td align="center"><a href="http://www.boost.org/users/people.html">People</a></td>
<td align="center"><a href="http://www.boost.org/users/faq.html">FAQ</a></td>
<td align="center"><a href="../../../more/index.htm">More</a></td>
</tr></table>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="crc_optimal.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../crc/reference.html#header.boost.crc_hpp"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="crc_xmodem_type.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
<div class="refentry">
<a name="boost.crc_ccitt_true_t"></a><div class="titlepage"></div>
<div class="refnamediv">
<h2><span class="refentrytitle">Type definition crc_ccitt_true_t</span></h2>
<p>crc_ccitt_true_t</p>
</div>
<h2 class="refsynopsisdiv-title">Synopsis</h2>
<div class="refsynopsisdiv"><pre class="synopsis"><span class="comment">// In header: &lt;<a class="link" href="../crc/reference.html#header.boost.crc_hpp" title="Header &lt;boost/crc.hpp&gt;">boost/crc.hpp</a>&gt;

</span>
<span class="keyword">typedef</span> <a class="link" href="crc_optimal.html" title="Class template crc_optimal">crc_optimal</a><span class="special">&lt;</span> <span class="number">16</span><span class="special">,</span> <span class="number">0x1021</span><span class="special">,</span> <span class="number">0</span><span class="special">,</span> <span class="number">0</span><span class="special">,</span> <span class="keyword">true</span><span class="special">,</span> <span class="keyword">true</span> <span class="special">&gt;</span> <span class="identifier">crc_ccitt_true_t</span><span class="special">;</span></pre></div>
<div class="refsect1">
<a name="id-*********.2.9.4"></a><h2>Description</h2>
<p>Computation type for the actual KERMIT|CRC-16/CCITT|CRC-16/CCITT-TRUE|CRC-CCITT standard </p>
</div>
</div>
<div class="copyright-footer">Copyright © 2001, 2003, 2012 Daryle Walker<p>
        Distributed under the Boost Software License, Version 1.0. (See the accompanying
        file LICENSE_1_0.txt or a copy at <a href="http://www.boost.org/LICENSE_1_0.txt" target="_top">http://www.boost.org/LICENSE_1_0.txt</a>)
      </p>
</div>
<hr>
<div class="spirit-nav">
<a accesskey="p" href="crc_optimal.html"><img src="../../../doc/src/images/prev.png" alt="Prev"></a><a accesskey="u" href="../crc/reference.html#header.boost.crc_hpp"><img src="../../../doc/src/images/up.png" alt="Up"></a><a accesskey="h" href="../index.html"><img src="../../../doc/src/images/home.png" alt="Home"></a><a accesskey="n" href="crc_xmodem_type.html"><img src="../../../doc/src/images/next.png" alt="Next"></a>
</div>
</body>
</html>
