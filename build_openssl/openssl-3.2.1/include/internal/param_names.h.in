/*
 * {- join("\n * ", @autowarntext) -}
 *
 * Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */
{-
use OpenSSL::paramnames qw(generate_internal_macros);
-}

int ossl_param_find_pidx(const char *s);

/* Parameter name definitions - generated by util/perl/OpenSSL/paramnames.pm */
{- generate_internal_macros(); -}
