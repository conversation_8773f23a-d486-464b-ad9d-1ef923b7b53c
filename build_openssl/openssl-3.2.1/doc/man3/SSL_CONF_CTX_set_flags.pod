=pod

=head1 NAME

SSL_CONF_CTX_set_flags, SSL_CONF_CTX_clear_flags - Set or clear SSL configuration context flags

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 unsigned int SSL_CONF_CTX_set_flags(SSL_CONF_CTX *cctx, unsigned int flags);
 unsigned int SSL_CONF_CTX_clear_flags(SSL_CONF_CTX *cctx, unsigned int flags);

=head1 DESCRIPTION

The function SSL_CONF_CTX_set_flags() sets B<flags> in the context B<cctx>.

The function SSL_CONF_CTX_clear_flags() clears B<flags> in the context B<cctx>.

=head1 NOTES

The flags set affect how subsequent calls to SSL_CONF_cmd() or
SSL_CONF_argv() behave.

Currently the following B<flags> values are recognised:

=over 4

=item SSL_CONF_FLAG_CMDLINE, SSL_CONF_FLAG_FILE

recognise options intended for command line or configuration file use. At
least one of these flags must be set.

=item SSL_CONF_FLAG_CLIENT, SSL_CONF_FLAG_SERVER

recognise options intended for use in SSL/TLS clients or servers. One or
both of these flags must be set.

=item SSL_CONF_FLAG_CERTIFICATE

recognise certificate and private key options.

=item SSL_CONF_FLAG_REQUIRE_PRIVATE

If this option is set then if a private key is not specified for a certificate
it will attempt to load a private key from the certificate file when
SSL_CONF_CTX_finish() is called. If a key cannot be loaded from the certificate
file an error occurs.

=item SSL_CONF_FLAG_SHOW_ERRORS

indicate errors relating to unrecognised options or missing arguments in
the error queue. If this option isn't set such errors are only reflected
in the return values of SSL_CONF_set_cmd() or SSL_CONF_set_argv()

=back

=head1 RETURN VALUES

SSL_CONF_CTX_set_flags() and SSL_CONF_CTX_clear_flags() returns the new flags
value after setting or clearing flags.

=head1 SEE ALSO

L<ssl(7)>,
L<SSL_CONF_CTX_new(3)>,
L<SSL_CONF_CTX_set_ssl_ctx(3)>,
L<SSL_CONF_CTX_set1_prefix(3)>,
L<SSL_CONF_cmd(3)>,
L<SSL_CONF_cmd_argv(3)>

=head1 HISTORY

These functions were added in OpenSSL 1.0.2.

=head1 COPYRIGHT

Copyright 2012-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
